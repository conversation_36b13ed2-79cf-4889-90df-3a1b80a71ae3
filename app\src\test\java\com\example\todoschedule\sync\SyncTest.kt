package com.example.todoschedule.sync

import com.example.todoschedule.data.database.entity.CourseEntity
import com.example.todoschedule.data.repository.SyncRepository
import com.example.todoschedule.data.sync.CrdtKeyResolver
import com.example.todoschedule.data.sync.DeviceIdManager
import com.example.todoschedule.data.sync.SyncConstants
import com.example.todoschedule.data.sync.SyncManager
import com.example.todoschedule.data.sync.adapter.CourseAdapter
import com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry
import com.example.todoschedule.data.sync.dto.SyncMessageDto
import com.example.todoschedule.data.sync.dto.TimestampDto
import com.tap.hlc.HybridLogicalClock
import com.tap.hlc.NodeID
import com.tap.hlc.Timestamp
import kotlinx.coroutines.runBlocking
import kotlinx.datetime.Clock
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito.`when`
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import java.util.UUID
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull

/**
 * 同步和冲突解决测试类
 */
class SyncTest {
    
    // 模拟依赖
    private lateinit var syncRepository: SyncRepository
    private lateinit var deviceIdManager: DeviceIdManager
    private lateinit var crdtKeyResolver: CrdtKeyResolver
    private lateinit var synkAdapterRegistry: SynkAdapterRegistry
    private lateinit var courseAdapter: CourseAdapter
    
    // 被测系统
    private lateinit var syncManager: SyncManager
    
    @Before
    fun setup() {
        // 初始化模拟依赖
        syncRepository = mock(SyncRepository::class.java)
        deviceIdManager = mock(DeviceIdManager::class.java)
        crdtKeyResolver = mock(CrdtKeyResolver::class.java)
        synkAdapterRegistry = mock(SynkAdapterRegistry::class.java)
        courseAdapter = CourseAdapter()
        
        // 设置模拟行为
        runBlocking {
            `when`(deviceIdManager.getOrCreateDeviceId()).thenReturn("device-1")
            `when`(synkAdapterRegistry.getAdapter(SyncConstants.EntityType.COURSE.value)).thenReturn(courseAdapter)
        }
        
        // 创建被测系统
        syncManager = SyncManager(
            syncRepository = syncRepository,
            syncService = mock(), // 暂时不需要测试SyncService
            deviceIdManager = deviceIdManager,
            synkAdapterRegistry = synkAdapterRegistry,
            crdtKeyResolver = crdtKeyResolver
        )
    }
    
    /**
     * 测试创建同步消息
     */
    @Test
    fun testCreateSyncMessage() = runBlocking {
        // 准备测试数据
        val crdtKey = UUID.randomUUID().toString()
        val entityType = SyncConstants.EntityType.COURSE
        val operationType = SyncConstants.OperationType.ADD
        val userId = 1
        val payload = """{"name":"数学","location":"A101"}"""
        
        // 执行被测方法
        val message = syncManager.createSyncMessage(
            crdtKey = crdtKey,
            entityType = entityType,
            operationType = operationType,
            userId = userId,
            payload = payload
        )
        
        // 验证结果
        assertNotNull(message)
        assertEquals(crdtKey, message.crdtKey)
        assertEquals(entityType.value, message.entityType)
        assertEquals(operationType, message.operationType)
        assertEquals(userId, message.userId)
        assertEquals(payload, message.payload)
        assertEquals("device-1", message.deviceId)
    }
    
    /**
     * 测试处理接收到的同步消息
     */
    @Test
    fun testProcessReceivedMessage() = runBlocking {
        // 准备测试数据
        val courseId = 1
        val tableCrdtKey = UUID.randomUUID().toString()
        val courseCrdtKey = UUID.randomUUID().toString()
        
        // 模拟课程数据
        val course = CourseEntity(
            id = courseId,
            tableId = 1,
            courseName = "数学",
            location = "A101",
            crdtKey = courseCrdtKey,
            tableCrdtKey = tableCrdtKey,
            updateTimestamp = System.currentTimeMillis()
        )
        
        // 创建时间戳
        val currentTime = Timestamp.now(Clock.System)
        val hlcTimestamp = HybridLogicalClock(
            timestamp = currentTime,
            node = NodeID("device-2"),
            counter = 0
        )
        
        // 创建接收到的消息
        val message = SyncMessageDto(
            id = UUID.randomUUID().toString(),
            crdtKey = courseCrdtKey,
            entityType = SyncConstants.EntityType.COURSE.value,
            operationType = SyncConstants.OperationType.UPDATE,
            deviceId = "device-2",
            timestamp = TimestampDto(
                wallClock = hlcTimestamp.timestamp.epochMillis,
                logical = hlcTimestamp.counter.toLong(),
                nodeId = hlcTimestamp.node.identifier
            ),
            payload = """{"id":1,"tableId":1,"courseName":"高等数学","location":"A101","crdtKey":"$courseCrdtKey","tableCrdtKey":"$tableCrdtKey"}""",
            userId = 1
        )
        
        // 模拟CrdtKeyResolver解析关系
        runBlocking {
            `when`(crdtKeyResolver.resolveCourseReferences(org.mockito.kotlin.any())).thenReturn(course.copy(courseName = "高等数学"))
        }
        
        // 执行被测方法
        syncManager.processReceivedMessage(message)
        
        // 验证存储库方法被调用
        runBlocking {
            verify(syncRepository).getCourseDao()
        }
    }
    
    /**
     * 测试冲突解决 - 后更改优先
     */
    @Test
    fun testConflictResolution_LaterChangeWins() = runBlocking {
        // 准备测试数据
        val crdtKey = UUID.randomUUID().toString()
        
        // 早期版本，时间戳较早
        val course1 = CourseEntity(
            id = 1,
            tableId = 1,
            courseName = "数学",
            location = "A101",
            crdtKey = crdtKey,
            tableCrdtKey = "table-1",
            updateTimestamp = 1000L // 较早的时间戳
        )
        
        // 后期版本，时间戳较晚
        val course2 = CourseEntity(
            id = 1,
            tableId = 1,
            courseName = "高等数学", // 名称已更改
            location = "A101",
            crdtKey = crdtKey,
            tableCrdtKey = "table-1",
            updateTimestamp = 2000L // 较晚的时间戳
        )
        
        // 执行合并
        val merged = courseAdapter.merge(course1, course2)
        
        // 验证结果 - 应该保留较晚版本的名称
        assertEquals("高等数学", merged.courseName)
        // 但ID和其他未更改的字段应保持一致
        assertEquals(1, merged.id)
        assertEquals("A101", merged.location)
    }
    
    /**
     * 测试冲突解决 - 不同字段的更新
     */
    @Test
    fun testConflictResolution_DifferentFieldsUpdated() = runBlocking {
        // 准备测试数据
        val crdtKey = UUID.randomUUID().toString()
        
        // 第一个版本，更新了名称
        val course1 = CourseEntity(
            id = 1,
            tableId = 1,
            courseName = "高等数学", // 更新的名称
            location = "A101",
            crdtKey = crdtKey,
            tableCrdtKey = "table-1",
            updateTimestamp = 2000L // 较晚的名称更新时间戳
        )
        
        // 第二个版本，更新了位置
        val course2 = CourseEntity(
            id = 1,
            tableId = 1,
            courseName = "数学",
            location = "B202", // 更新的位置
            crdtKey = crdtKey,
            tableCrdtKey = "table-1",
            updateTimestamp = 1500L // 较早的位置更新时间戳
        )
        
        // 执行合并
        val merged = courseAdapter.merge(course1, course2)
        
        // 验证结果 - 应该保留较晚版本的名称和较早版本的位置
        assertEquals("高等数学", merged.courseName)
        assertEquals("B202", merged.location)
    }
} 