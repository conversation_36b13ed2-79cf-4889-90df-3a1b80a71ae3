// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.sync.adapter.TableAdapter;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideTableAdapterFactory implements Factory<TableAdapter> {
  @Override
  public TableAdapter get() {
    return provideTableAdapter();
  }

  public static SyncModule_ProvideTableAdapterFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TableAdapter provideTableAdapter() {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideTableAdapter());
  }

  private static final class InstanceHolder {
    private static final SyncModule_ProvideTableAdapterFactory INSTANCE = new SyncModule_ProvideTableAdapterFactory();
  }
}
