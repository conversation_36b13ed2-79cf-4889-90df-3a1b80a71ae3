#androidx.activity.ComponentActivityandroidx.lifecycle.ViewModelandroid.app.Applicationandroidx.room.RoomDatabasekotlin.Enum6com.example.todoschedule.data.database.entity.Syncable;com.example.todoschedule.domain.repository.CourseRepositoryBcom.example.todoschedule.domain.repository.GlobalSettingRepositoryEcom.example.todoschedule.domain.repository.OrdinaryScheduleRepository?com.example.todoschedule.domain.repository.RemoteUserRepository<com.example.todoschedule.domain.repository.SessionRepository7com.example.todoschedule.data.repository.SyncRepository:com.example.todoschedule.domain.repository.TableRepositoryDcom.example.todoschedule.domain.repository.TableTimeConfigRepository9com.example.todoschedule.domain.repository.UserRepository*com.example.todoschedule.data.sync.SyncApiandroid.app.Serviceandroid.os.Binderandroidx.work.CoroutineWorker>com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter6com.example.todoschedule.data.sync.adapter.SynkAdapterandroid.os.Parcelable2kotlinx.serialization.internal.GeneratedSerializer!kotlinx.serialization.KSerializer=com.example.todoschedule.ui.components.PermissionTextProvider0com.example.todoschedule.ui.course.add.SaveState=com.example.todoschedule.ui.course.detail.CourseDetailUiState7com.example.todoschedule.ui.course.edit.EditCourseEvent7com.example.todoschedule.ui.course.load.SaveCourseState0com.example.todoschedule.ui.navigation.AppRoutesJcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiStateHcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEvent:com.example.todoschedule.ui.schedule.model.ScheduleUiState#androidx.lifecycle.AndroidViewModel6com.example.todoschedule.ui.settings.DatabaseOperation0com.example.todoschedule.ui.task.TaskItemUiModel1com.example.todoschedule.ui.theme.ColorSchemeEnum,com.example.todoschedule.ui.todo.TodoUiState&com.example.todoschedule.util.Resource
parser.Parser                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        