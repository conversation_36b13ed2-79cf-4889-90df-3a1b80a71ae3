// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.database.dao.OrdinaryScheduleDao;
import com.example.todoschedule.data.database.dao.TimeSlotDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OrdinaryScheduleRepositoryImpl_Factory implements Factory<OrdinaryScheduleRepositoryImpl> {
  private final Provider<OrdinaryScheduleDao> ordinaryScheduleDaoProvider;

  private final Provider<TimeSlotDao> timeSlotDaoProvider;

  public OrdinaryScheduleRepositoryImpl_Factory(
      Provider<OrdinaryScheduleDao> ordinaryScheduleDaoProvider,
      Provider<TimeSlotDao> timeSlotDaoProvider) {
    this.ordinaryScheduleDaoProvider = ordinaryScheduleDaoProvider;
    this.timeSlotDaoProvider = timeSlotDaoProvider;
  }

  @Override
  public OrdinaryScheduleRepositoryImpl get() {
    return newInstance(ordinaryScheduleDaoProvider.get(), timeSlotDaoProvider.get());
  }

  public static OrdinaryScheduleRepositoryImpl_Factory create(
      Provider<OrdinaryScheduleDao> ordinaryScheduleDaoProvider,
      Provider<TimeSlotDao> timeSlotDaoProvider) {
    return new OrdinaryScheduleRepositoryImpl_Factory(ordinaryScheduleDaoProvider, timeSlotDaoProvider);
  }

  public static OrdinaryScheduleRepositoryImpl newInstance(OrdinaryScheduleDao ordinaryScheduleDao,
      TimeSlotDao timeSlotDao) {
    return new OrdinaryScheduleRepositoryImpl(ordinaryScheduleDao, timeSlotDao);
  }
}
