package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.LongSparseArray;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.RelationUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.converter.Converters;
import com.example.todoschedule.data.database.entity.TableTimeConfigEntity;
import com.example.todoschedule.data.database.entity.TableTimeConfigNodeDetaileEntity;
import com.example.todoschedule.data.model.TableTimeConfigWithNodes;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalTime;

@SuppressWarnings({"unchecked", "deprecation"})
public final class TableTimeConfigDao_Impl implements TableTimeConfigDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TableTimeConfigEntity> __insertionAdapterOfTableTimeConfigEntity;

  private final EntityInsertionAdapter<TableTimeConfigNodeDetaileEntity> __insertionAdapterOfTableTimeConfigNodeDetaileEntity;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<TableTimeConfigEntity> __deletionAdapterOfTableTimeConfigEntity;

  private final EntityDeletionOrUpdateAdapter<TableTimeConfigNodeDetaileEntity> __deletionAdapterOfTableTimeConfigNodeDetaileEntity;

  private final EntityDeletionOrUpdateAdapter<TableTimeConfigEntity> __updateAdapterOfTableTimeConfigEntity;

  private final EntityDeletionOrUpdateAdapter<TableTimeConfigNodeDetaileEntity> __updateAdapterOfTableTimeConfigNodeDetaileEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteNodeDetailsForConfig;

  private final SharedSQLiteStatement __preparedStmtOfDeleteTimeConfigsForTable;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllTimeConfigs;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllNodeDetails;

  public TableTimeConfigDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTableTimeConfigEntity = new EntityInsertionAdapter<TableTimeConfigEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `table_time_config` (`id`,`table_id`,`name`,`is_default`) VALUES (nullif(?, 0),?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableTimeConfigEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTableId());
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        final int _tmp = entity.isDefault() ? 1 : 0;
        statement.bindLong(4, _tmp);
      }
    };
    this.__insertionAdapterOfTableTimeConfigNodeDetaileEntity = new EntityInsertionAdapter<TableTimeConfigNodeDetaileEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `table_time_config_node_detaile` (`id`,`table_time_config_id`,`name`,`start_time`,`end_time`,`node`) VALUES (nullif(?, 0),?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableTimeConfigNodeDetaileEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTableTimeConfigId());
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        final String _tmp = __converters.localTimeToString(entity.getStartTime());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        final String _tmp_1 = __converters.localTimeToString(entity.getEndTime());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_1);
        }
        statement.bindLong(6, entity.getNode());
      }
    };
    this.__deletionAdapterOfTableTimeConfigEntity = new EntityDeletionOrUpdateAdapter<TableTimeConfigEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `table_time_config` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableTimeConfigEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__deletionAdapterOfTableTimeConfigNodeDetaileEntity = new EntityDeletionOrUpdateAdapter<TableTimeConfigNodeDetaileEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `table_time_config_node_detaile` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableTimeConfigNodeDetaileEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfTableTimeConfigEntity = new EntityDeletionOrUpdateAdapter<TableTimeConfigEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `table_time_config` SET `id` = ?,`table_id` = ?,`name` = ?,`is_default` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableTimeConfigEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTableId());
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        final int _tmp = entity.isDefault() ? 1 : 0;
        statement.bindLong(4, _tmp);
        statement.bindLong(5, entity.getId());
      }
    };
    this.__updateAdapterOfTableTimeConfigNodeDetaileEntity = new EntityDeletionOrUpdateAdapter<TableTimeConfigNodeDetaileEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `table_time_config_node_detaile` SET `id` = ?,`table_time_config_id` = ?,`name` = ?,`start_time` = ?,`end_time` = ?,`node` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableTimeConfigNodeDetaileEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTableTimeConfigId());
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        final String _tmp = __converters.localTimeToString(entity.getStartTime());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        final String _tmp_1 = __converters.localTimeToString(entity.getEndTime());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_1);
        }
        statement.bindLong(6, entity.getNode());
        statement.bindLong(7, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteNodeDetailsForConfig = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM table_time_config_node_detaile WHERE table_time_config_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteTimeConfigsForTable = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM table_time_config WHERE table_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllTimeConfigs = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM table_time_config";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllNodeDetails = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM table_time_config_node_detaile";
        return _query;
      }
    };
  }

  @Override
  public Object insertTimeConfig(final TableTimeConfigEntity config,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTableTimeConfigEntity.insertAndReturnId(config);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertNodeDetail(final TableTimeConfigNodeDetaileEntity node,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTableTimeConfigNodeDetaileEntity.insertAndReturnId(node);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertNodeDetails(final List<TableTimeConfigNodeDetaileEntity> nodes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfTableTimeConfigNodeDetaileEntity.insert(nodes);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTimeConfig(final TableTimeConfigEntity config,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTableTimeConfigEntity.handle(config);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteNodeDetail(final TableTimeConfigNodeDetaileEntity node,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTableTimeConfigNodeDetaileEntity.handle(node);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTimeConfig(final TableTimeConfigEntity config,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTableTimeConfigEntity.handle(config);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateNodeDetail(final TableTimeConfigNodeDetaileEntity node,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTableTimeConfigNodeDetaileEntity.handle(node);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteNodeDetailsForConfig(final int configId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteNodeDetailsForConfig.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, configId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteNodeDetailsForConfig.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTimeConfigsForTable(final int tableId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteTimeConfigsForTable.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, tableId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteTimeConfigsForTable.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllTimeConfigs(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllTimeConfigs.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllTimeConfigs.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllNodeDetails(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllNodeDetails.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllNodeDetails.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<TableTimeConfigEntity> getTimeConfigById(final int configId) {
    final String _sql = "SELECT * FROM table_time_config WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, configId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"table_time_config"}, new Callable<TableTimeConfigEntity>() {
      @Override
      @Nullable
      public TableTimeConfigEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "table_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfIsDefault = CursorUtil.getColumnIndexOrThrow(_cursor, "is_default");
          final TableTimeConfigEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpTableId;
            _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final boolean _tmpIsDefault;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDefault);
            _tmpIsDefault = _tmp != 0;
            _result = new TableTimeConfigEntity(_tmpId,_tmpTableId,_tmpName,_tmpIsDefault);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TableTimeConfigEntity>> getTimeConfigsForTable(final int tableId) {
    final String _sql = "SELECT * FROM table_time_config WHERE table_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tableId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"table_time_config"}, new Callable<List<TableTimeConfigEntity>>() {
      @Override
      @NonNull
      public List<TableTimeConfigEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "table_id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfIsDefault = CursorUtil.getColumnIndexOrThrow(_cursor, "is_default");
          final List<TableTimeConfigEntity> _result = new ArrayList<TableTimeConfigEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TableTimeConfigEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpTableId;
            _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final boolean _tmpIsDefault;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDefault);
            _tmpIsDefault = _tmp != 0;
            _item = new TableTimeConfigEntity(_tmpId,_tmpTableId,_tmpName,_tmpIsDefault);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<TableTimeConfigWithNodes> getDefaultTimeConfigWithNodes(final int tableId) {
    final String _sql = "SELECT * FROM table_time_config WHERE table_id = ? AND is_default = 1 LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tableId);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"table_time_config_node_detaile",
        "table_time_config"}, new Callable<TableTimeConfigWithNodes>() {
      @Override
      @Nullable
      public TableTimeConfigWithNodes call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "table_id");
            final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
            final int _cursorIndexOfIsDefault = CursorUtil.getColumnIndexOrThrow(_cursor, "is_default");
            final LongSparseArray<ArrayList<TableTimeConfigNodeDetaileEntity>> _collectionNodes = new LongSparseArray<ArrayList<TableTimeConfigNodeDetaileEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionNodes.containsKey(_tmpKey)) {
                _collectionNodes.put(_tmpKey, new ArrayList<TableTimeConfigNodeDetaileEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshiptableTimeConfigNodeDetaileAscomExampleTodoscheduleDataDatabaseEntityTableTimeConfigNodeDetaileEntity(_collectionNodes);
            final TableTimeConfigWithNodes _result;
            if (_cursor.moveToFirst()) {
              final TableTimeConfigEntity _tmpConfig;
              final int _tmpId;
              _tmpId = _cursor.getInt(_cursorIndexOfId);
              final int _tmpTableId;
              _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
              final String _tmpName;
              if (_cursor.isNull(_cursorIndexOfName)) {
                _tmpName = null;
              } else {
                _tmpName = _cursor.getString(_cursorIndexOfName);
              }
              final boolean _tmpIsDefault;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsDefault);
              _tmpIsDefault = _tmp != 0;
              _tmpConfig = new TableTimeConfigEntity(_tmpId,_tmpTableId,_tmpName,_tmpIsDefault);
              final ArrayList<TableTimeConfigNodeDetaileEntity> _tmpNodesCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpNodesCollection = _collectionNodes.get(_tmpKey_1);
              _result = new TableTimeConfigWithNodes(_tmpConfig,_tmpNodesCollection);
            } else {
              _result = null;
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<TableTimeConfigWithNodes> getTimeConfigWithNodesById(final int configId) {
    final String _sql = "SELECT * FROM table_time_config WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, configId);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"table_time_config_node_detaile",
        "table_time_config"}, new Callable<TableTimeConfigWithNodes>() {
      @Override
      @Nullable
      public TableTimeConfigWithNodes call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "table_id");
            final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
            final int _cursorIndexOfIsDefault = CursorUtil.getColumnIndexOrThrow(_cursor, "is_default");
            final LongSparseArray<ArrayList<TableTimeConfigNodeDetaileEntity>> _collectionNodes = new LongSparseArray<ArrayList<TableTimeConfigNodeDetaileEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionNodes.containsKey(_tmpKey)) {
                _collectionNodes.put(_tmpKey, new ArrayList<TableTimeConfigNodeDetaileEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshiptableTimeConfigNodeDetaileAscomExampleTodoscheduleDataDatabaseEntityTableTimeConfigNodeDetaileEntity(_collectionNodes);
            final TableTimeConfigWithNodes _result;
            if (_cursor.moveToFirst()) {
              final TableTimeConfigEntity _tmpConfig;
              final int _tmpId;
              _tmpId = _cursor.getInt(_cursorIndexOfId);
              final int _tmpTableId;
              _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
              final String _tmpName;
              if (_cursor.isNull(_cursorIndexOfName)) {
                _tmpName = null;
              } else {
                _tmpName = _cursor.getString(_cursorIndexOfName);
              }
              final boolean _tmpIsDefault;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsDefault);
              _tmpIsDefault = _tmp != 0;
              _tmpConfig = new TableTimeConfigEntity(_tmpId,_tmpTableId,_tmpName,_tmpIsDefault);
              final ArrayList<TableTimeConfigNodeDetaileEntity> _tmpNodesCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpNodesCollection = _collectionNodes.get(_tmpKey_1);
              _result = new TableTimeConfigWithNodes(_tmpConfig,_tmpNodesCollection);
            } else {
              _result = null;
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private void __fetchRelationshiptableTimeConfigNodeDetaileAscomExampleTodoscheduleDataDatabaseEntityTableTimeConfigNodeDetaileEntity(
      @NonNull final LongSparseArray<ArrayList<TableTimeConfigNodeDetaileEntity>> _map) {
    if (_map.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchLongSparseArray(_map, true, (map) -> {
        __fetchRelationshiptableTimeConfigNodeDetaileAscomExampleTodoscheduleDataDatabaseEntityTableTimeConfigNodeDetaileEntity(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`table_time_config_id`,`name`,`start_time`,`end_time`,`node` FROM `table_time_config_node_detaile` WHERE `table_time_config_id` IN (");
    final int _inputSize = _map.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    for (int i = 0; i < _map.size(); i++) {
      final long _item = _map.keyAt(i);
      _stmt.bindLong(_argIndex, _item);
      _argIndex++;
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, false, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "table_time_config_id");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfTableTimeConfigId = 1;
      final int _cursorIndexOfName = 2;
      final int _cursorIndexOfStartTime = 3;
      final int _cursorIndexOfEndTime = 4;
      final int _cursorIndexOfNode = 5;
      while (_cursor.moveToNext()) {
        final long _tmpKey;
        _tmpKey = _cursor.getLong(_itemKeyIndex);
        final ArrayList<TableTimeConfigNodeDetaileEntity> _tmpRelation = _map.get(_tmpKey);
        if (_tmpRelation != null) {
          final TableTimeConfigNodeDetaileEntity _item_1;
          final int _tmpId;
          _tmpId = _cursor.getInt(_cursorIndexOfId);
          final int _tmpTableTimeConfigId;
          _tmpTableTimeConfigId = _cursor.getInt(_cursorIndexOfTableTimeConfigId);
          final String _tmpName;
          if (_cursor.isNull(_cursorIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _cursor.getString(_cursorIndexOfName);
          }
          final LocalTime _tmpStartTime;
          final String _tmp;
          if (_cursor.isNull(_cursorIndexOfStartTime)) {
            _tmp = null;
          } else {
            _tmp = _cursor.getString(_cursorIndexOfStartTime);
          }
          _tmpStartTime = __converters.stringToLocalTime(_tmp);
          final LocalTime _tmpEndTime;
          final String _tmp_1;
          if (_cursor.isNull(_cursorIndexOfEndTime)) {
            _tmp_1 = null;
          } else {
            _tmp_1 = _cursor.getString(_cursorIndexOfEndTime);
          }
          _tmpEndTime = __converters.stringToLocalTime(_tmp_1);
          final int _tmpNode;
          _tmpNode = _cursor.getInt(_cursorIndexOfNode);
          _item_1 = new TableTimeConfigNodeDetaileEntity(_tmpId,_tmpTableTimeConfigId,_tmpName,_tmpStartTime,_tmpEndTime,_tmpNode);
          _tmpRelation.add(_item_1);
        }
      }
    } finally {
      _cursor.close();
    }
  }
}
