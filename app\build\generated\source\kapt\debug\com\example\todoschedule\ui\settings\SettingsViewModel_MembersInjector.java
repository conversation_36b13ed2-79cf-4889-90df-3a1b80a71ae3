// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.settings;

import com.example.todoschedule.core.utils.DevUtils;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsViewModel_MembersInjector implements MembersInjector<SettingsViewModel> {
  private final Provider<DevUtils> devUtilsProvider;

  public SettingsViewModel_MembersInjector(Provider<DevUtils> devUtilsProvider) {
    this.devUtilsProvider = devUtilsProvider;
  }

  public static MembersInjector<SettingsViewModel> create(Provider<DevUtils> devUtilsProvider) {
    return new SettingsViewModel_MembersInjector(devUtilsProvider);
  }

  @Override
  public void injectMembers(SettingsViewModel instance) {
    injectDevUtils(instance, devUtilsProvider.get());
  }

  @InjectedFieldSignature("com.example.todoschedule.ui.settings.SettingsViewModel.devUtils")
  public static void injectDevUtils(SettingsViewModel instance, DevUtils devUtils) {
    instance.devUtils = devUtils;
  }
}
