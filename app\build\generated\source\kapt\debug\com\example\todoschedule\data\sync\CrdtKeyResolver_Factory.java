// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.repository.SyncRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CrdtKeyResolver_Factory implements Factory<CrdtKeyResolver> {
  private final Provider<AppDatabase> databaseProvider;

  private final Provider<SyncRepository> syncRepositoryProvider;

  public CrdtKeyResolver_Factory(Provider<AppDatabase> databaseProvider,
      Provider<SyncRepository> syncRepositoryProvider) {
    this.databaseProvider = databaseProvider;
    this.syncRepositoryProvider = syncRepositoryProvider;
  }

  @Override
  public CrdtKeyResolver get() {
    return newInstance(databaseProvider.get(), syncRepositoryProvider);
  }

  public static CrdtKeyResolver_Factory create(Provider<AppDatabase> databaseProvider,
      Provider<SyncRepository> syncRepositoryProvider) {
    return new CrdtKeyResolver_Factory(databaseProvider, syncRepositoryProvider);
  }

  public static CrdtKeyResolver newInstance(AppDatabase database,
      Provider<SyncRepository> syncRepositoryProvider) {
    return new CrdtKeyResolver(database, syncRepositoryProvider);
  }
}
