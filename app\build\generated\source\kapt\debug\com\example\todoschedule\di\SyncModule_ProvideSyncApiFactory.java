// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.sync.DeviceIdManager;
import com.example.todoschedule.data.sync.SyncApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideSyncApiFactory implements Factory<SyncApi> {
  private final Provider<com.example.todoschedule.data.remote.api.SyncApi> remoteSyncApiProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  public SyncModule_ProvideSyncApiFactory(
      Provider<com.example.todoschedule.data.remote.api.SyncApi> remoteSyncApiProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider) {
    this.remoteSyncApiProvider = remoteSyncApiProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
  }

  @Override
  public SyncApi get() {
    return provideSyncApi(remoteSyncApiProvider.get(), deviceIdManagerProvider.get());
  }

  public static SyncModule_ProvideSyncApiFactory create(
      Provider<com.example.todoschedule.data.remote.api.SyncApi> remoteSyncApiProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider) {
    return new SyncModule_ProvideSyncApiFactory(remoteSyncApiProvider, deviceIdManagerProvider);
  }

  public static SyncApi provideSyncApi(
      com.example.todoschedule.data.remote.api.SyncApi remoteSyncApi,
      DeviceIdManager deviceIdManager) {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideSyncApi(remoteSyncApi, deviceIdManager));
  }
}
