// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.sync.adapter.CourseNodeAdapter;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideCourseNodeAdapterFactory implements Factory<CourseNodeAdapter> {
  @Override
  public CourseNodeAdapter get() {
    return provideCourseNodeAdapter();
  }

  public static SyncModule_ProvideCourseNodeAdapterFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static CourseNodeAdapter provideCourseNodeAdapter() {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideCourseNodeAdapter());
  }

  private static final class InstanceHolder {
    private static final SyncModule_ProvideCourseNodeAdapterFactory INSTANCE = new SyncModule_ProvideCourseNodeAdapterFactory();
  }
}
