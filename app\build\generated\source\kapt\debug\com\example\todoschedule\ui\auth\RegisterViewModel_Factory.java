// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.auth;

import com.example.todoschedule.domain.use_case.auth.RegisterUserUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RegisterViewModel_Factory implements Factory<RegisterViewModel> {
  private final Provider<RegisterUserUseCase> registerUserUseCaseProvider;

  public RegisterViewModel_Factory(Provider<RegisterUserUseCase> registerUserUseCaseProvider) {
    this.registerUserUseCaseProvider = registerUserUseCaseProvider;
  }

  @Override
  public RegisterViewModel get() {
    return newInstance(registerUserUseCaseProvider.get());
  }

  public static RegisterViewModel_Factory create(
      Provider<RegisterUserUseCase> registerUserUseCaseProvider) {
    return new RegisterViewModel_Factory(registerUserUseCaseProvider);
  }

  public static RegisterViewModel newInstance(RegisterUserUseCase registerUserUseCase) {
    return new RegisterViewModel(registerUserUseCase);
  }
}
