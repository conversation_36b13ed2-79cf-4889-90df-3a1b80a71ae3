package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.converter.Converters;
import com.example.todoschedule.data.database.entity.TableEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDate;

@SuppressWarnings({"unchecked", "deprecation"})
public final class TableDao_Impl implements TableDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TableEntity> __insertionAdapterOfTableEntity;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<TableEntity> __updateAdapterOfTableEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteTable;

  public TableDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTableEntity = new EntityInsertionAdapter<TableEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `table` (`id`,`userId`,`tableName`,`background`,`listPosition`,`terms`,`startDate`,`totalWeeks`,`crdtKey`,`userCrdtKey`,`update_timestamp`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getTableName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTableName());
        }
        if (entity.getBackground() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getBackground());
        }
        statement.bindLong(5, entity.getListPosition());
        if (entity.getTerms() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTerms());
        }
        final String _tmp = __converters.localDateToString(entity.getStartDate());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        statement.bindLong(8, entity.getTotalWeeks());
        if (entity.getCrdtKey() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCrdtKey());
        }
        if (entity.getUserCrdtKey() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getUserCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getUpdateTimestamp());
        }
      }
    };
    this.__updateAdapterOfTableEntity = new EntityDeletionOrUpdateAdapter<TableEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `table` SET `id` = ?,`userId` = ?,`tableName` = ?,`background` = ?,`listPosition` = ?,`terms` = ?,`startDate` = ?,`totalWeeks` = ?,`crdtKey` = ?,`userCrdtKey` = ?,`update_timestamp` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TableEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getTableName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTableName());
        }
        if (entity.getBackground() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getBackground());
        }
        statement.bindLong(5, entity.getListPosition());
        if (entity.getTerms() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTerms());
        }
        final String _tmp = __converters.localDateToString(entity.getStartDate());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        statement.bindLong(8, entity.getTotalWeeks());
        if (entity.getCrdtKey() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCrdtKey());
        }
        if (entity.getUserCrdtKey() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getUserCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getUpdateTimestamp());
        }
        statement.bindLong(12, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteTable = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM `table` WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertTable(final TableEntity table, final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTableEntity.insertAndReturnId(table);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTable(final TableEntity table, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTableEntity.handle(table);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTable(final int tableId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteTable.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, tableId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteTable.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<TableEntity>> getAllTables() {
    final String _sql = "SELECT * FROM `table` ORDER BY listPosition";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"table"}, new Callable<List<TableEntity>>() {
      @Override
      @NonNull
      public List<TableEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<TableEntity> _result = new ArrayList<TableEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TableEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<TableEntity> getDefaultTable() {
    final String _sql = "SELECT * FROM `table` ORDER BY id LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"table"}, new Callable<TableEntity>() {
      @Override
      @Nullable
      public TableEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final TableEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<TableEntity> getTableById(final int tableId) {
    final String _sql = "SELECT * FROM `table` WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tableId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"table"}, new Callable<TableEntity>() {
      @Override
      @Nullable
      public TableEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final TableEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TableEntity>> getTableByUserId(final int userId) {
    final String _sql = "SELECT * FROM `table` WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"table"}, new Callable<List<TableEntity>>() {
      @Override
      @NonNull
      public List<TableEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<TableEntity> _result = new ArrayList<TableEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TableEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object fetchTableById(final int tableId,
      final Continuation<? super TableEntity> $completion) {
    final String _sql = "SELECT * FROM `table` WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tableId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<TableEntity>() {
      @Override
      @Nullable
      public TableEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final TableEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object fetchTablesByUserId(final int userId,
      final Continuation<? super List<TableEntity>> $completion) {
    final String _sql = "SELECT * FROM `table` WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<TableEntity>>() {
      @Override
      @NonNull
      public List<TableEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<TableEntity> _result = new ArrayList<TableEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TableEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getIdByCrdtKey(final String crdtKey,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT id FROM `table` WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            if (_cursor.isNull(0)) {
              _result = null;
            } else {
              _result = _cursor.getInt(0);
            }
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTableByCrdtKey(final String crdtKey,
      final Continuation<? super TableEntity> $completion) {
    final String _sql = "SELECT * FROM `table` WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<TableEntity>() {
      @Override
      @Nullable
      public TableEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final TableEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTablesByUserCrdtKey(final String userCrdtKey,
      final Continuation<? super List<TableEntity>> $completion) {
    final String _sql = "SELECT * FROM `table` WHERE userCrdtKey = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userCrdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userCrdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<TableEntity>>() {
      @Override
      @NonNull
      public List<TableEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTableName = CursorUtil.getColumnIndexOrThrow(_cursor, "tableName");
          final int _cursorIndexOfBackground = CursorUtil.getColumnIndexOrThrow(_cursor, "background");
          final int _cursorIndexOfListPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "listPosition");
          final int _cursorIndexOfTerms = CursorUtil.getColumnIndexOrThrow(_cursor, "terms");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfTotalWeeks = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWeeks");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<TableEntity> _result = new ArrayList<TableEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TableEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTableName;
            if (_cursor.isNull(_cursorIndexOfTableName)) {
              _tmpTableName = null;
            } else {
              _tmpTableName = _cursor.getString(_cursorIndexOfTableName);
            }
            final String _tmpBackground;
            if (_cursor.isNull(_cursorIndexOfBackground)) {
              _tmpBackground = null;
            } else {
              _tmpBackground = _cursor.getString(_cursorIndexOfBackground);
            }
            final int _tmpListPosition;
            _tmpListPosition = _cursor.getInt(_cursorIndexOfListPosition);
            final String _tmpTerms;
            if (_cursor.isNull(_cursorIndexOfTerms)) {
              _tmpTerms = null;
            } else {
              _tmpTerms = _cursor.getString(_cursorIndexOfTerms);
            }
            final LocalDate _tmpStartDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfStartDate);
            }
            _tmpStartDate = __converters.stringToLocalDate(_tmp);
            final int _tmpTotalWeeks;
            _tmpTotalWeeks = _cursor.getInt(_cursorIndexOfTotalWeeks);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new TableEntity(_tmpId,_tmpUserId,_tmpTableName,_tmpBackground,_tmpListPosition,_tmpTerms,_tmpStartDate,_tmpTotalWeeks,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
