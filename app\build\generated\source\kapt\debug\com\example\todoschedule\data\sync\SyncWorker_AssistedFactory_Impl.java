// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import android.content.Context;
import androidx.work.WorkerParameters;
import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.inject.Provider;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncWorker_AssistedFactory_Impl implements SyncWorker_AssistedFactory {
  private final SyncWorker_Factory delegateFactory;

  SyncWorker_AssistedFactory_Impl(SyncWorker_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public SyncWorker create(Context arg0, WorkerParameters arg1) {
    return delegateFactory.get(arg0, arg1);
  }

  public static Provider<SyncWorker_AssistedFactory> create(SyncWorker_Factory delegateFactory) {
    return InstanceFactory.create(new SyncWorker_AssistedFactory_Impl(delegateFactory));
  }
}
