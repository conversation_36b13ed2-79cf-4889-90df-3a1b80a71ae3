// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import com.example.todoschedule.data.database.dao.SyncMessageDao;
import com.example.todoschedule.data.remote.api.SyncApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncMessageUploader_Factory implements Factory<SyncMessageUploader> {
  private final Provider<SyncApi> syncApiProvider;

  private final Provider<SyncMessageDao> syncMessageDaoProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  public SyncMessageUploader_Factory(Provider<SyncApi> syncApiProvider,
      Provider<SyncMessageDao> syncMessageDaoProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider) {
    this.syncApiProvider = syncApiProvider;
    this.syncMessageDaoProvider = syncMessageDaoProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
  }

  @Override
  public SyncMessageUploader get() {
    return newInstance(syncApiProvider.get(), syncMessageDaoProvider.get(), deviceIdManagerProvider.get());
  }

  public static SyncMessageUploader_Factory create(Provider<SyncApi> syncApiProvider,
      Provider<SyncMessageDao> syncMessageDaoProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider) {
    return new SyncMessageUploader_Factory(syncApiProvider, syncMessageDaoProvider, deviceIdManagerProvider);
  }

  public static SyncMessageUploader newInstance(SyncApi syncApi, SyncMessageDao syncMessageDao,
      DeviceIdManager deviceIdManager) {
    return new SyncMessageUploader(syncApi, syncMessageDao, deviceIdManager);
  }
}
