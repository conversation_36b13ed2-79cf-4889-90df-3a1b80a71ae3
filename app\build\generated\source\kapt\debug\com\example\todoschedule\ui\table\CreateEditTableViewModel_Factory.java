// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.table;

import androidx.lifecycle.SavedStateHandle;
import com.example.todoschedule.domain.repository.GlobalSettingRepository;
import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.repository.TableRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CreateEditTableViewModel_Factory implements Factory<CreateEditTableViewModel> {
  private final Provider<TableRepository> tableRepositoryProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<GlobalSettingRepository> globalSettingRepositoryProvider;

  private final Provider<SavedStateHandle> savedStateHandleProvider;

  public CreateEditTableViewModel_Factory(Provider<TableRepository> tableRepositoryProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    this.tableRepositoryProvider = tableRepositoryProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.globalSettingRepositoryProvider = globalSettingRepositoryProvider;
    this.savedStateHandleProvider = savedStateHandleProvider;
  }

  @Override
  public CreateEditTableViewModel get() {
    return newInstance(tableRepositoryProvider.get(), sessionRepositoryProvider.get(), globalSettingRepositoryProvider.get(), savedStateHandleProvider.get());
  }

  public static CreateEditTableViewModel_Factory create(
      Provider<TableRepository> tableRepositoryProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    return new CreateEditTableViewModel_Factory(tableRepositoryProvider, sessionRepositoryProvider, globalSettingRepositoryProvider, savedStateHandleProvider);
  }

  public static CreateEditTableViewModel newInstance(TableRepository tableRepository,
      SessionRepository sessionRepository, GlobalSettingRepository globalSettingRepository,
      SavedStateHandle savedStateHandle) {
    return new CreateEditTableViewModel(tableRepository, sessionRepository, globalSettingRepository, savedStateHandle);
  }
}
