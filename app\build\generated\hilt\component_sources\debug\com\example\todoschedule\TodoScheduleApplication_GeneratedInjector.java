package com.example.todoschedule;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = TodoScheduleApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface TodoScheduleApplication_GeneratedInjector {
  void injectTodoScheduleApplication(TodoScheduleApplication todoScheduleApplication);
}
