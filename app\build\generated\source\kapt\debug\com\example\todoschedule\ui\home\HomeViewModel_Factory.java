// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.home;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HomeViewModel_Factory implements Factory<HomeViewModel> {
  @Override
  public HomeViewModel get() {
    return newInstance();
  }

  public static HomeViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static HomeViewModel newInstance() {
    return new HomeViewModel();
  }

  private static final class InstanceHolder {
    private static final HomeViewModel_Factory INSTANCE = new HomeViewModel_Factory();
  }
}
