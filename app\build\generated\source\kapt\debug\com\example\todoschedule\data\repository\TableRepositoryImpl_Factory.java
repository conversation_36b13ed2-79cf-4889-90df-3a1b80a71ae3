// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.database.dao.TableDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TableRepositoryImpl_Factory implements Factory<TableRepositoryImpl> {
  private final Provider<TableDao> tableDaoProvider;

  public TableRepositoryImpl_Factory(Provider<TableDao> tableDaoProvider) {
    this.tableDaoProvider = tableDaoProvider;
  }

  @Override
  public TableRepositoryImpl get() {
    return newInstance(tableDaoProvider.get());
  }

  public static TableRepositoryImpl_Factory create(Provider<TableDao> tableDaoProvider) {
    return new TableRepositoryImpl_Factory(tableDaoProvider);
  }

  public static TableRepositoryImpl newInstance(TableDao tableDao) {
    return new TableRepositoryImpl(tableDao);
  }
}
