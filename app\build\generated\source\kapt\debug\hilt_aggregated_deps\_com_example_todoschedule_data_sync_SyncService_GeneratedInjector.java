package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ServiceComponent",
    entryPoints = "com.example.todoschedule.data.sync.SyncService_GeneratedInjector"
)
public class _com_example_todoschedule_data_sync_SyncService_GeneratedInjector {
}
