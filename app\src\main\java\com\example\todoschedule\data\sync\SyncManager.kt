package com.example.todoschedule.data.sync

import android.util.Log
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOr
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Err
import com.tap.hlc.Timestamp
import com.tap.hlc.HybridLogicalClock
import com.tap.hlc.NodeID
import com.example.todoschedule.data.database.entity.SyncMessageEntity
import com.example.todoschedule.data.database.entity.toEntity
import com.example.todoschedule.data.repository.SyncRepository
import com.example.todoschedule.data.sync.dto.SyncMessageDto
import com.example.todoschedule.data.sync.dto.TimestampDto
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton
import com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry
import com.example.todoschedule.data.sync.adapter.SynkAdapter
import com.example.todoschedule.data.database.entity.Syncable
import com.example.todoschedule.data.database.entity.CourseEntity
import com.example.todoschedule.data.database.entity.CourseNodeEntity
import com.example.todoschedule.data.database.entity.OrdinaryScheduleEntity
import com.example.todoschedule.data.database.entity.TableEntity
import com.example.todoschedule.data.sync.CrdtKeyResolver
import org.json.JSONObject
import java.util.UUID

/**
 * 同步管理器
 * 
 * 负责协调同步操作和处理CRDT冲突解决逻辑。这是应用程序中同步功能的核心组件，
 * 它使用HybridLogicalClock来维护分布式系统中事件的因果关系。
 */
@Singleton
class SyncManager @Inject constructor(
    private val syncRepository: SyncRepository,
    private val deviceIdManager: DeviceIdManager,
    private val synkAdapterRegistry: SynkAdapterRegistry,
    private val crdtKeyResolver: CrdtKeyResolver
) {
    companion object {
        private const val TAG = "SyncManager"
    }
    
    private val _syncState = MutableStateFlow(SyncState.IDLE)
    val syncState: StateFlow<SyncState> = _syncState
    
    private val json = Json { 
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    // 混合逻辑时钟(HLC)实例，用于生成时间戳和解决冲突
    private var hlcClock: HybridLogicalClock? = null
    
    /**
     * 初始化同步管理器
     * 
     * 创建并配置HLC时钟，并启动周期性同步服务
     * @param coroutineScope 协程作用域
     */
    fun initialize(coroutineScope: CoroutineScope) {
        coroutineScope.launch {
            try {
                val deviceId = deviceIdManager.getOrCreateDeviceId()
                
                // 使用设备ID初始化HLC时钟，确保每个设备的时间戳都是唯一的
                val currentTime = Timestamp.now(kotlinx.datetime.Clock.System)
                hlcClock = HybridLogicalClock(
                    timestamp = currentTime,
                    node = NodeID(deviceId),
                    counter = 0
                )
                
                Log.d(TAG, "SyncManager initialized with device ID: $deviceId")
            } catch (e: Exception) {
                Log.e(TAG, "Error initializing SyncManager", e)
            }
        }
    }
    
    /**
     * 创建同步消息
     * 
     * 为要同步的数据生成带有HLC时间戳的消息实体
     * 
     * @param crdtKey 实体在分布式系统中的唯一标识符
     * @param entityType 实体类型
     * @param operationType 操作类型
     * @param userId 用户ID
     * @param payload 消息负载（实体JSON序列化数据）
     * @return 同步消息实体
     */
    suspend fun createSyncMessage(
        crdtKey: String,
        entityType: SyncConstants.EntityType,
        operationType: String,
        userId: Int,
        payload: String
    ): SyncMessageEntity {
        return withContext(Dispatchers.IO) {
            val deviceId = deviceIdManager.getOrCreateDeviceId()
            // 获取当前的HLC时间戳和更新逻辑时钟
            val hlc = getAndUpdateClock()
            
            SyncMessageEntity(
                syncId = 0, // 自增ID，数据库会自动分配
                crdtKey = crdtKey,
                entityType = entityType.value,
                operationType = operationType,
                deviceId = deviceId,
                timestampWallClock = hlc.timestamp.epochMillis,
                timestampLogical = hlc.counter.toLong(),
                timestampNodeId = hlc.node.identifier,
                payload = payload,
                userId = userId
            )
        }
    }
    
    /**
     * 创建并保存同步消息
     * 
     * 使用实体类型的适配器将实体对象序列化为JSON，并创建带有时间戳的同步消息保存到本地数据库
     * 
     * @param crdtKey 实体在分布式系统中的唯一标识符
     * @param entityType 实体类型
     * @param operationType 操作类型
     * @param userId 用户ID
     * @param entity 实体对象
     */
    @Suppress("UNCHECKED_CAST")
    suspend fun createAndSaveSyncMessage(
        crdtKey: String,
        entityType: SyncConstants.EntityType,
        operationType: String,
        userId: Int,
        entity: Any
    ) {
        withContext(Dispatchers.IO) {
            try {
                // 通过实体类型获取对应的适配器
                val adapter = synkAdapterRegistry.getAdapter(entityType)
                
                when (entityType) {
                    SyncConstants.EntityType.COURSE -> {
                        if (entity is CourseEntity) {
                            val typedAdapter = adapter as SynkAdapter<CourseEntity>
                            // 使用适配器将实体序列化为Map
                            val serializedMap = typedAdapter.serialize(entity)
                            // 使用自定义Map序列化器而不是默认的Any序列化器
                            val serializedJson = serializeMapToJson(serializedMap)
                            val message = createSyncMessage(crdtKey, entityType, operationType, userId, serializedJson)
                            syncRepository.saveSyncMessage(message)
                        } else {
                            Log.e(TAG, "实体类型不匹配: 期望CourseEntity，实际为${entity::class.java.simpleName}")
                        }
                    }
                    SyncConstants.EntityType.COURSE_NODE -> {
                        if (entity is CourseNodeEntity) {
                            val typedAdapter = adapter as SynkAdapter<CourseNodeEntity>
                            val serializedMap = typedAdapter.serialize(entity)
                            val serializedJson = serializeMapToJson(serializedMap)
                            val message = createSyncMessage(crdtKey, entityType, operationType, userId, serializedJson)
                            syncRepository.saveSyncMessage(message)
                        } else {
                            Log.e(TAG, "实体类型不匹配: 期望CourseNodeEntity，实际为${entity::class.java.simpleName}")
                        }
                    }
                    SyncConstants.EntityType.ORDINARY_SCHEDULE -> {
                        if (entity is OrdinaryScheduleEntity) {
                            val typedAdapter = adapter as SynkAdapter<OrdinaryScheduleEntity>
                            val serializedMap = typedAdapter.serialize(entity)
                            val serializedJson = serializeMapToJson(serializedMap)
                            val message = createSyncMessage(crdtKey, entityType, operationType, userId, serializedJson)
                            syncRepository.saveSyncMessage(message)
                        } else {
                            Log.e(TAG, "实体类型不匹配: 期望OrdinaryScheduleEntity，实际为${entity::class.java.simpleName}")
                        }
                    }
                    SyncConstants.EntityType.TABLE -> {
                        if (entity is TableEntity) {
                            val typedAdapter = adapter as SynkAdapter<TableEntity>
                            val serializedMap = typedAdapter.serialize(entity)
                            val serializedJson = serializeMapToJson(serializedMap)
                            val message = createSyncMessage(crdtKey, entityType, operationType, userId, serializedJson)
                            syncRepository.saveSyncMessage(message)
                        } else {
                            Log.e(TAG, "实体类型不匹配: 期望TableEntity，实际为${entity::class.java.simpleName}")
                        }
                    }
                    SyncConstants.EntityType.TIME_SLOT -> {
                        Log.e(TAG, "暂不支持TimeSlot实体类型的同步")
                    }
                    SyncConstants.EntityType.TABLE_TIME_CONFIG -> {
                        Log.e(TAG, "暂不支持TableTimeConfig实体类型的同步")
                    }
                    SyncConstants.EntityType.TABLE_TIME_CONFIG_NODE -> {
                        Log.e(TAG, "暂不支持TableTimeConfigNode实体类型的同步")
                    }
                    SyncConstants.EntityType.GLOBAL_TABLE_SETTING -> {
                        Log.e(TAG, "暂不支持GlobalTableSetting实体类型的同步")
                    }
                }
                
                Log.d(TAG, "Created sync message for ${entityType.value} with crdtKey $crdtKey")
            } catch (e: Exception) {
                Log.e(TAG, "Error creating sync message", e)
            }
        }
    }
    
    /**
     * 手动将Map<String, Any?>序列化为JSON字符串
     * 避免因为Any类型没有序列化器而导致的问题
     * 
     * @param map 要序列化的Map
     * @return 序列化后的JSON字符串
     */
    private fun serializeMapToJson(map: Map<String, Any?>): String {
        val jsonObject = org.json.JSONObject()
        for ((key, value) in map) {
            when (value) {
                null -> jsonObject.put(key, JSONObject.NULL)
                is String -> jsonObject.put(key, value)
                is Number -> jsonObject.put(key, value)
                is Boolean -> jsonObject.put(key, value)
                is Map<*, *> -> {
                    @Suppress("UNCHECKED_CAST")
                    jsonObject.put(key, serializeMapToJson(value as Map<String, Any?>))
                }
                is List<*> -> {
                    val jsonArray = org.json.JSONArray()
                    value.forEach { item ->
                        when (item) {
                            null -> jsonArray.put(JSONObject.NULL)
                            is String -> jsonArray.put(item)
                            is Number -> jsonArray.put(item)
                            is Boolean -> jsonArray.put(item)
                            is Map<*, *> -> {
                                @Suppress("UNCHECKED_CAST")
                                jsonArray.put(serializeMapToJson(item as Map<String, Any?>))
                            }
                            else -> jsonArray.put(item.toString())
                        }
                    }
                    jsonObject.put(key, jsonArray)
                }
                else -> jsonObject.put(key, value.toString())
            }
        }
        return jsonObject.toString()
    }
    
    /**
     * 立即执行同步
     * 
     * 触发立即同步操作，同时上传本地消息和下载远程消息
     * @return 是否同步成功
     */
    suspend fun syncNow(): Boolean {
        Log.d(TAG, "Executing immediate sync")
        
        try {
            _syncState.value = SyncState.SYNCING
            // 使用syncData方法代替syncAll，确保同时上传和下载消息
            syncRepository.syncData()
            _syncState.value = SyncState.SYNCED
            
            Log.d(TAG, "Immediate sync completed successfully")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error during immediate sync", e)
            _syncState.value = SyncState.FAILED
            return false
        }
    }
    
    /**
     * 获取所有同步消息
     * 
     * @return 同步消息流
     */
    fun getAllSyncMessages(): Flow<List<SyncMessageEntity>> {
        return syncRepository.getAllSyncMessages()
    }
    
    /**
     * 获取并更新混合逻辑时钟
     * 
     * 为本地事件更新逻辑时钟，实现了HLC的localTick操作
     * @return 更新后的HybridLogicalClock
     */
    private fun getAndUpdateClock(): HybridLogicalClock {
        synchronized(this) {
            val currentTime = Timestamp.now(kotlinx.datetime.Clock.System)
            val deviceId = try {
                deviceIdManager.getCachedDeviceId()
            } catch (e: Exception) {
                Log.w(TAG, "无法获取缓存的设备ID，将生成新的: ${e.message}")
                UUID.randomUUID().toString()
            }
            
            if (hlcClock == null) {
                hlcClock = HybridLogicalClock(
                    timestamp = currentTime,
                    node = NodeID(deviceId),
                    counter = 0
                )
                return hlcClock!!
            }
            
            // 使用HLC的localTick更新时钟
            val result = HybridLogicalClock.localTick(
                local = hlcClock!!,
                wallClockTime = currentTime
            )
            
            // 处理可能的错误
            when (result) {
                is Ok -> {
                    hlcClock = result.value
                    return result.value
                }
                is Err -> {
                    Log.e(TAG, "HLC时钟更新错误: ${result.error}")
                    // 当出现错误时，重置时钟并返回
                    hlcClock = HybridLogicalClock(
                        timestamp = currentTime,
                        node = NodeID(deviceId),
                        counter = 0
                    )
                    return hlcClock!!
                }
            }
        }
    }
    
    /**
     * 更新时钟与远程节点同步
     * 
     * 接收远程消息时更新逻辑时钟，实现了HLC的remoteTock操作
     * @param remoteTimestamp 远程消息的时间戳
     * @param remoteCounter 远程消息的逻辑计数器
     * @param remoteNodeId 远程消息的节点ID
     * @return 更新后的HybridLogicalClock
     */
    private fun updateClockWithRemote(
        remoteTimestamp: Long,
        remoteCounter: Int,
        remoteNodeId: String
    ): HybridLogicalClock {
        synchronized(this) {
            val currentTime = Timestamp.now(kotlinx.datetime.Clock.System)
            val deviceId = deviceIdManager.getCachedDeviceId()
            
            if (hlcClock == null) {
                hlcClock = HybridLogicalClock(
                    timestamp = currentTime,
                    node = NodeID(deviceId),
                    counter = 0
                )
                return hlcClock!!
            }
            
            // 构造远程HLC
            val remoteHLC = HybridLogicalClock(
                timestamp = Timestamp(remoteTimestamp),
                node = NodeID(remoteNodeId),
                counter = remoteCounter
            )
            
            // 使用HLC的remoteTock更新时钟
            val result = HybridLogicalClock.remoteTock(
                local = hlcClock!!,
                remote = remoteHLC,
                wallClockTime = currentTime
            )
            
            // 处理可能的错误
            when (result) {
                is Ok -> {
                    hlcClock = result.value
                    return result.value
                }
                is Err -> {
                    Log.e(TAG, "HLC时钟与远程同步错误: ${result.error}")
                    
                    // 对于节点ID重复错误，生成新的节点ID
                    if (result.error is com.tap.hlc.HLCError.DuplicateNodeError) {
                        Log.w(TAG, "节点ID重复，生成新的节点ID")
                        hlcClock = HybridLogicalClock(
                            timestamp = currentTime,
                            node = NodeID.mint(), // 生成新的NodeID
                            counter = hlcClock!!.counter
                        )
                    }
                    
                    return hlcClock!!
                }
            }
        }
    }
    
    /**
     * 处理从服务器接收到的消息
     * 
     * 解析消息并应用CRDT合并逻辑，处理可能的冲突
     * @param messageDto 接收到的消息DTO
     */
    suspend fun processReceivedMessage(messageDto: SyncMessageDto) {
        try {
            Log.d(TAG, "处理接收到的消息: crdtKey=${messageDto.crdtKey} (${messageDto.entityType})")
            
            // 获取实体类型
            val entityType = getEntityTypeFromString(messageDto.entityType)
                ?: throw IllegalArgumentException("未知的实体类型: ${messageDto.entityType}")
            
            // 获取对应的适配器
            val adapter = synkAdapterRegistry.getAdapter(entityType.value)
            
            // 使用远程消息更新本地HLC时钟
            val remoteTimestamp = messageDto.timestamp.wallClockTime
            val remoteCounter = messageDto.timestamp.logicalTime.toInt()
            val remoteNodeId = messageDto.timestamp.nodeId
            updateClockWithRemote(remoteTimestamp, remoteCounter, remoteNodeId)
            
            // 根据实体类型处理消息
            when (entityType) {
                SyncConstants.EntityType.COURSE -> processEntityMessage<CourseEntity>(messageDto, adapter)
                SyncConstants.EntityType.COURSE_NODE -> processEntityMessage<CourseNodeEntity>(messageDto, adapter)
                SyncConstants.EntityType.ORDINARY_SCHEDULE -> processEntityMessage<OrdinaryScheduleEntity>(messageDto, adapter)
                SyncConstants.EntityType.TABLE -> processEntityMessage<TableEntity>(messageDto, adapter)
                SyncConstants.EntityType.TIME_SLOT -> {
                    Log.d(TAG, "TIME_SLOT实体类型同步尚未实现")
                }
                SyncConstants.EntityType.TABLE_TIME_CONFIG -> {
                    Log.d(TAG, "TABLE_TIME_CONFIG实体类型同步尚未实现")
                }
                else -> {
                    Log.d(TAG, "不支持的实体类型: ${entityType.value}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理接收到的消息时发生错误: ${e.message}", e)
        }
    }
    
    /**
     * 根据字符串获取实体类型枚举
     * 
     * @param typeString 实体类型字符串
     * @return 实体类型枚举或null
     */
    private fun getEntityTypeFromString(typeString: String): SyncConstants.EntityType? {
        return SyncConstants.EntityType.values().find { it.value == typeString }
    }
    
    /**
     * 处理特定类型的实体消息
     * 
     * @param messageDto 接收到的消息DTO
     * @param adapter 实体适配器
     */
    @Suppress("UNCHECKED_CAST")
    private suspend inline fun <reified T : Syncable> processEntityMessage(
        messageDto: SyncMessageDto,
        adapter: SynkAdapter<*>
    ) {
        try {
            // 转换为类型安全的适配器
            val typedAdapter = adapter as SynkAdapter<T>
            
            // 解析消息负载
            val serializedMap = deserializeJsonToMap(messageDto.payload)
            val remoteEntity = typedAdapter.deserialize(serializedMap)
            
            // 本地可能已经存在的实体
            val localEntity = syncRepository.getEntityByCrdtKey<T>(messageDto.crdtKey)
            
            // 应用CRDT合并逻辑
            val mergedEntity = if (localEntity != null) {
                // 如果本地存在，执行冲突解决
                Log.d(TAG, "发现本地现有实体，执行CRDT合并")
                typedAdapter.merge(localEntity, remoteEntity)
            } else {
                // 如果本地不存在，直接使用远程实体
                Log.d(TAG, "本地不存在此实体，直接使用远程版本")
                remoteEntity
            }
            
            // 保存合并后的实体
            syncRepository.saveEntity(mergedEntity)
            
            // 保存同步消息以供追踪
            val localMessage = messageDto.toEntity()
            syncRepository.saveSyncMessage(localMessage)
        } catch (e: Exception) {
            Log.e(TAG, "处理实体消息时发生错误: ${e.message}", e)
        }
    }
    
    /**
     * 将JSON字符串反序列化为Map<String, Any?>
     * 与serializeMapToJson方法配对使用
     * 
     * @param json JSON字符串
     * @return 反序列化后的Map
     */
    private fun deserializeJsonToMap(json: String): Map<String, Any?> {
        val result = mutableMapOf<String, Any?>()
        val jsonObject = org.json.JSONObject(json)
        val keys = jsonObject.keys()
        
        while (keys.hasNext()) {
            val key = keys.next()
            val value = jsonObject.get(key)
            
            result[key] = when {
                value == JSONObject.NULL -> null
                value is org.json.JSONObject -> deserializeJsonToMap(value.toString())
                value is org.json.JSONArray -> {
                    val list = mutableListOf<Any?>()
                    for (i in 0 until value.length()) {
                        val item = value.get(i)
                        list.add(
                            when {
                                item == JSONObject.NULL -> null
                                item is org.json.JSONObject -> deserializeJsonToMap(item.toString())
                                item is org.json.JSONArray -> throw IllegalArgumentException("不支持嵌套JSONArray")
                                else -> item
                            }
                        )
                    }
                    list
                }
                else -> value
            }
        }
        
        return result
    }
    
    /**
     * 同步状态枚举
     */
    enum class SyncState {
        IDLE,       // 空闲
        SYNCING,    // 同步中
        SYNCED,     // 同步完成
        FAILED      // 同步失败
    }
    
    /**
     * 获取同步仓库实例
     * 允许外部组件访问同步仓库以执行特定操作
     * @return SyncRepository实例
     */
    fun getSyncRepository(): SyncRepository {
        return syncRepository
    }
} 