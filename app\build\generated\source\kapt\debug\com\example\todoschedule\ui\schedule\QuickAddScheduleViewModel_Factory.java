// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.schedule;

import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class QuickAddScheduleViewModel_Factory implements Factory<QuickAddScheduleViewModel> {
  private final Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  public QuickAddScheduleViewModel_Factory(
      Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    this.addOrdinaryScheduleUseCaseProvider = addOrdinaryScheduleUseCaseProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public QuickAddScheduleViewModel get() {
    return newInstance(addOrdinaryScheduleUseCaseProvider.get(), sessionRepositoryProvider.get());
  }

  public static QuickAddScheduleViewModel_Factory create(
      Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new QuickAddScheduleViewModel_Factory(addOrdinaryScheduleUseCaseProvider, sessionRepositoryProvider);
  }

  public static QuickAddScheduleViewModel newInstance(
      AddOrdinaryScheduleUseCase addOrdinaryScheduleUseCase, SessionRepository sessionRepository) {
    return new QuickAddScheduleViewModel(addOrdinaryScheduleUseCase, sessionRepository);
  }
}
