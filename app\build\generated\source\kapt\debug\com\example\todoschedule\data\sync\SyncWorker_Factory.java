// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.example.todoschedule.data.repository.SyncRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncWorker_Factory {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<SyncManager> syncManagerProvider;

  public SyncWorker_Factory(Provider<SyncRepository> syncRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.syncManagerProvider = syncManagerProvider;
  }

  public SyncWorker get(Context context, WorkerParameters workerParams) {
    return newInstance(context, workerParams, syncRepositoryProvider.get(), syncManagerProvider.get());
  }

  public static SyncWorker_Factory create(Provider<SyncRepository> syncRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    return new SyncWorker_Factory(syncRepositoryProvider, syncManagerProvider);
  }

  public static SyncWorker newInstance(Context context, WorkerParameters workerParams,
      SyncRepository syncRepository, SyncManager syncManager) {
    return new SyncWorker(context, workerParams, syncRepository, syncManager);
  }
}
