// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.course.edit;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.internal.lifecycle.HiltViewModelMap.KeySet")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EditCourseViewModel_HiltModules_KeyModule_ProvideFactory implements Factory<String> {
  @Override
  public String get() {
    return provide();
  }

  public static EditCourseViewModel_HiltModules_KeyModule_ProvideFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static String provide() {
    return Preconditions.checkNotNullFromProvides(EditCourseViewModel_HiltModules.KeyModule.provide());
  }

  private static final class InstanceHolder {
    private static final EditCourseViewModel_HiltModules_KeyModule_ProvideFactory INSTANCE = new EditCourseViewModel_HiltModules_KeyModule_ProvideFactory();
  }
}
