package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.converter.Converters;
import com.example.todoschedule.data.database.converter.ReminderType;
import com.example.todoschedule.data.database.converter.ScheduleType;
import com.example.todoschedule.data.database.entity.TimeSlotEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class TimeSlotDao_Impl implements TimeSlotDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TimeSlotEntity> __insertionAdapterOfTimeSlotEntity;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<TimeSlotEntity> __deletionAdapterOfTimeSlotEntity;

  private final EntityDeletionOrUpdateAdapter<TimeSlotEntity> __updateAdapterOfTimeSlotEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteTimeSlotsBySchedule;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllTimeSlots;

  public TimeSlotDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTimeSlotEntity = new EntityInsertionAdapter<TimeSlotEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `time_slot` (`id`,`user_id`,`start_time`,`end_time`,`schedule_type`,`schedule_id`,`head`,`priority`,`is_completed`,`is_repeated`,`repeat_pattern`,`reminder_type`,`reminder_offset`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TimeSlotEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        statement.bindLong(3, entity.getStartTime());
        statement.bindLong(4, entity.getEndTime());
        final String _tmp = __converters.fromScheduleType(entity.getScheduleType());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        statement.bindLong(6, entity.getScheduleId());
        if (entity.getHead() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getHead());
        }
        if (entity.getPriority() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getPriority());
        }
        final int _tmp_1 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(9, _tmp_1);
        final int _tmp_2 = entity.isRepeated() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        if (entity.getRepeatPattern() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getRepeatPattern());
        }
        final String _tmp_3 = __converters.fromReminderType(entity.getReminderType());
        if (_tmp_3 == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, _tmp_3);
        }
        if (entity.getReminderOffset() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getReminderOffset());
        }
      }
    };
    this.__deletionAdapterOfTimeSlotEntity = new EntityDeletionOrUpdateAdapter<TimeSlotEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `time_slot` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TimeSlotEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfTimeSlotEntity = new EntityDeletionOrUpdateAdapter<TimeSlotEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `time_slot` SET `id` = ?,`user_id` = ?,`start_time` = ?,`end_time` = ?,`schedule_type` = ?,`schedule_id` = ?,`head` = ?,`priority` = ?,`is_completed` = ?,`is_repeated` = ?,`repeat_pattern` = ?,`reminder_type` = ?,`reminder_offset` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TimeSlotEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        statement.bindLong(3, entity.getStartTime());
        statement.bindLong(4, entity.getEndTime());
        final String _tmp = __converters.fromScheduleType(entity.getScheduleType());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        statement.bindLong(6, entity.getScheduleId());
        if (entity.getHead() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getHead());
        }
        if (entity.getPriority() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getPriority());
        }
        final int _tmp_1 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(9, _tmp_1);
        final int _tmp_2 = entity.isRepeated() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        if (entity.getRepeatPattern() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getRepeatPattern());
        }
        final String _tmp_3 = __converters.fromReminderType(entity.getReminderType());
        if (_tmp_3 == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, _tmp_3);
        }
        if (entity.getReminderOffset() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getReminderOffset());
        }
        statement.bindLong(14, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteTimeSlotsBySchedule = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM time_slot WHERE schedule_type = ? AND schedule_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllTimeSlots = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM time_slot";
        return _query;
      }
    };
  }

  @Override
  public Object insertTimeSlot(final TimeSlotEntity timeSlot,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTimeSlotEntity.insertAndReturnId(timeSlot);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertTimeSlots(final List<TimeSlotEntity> timeSlots,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfTimeSlotEntity.insert(timeSlots);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTimeSlot(final TimeSlotEntity timeSlot,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTimeSlotEntity.handle(timeSlot);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTimeSlot(final TimeSlotEntity timeSlot,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTimeSlotEntity.handle(timeSlot);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTimeSlotsBySchedule(final ScheduleType scheduleType, final int scheduleId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteTimeSlotsBySchedule.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromScheduleType(scheduleType);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, scheduleId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteTimeSlotsBySchedule.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllTimeSlots(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllTimeSlots.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllTimeSlots.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<TimeSlotEntity> getTimeSlotById(final int id) {
    final String _sql = "SELECT * FROM time_slot WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_slot"}, new Callable<TimeSlotEntity>() {
      @Override
      @Nullable
      public TimeSlotEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "end_time");
          final int _cursorIndexOfScheduleType = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_type");
          final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
          final int _cursorIndexOfHead = CursorUtil.getColumnIndexOrThrow(_cursor, "head");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfIsRepeated = CursorUtil.getColumnIndexOrThrow(_cursor, "is_repeated");
          final int _cursorIndexOfRepeatPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "repeat_pattern");
          final int _cursorIndexOfReminderType = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_type");
          final int _cursorIndexOfReminderOffset = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_offset");
          final TimeSlotEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final ScheduleType _tmpScheduleType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfScheduleType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfScheduleType);
            }
            _tmpScheduleType = __converters.toScheduleType(_tmp);
            final int _tmpScheduleId;
            _tmpScheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
            final String _tmpHead;
            if (_cursor.isNull(_cursorIndexOfHead)) {
              _tmpHead = null;
            } else {
              _tmpHead = _cursor.getString(_cursorIndexOfHead);
            }
            final Integer _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final boolean _tmpIsRepeated;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsRepeated);
            _tmpIsRepeated = _tmp_2 != 0;
            final String _tmpRepeatPattern;
            if (_cursor.isNull(_cursorIndexOfRepeatPattern)) {
              _tmpRepeatPattern = null;
            } else {
              _tmpRepeatPattern = _cursor.getString(_cursorIndexOfRepeatPattern);
            }
            final ReminderType _tmpReminderType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfReminderType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfReminderType);
            }
            _tmpReminderType = __converters.toReminderType(_tmp_3);
            final Long _tmpReminderOffset;
            if (_cursor.isNull(_cursorIndexOfReminderOffset)) {
              _tmpReminderOffset = null;
            } else {
              _tmpReminderOffset = _cursor.getLong(_cursorIndexOfReminderOffset);
            }
            _result = new TimeSlotEntity(_tmpId,_tmpUserId,_tmpStartTime,_tmpEndTime,_tmpScheduleType,_tmpScheduleId,_tmpHead,_tmpPriority,_tmpIsCompleted,_tmpIsRepeated,_tmpRepeatPattern,_tmpReminderType,_tmpReminderOffset);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TimeSlotEntity>> getAllTimeSlots() {
    final String _sql = "SELECT * FROM time_slot";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_slot"}, new Callable<List<TimeSlotEntity>>() {
      @Override
      @NonNull
      public List<TimeSlotEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "end_time");
          final int _cursorIndexOfScheduleType = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_type");
          final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
          final int _cursorIndexOfHead = CursorUtil.getColumnIndexOrThrow(_cursor, "head");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfIsRepeated = CursorUtil.getColumnIndexOrThrow(_cursor, "is_repeated");
          final int _cursorIndexOfRepeatPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "repeat_pattern");
          final int _cursorIndexOfReminderType = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_type");
          final int _cursorIndexOfReminderOffset = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_offset");
          final List<TimeSlotEntity> _result = new ArrayList<TimeSlotEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TimeSlotEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final ScheduleType _tmpScheduleType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfScheduleType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfScheduleType);
            }
            _tmpScheduleType = __converters.toScheduleType(_tmp);
            final int _tmpScheduleId;
            _tmpScheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
            final String _tmpHead;
            if (_cursor.isNull(_cursorIndexOfHead)) {
              _tmpHead = null;
            } else {
              _tmpHead = _cursor.getString(_cursorIndexOfHead);
            }
            final Integer _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final boolean _tmpIsRepeated;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsRepeated);
            _tmpIsRepeated = _tmp_2 != 0;
            final String _tmpRepeatPattern;
            if (_cursor.isNull(_cursorIndexOfRepeatPattern)) {
              _tmpRepeatPattern = null;
            } else {
              _tmpRepeatPattern = _cursor.getString(_cursorIndexOfRepeatPattern);
            }
            final ReminderType _tmpReminderType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfReminderType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfReminderType);
            }
            _tmpReminderType = __converters.toReminderType(_tmp_3);
            final Long _tmpReminderOffset;
            if (_cursor.isNull(_cursorIndexOfReminderOffset)) {
              _tmpReminderOffset = null;
            } else {
              _tmpReminderOffset = _cursor.getLong(_cursorIndexOfReminderOffset);
            }
            _item = new TimeSlotEntity(_tmpId,_tmpUserId,_tmpStartTime,_tmpEndTime,_tmpScheduleType,_tmpScheduleId,_tmpHead,_tmpPriority,_tmpIsCompleted,_tmpIsRepeated,_tmpRepeatPattern,_tmpReminderType,_tmpReminderOffset);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TimeSlotEntity>> getTimeSlotsInRange(final long startTimeMillis,
      final long endTimeMillis) {
    final String _sql = "SELECT * FROM time_slot WHERE start_time >= ? AND end_time <= ? ORDER BY start_time ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTimeMillis);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTimeMillis);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_slot"}, new Callable<List<TimeSlotEntity>>() {
      @Override
      @NonNull
      public List<TimeSlotEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "end_time");
          final int _cursorIndexOfScheduleType = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_type");
          final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
          final int _cursorIndexOfHead = CursorUtil.getColumnIndexOrThrow(_cursor, "head");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfIsRepeated = CursorUtil.getColumnIndexOrThrow(_cursor, "is_repeated");
          final int _cursorIndexOfRepeatPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "repeat_pattern");
          final int _cursorIndexOfReminderType = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_type");
          final int _cursorIndexOfReminderOffset = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_offset");
          final List<TimeSlotEntity> _result = new ArrayList<TimeSlotEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TimeSlotEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final ScheduleType _tmpScheduleType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfScheduleType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfScheduleType);
            }
            _tmpScheduleType = __converters.toScheduleType(_tmp);
            final int _tmpScheduleId;
            _tmpScheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
            final String _tmpHead;
            if (_cursor.isNull(_cursorIndexOfHead)) {
              _tmpHead = null;
            } else {
              _tmpHead = _cursor.getString(_cursorIndexOfHead);
            }
            final Integer _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final boolean _tmpIsRepeated;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsRepeated);
            _tmpIsRepeated = _tmp_2 != 0;
            final String _tmpRepeatPattern;
            if (_cursor.isNull(_cursorIndexOfRepeatPattern)) {
              _tmpRepeatPattern = null;
            } else {
              _tmpRepeatPattern = _cursor.getString(_cursorIndexOfRepeatPattern);
            }
            final ReminderType _tmpReminderType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfReminderType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfReminderType);
            }
            _tmpReminderType = __converters.toReminderType(_tmp_3);
            final Long _tmpReminderOffset;
            if (_cursor.isNull(_cursorIndexOfReminderOffset)) {
              _tmpReminderOffset = null;
            } else {
              _tmpReminderOffset = _cursor.getLong(_cursorIndexOfReminderOffset);
            }
            _item = new TimeSlotEntity(_tmpId,_tmpUserId,_tmpStartTime,_tmpEndTime,_tmpScheduleType,_tmpScheduleId,_tmpHead,_tmpPriority,_tmpIsCompleted,_tmpIsRepeated,_tmpRepeatPattern,_tmpReminderType,_tmpReminderOffset);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TimeSlotEntity>> getTimeSlotsBySchedule(final ScheduleType scheduleType,
      final int scheduleId) {
    final String _sql = "SELECT * FROM time_slot WHERE schedule_type = ? AND schedule_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final String _tmp = __converters.fromScheduleType(scheduleType);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, scheduleId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_slot"}, new Callable<List<TimeSlotEntity>>() {
      @Override
      @NonNull
      public List<TimeSlotEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "end_time");
          final int _cursorIndexOfScheduleType = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_type");
          final int _cursorIndexOfScheduleId = CursorUtil.getColumnIndexOrThrow(_cursor, "schedule_id");
          final int _cursorIndexOfHead = CursorUtil.getColumnIndexOrThrow(_cursor, "head");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfIsRepeated = CursorUtil.getColumnIndexOrThrow(_cursor, "is_repeated");
          final int _cursorIndexOfRepeatPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "repeat_pattern");
          final int _cursorIndexOfReminderType = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_type");
          final int _cursorIndexOfReminderOffset = CursorUtil.getColumnIndexOrThrow(_cursor, "reminder_offset");
          final List<TimeSlotEntity> _result = new ArrayList<TimeSlotEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TimeSlotEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final ScheduleType _tmpScheduleType;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfScheduleType)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfScheduleType);
            }
            _tmpScheduleType = __converters.toScheduleType(_tmp_1);
            final int _tmpScheduleId;
            _tmpScheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
            final String _tmpHead;
            if (_cursor.isNull(_cursorIndexOfHead)) {
              _tmpHead = null;
            } else {
              _tmpHead = _cursor.getString(_cursorIndexOfHead);
            }
            final Integer _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final boolean _tmpIsRepeated;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRepeated);
            _tmpIsRepeated = _tmp_3 != 0;
            final String _tmpRepeatPattern;
            if (_cursor.isNull(_cursorIndexOfRepeatPattern)) {
              _tmpRepeatPattern = null;
            } else {
              _tmpRepeatPattern = _cursor.getString(_cursorIndexOfRepeatPattern);
            }
            final ReminderType _tmpReminderType;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfReminderType)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfReminderType);
            }
            _tmpReminderType = __converters.toReminderType(_tmp_4);
            final Long _tmpReminderOffset;
            if (_cursor.isNull(_cursorIndexOfReminderOffset)) {
              _tmpReminderOffset = null;
            } else {
              _tmpReminderOffset = _cursor.getLong(_cursorIndexOfReminderOffset);
            }
            _item = new TimeSlotEntity(_tmpId,_tmpUserId,_tmpStartTime,_tmpEndTime,_tmpScheduleType,_tmpScheduleId,_tmpHead,_tmpPriority,_tmpIsCompleted,_tmpIsRepeated,_tmpRepeatPattern,_tmpReminderType,_tmpReminderOffset);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
