// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.course.detail;

import com.example.todoschedule.domain.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CourseDetailViewModel_Factory implements Factory<CourseDetailViewModel> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public CourseDetailViewModel_Factory(Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public CourseDetailViewModel get() {
    return newInstance(courseRepositoryProvider.get());
  }

  public static CourseDetailViewModel_Factory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new CourseDetailViewModel_Factory(courseRepositoryProvider);
  }

  public static CourseDetailViewModel newInstance(CourseRepository courseRepository) {
    return new CourseDetailViewModel(courseRepository);
  }
}
