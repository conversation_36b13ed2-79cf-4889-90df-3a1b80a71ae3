// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.todo;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TodoViewModel_Factory implements Factory<TodoViewModel> {
  @Override
  public TodoViewModel get() {
    return newInstance();
  }

  public static TodoViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TodoViewModel newInstance() {
    return new TodoViewModel();
  }

  private static final class InstanceHolder {
    private static final TodoViewModel_Factory INSTANCE = new TodoViewModel_Factory();
  }
}
