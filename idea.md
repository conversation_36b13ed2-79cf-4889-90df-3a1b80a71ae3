# TodoSchedule数据同步实现方案详细说明

## 一、背景与目标

TodoSchedule是一个课程和日程管理应用，需要实现跨设备数据同步功能，让用户在不同设备上都能获取最新数据。在这个过程中，我们面临几个主要挑战：

1. **离线使用**：用户在无网络环境下仍需正常使用全部功能。
2. **数据冲突**：用户可能在多个设备上同时修改同一数据。
3. **网络不稳定**：需要处理网络断连、重连情况下的数据一致性。
4. **数据安全**：确保只有授权用户能访问其数据。

为解决这些问题，我们选择Synk库作为核心技术，它是基于CRDT（冲突无关数据类型）的分布式数据同步解决方案。

## 二、Synk库原理解析

### 1. CRDT基本原理

CRDT是一种特殊的数据结构，它能够在分布式系统中自动解决冲突，实现最终一致性。与传统的中央服务器模型不同，CRDT允许每个节点独立更新数据，后续通过特定算法将这些更新合并，且保证最终结果一致。

Synk库实现了CRDT的理念，主要基于以下原则：

- 所有操作都是可交换的（顺序无关）
- 操作是单调的（信息只增不减）
- 操作具有幂等性（重复应用同一操作不会改变结果）

### 2. Synk库核心组件

Synk库由以下核心组件构成：

- **混合逻辑时钟(HLC)**：为每个操作提供全局顺序，解决并发冲突。
- **MetaStore**：存储数据元信息和操作历史。
- **SynkAdapter**：将应用数据类型转换为Synk可处理的格式。
- **消息系统**：负责传输CRDT操作消息。

### 3. 数据流转过程

在Synk中，数据同步过程如下：

1. 本地修改生成出站消息(Outbound)
2. 消息通过网络传输到其他节点
3. 接收节点处理入站消息(Inbound)
4. 根据CRDT规则解决可能的冲突
5. 将合并结果应用到本地存储

## 三、客户端实现详细设计

### 1. 整体架构

客户端同步模块分为五层：

**数据层**：

- 扩展现有实体类，添加CRDT所需字段
- 实现支持CRDT操作的DAO接口

**同步核心层**：

- Synk初始化与配置
- SynkAdapter实现
- 冲突解决逻辑

**消息处理层**：

- 消息队列管理
- 消息序列化与反序列化
- 消息发送与接收逻辑

**网络交互层**：

- 与服务器API通信
- 网络状态监控
- 重试与错误处理

**协调管理层**：

- 同步触发机制
- 设备管理
- 用户认证与权限控制

### 2. 实体扩展设计

所有需要同步的实体类都需要扩展以下字段：

- **全局唯一标识符(crdtKey)**：确保在分布式系统中唯一识别实体
- **逻辑删除标记(isDeleted)**：支持软删除而非物理删除
- **版本时间戳(hlcTimestamp)**：记录最后修改时间，用于解决冲突
- **来源设备ID(originDeviceId)**：标识数据来源
- **操作类型(operationType)**：ADD、UPDATE或DELETE

例如，课程实体将包含：

- 原有业务字段：id、name、lecturer、location等
- CRDT字段：crdtKey、isDeleted、hlcTimestamp、originDeviceId

### 3. SynkAdapter详细设计

为每种实体类型实现专用的SynkAdapter，负责：

1. **提取键值**：从实体中提取唯一标识符
2. **序列化**：将实体转换为Map结构
3. **反序列化**：将Map结构恢复为实体
4. **合并策略**：定义如何解决字段级别的冲突

这些适配器需要处理的实体类型包括：

- 课程(Course)
- 普通日程(OrdinarySchedule)
- 课表(Table)
- 时间段(TimeSlot)
- 课表时间配置(TableTimeConfig)
- 全局表设置(GlobalTableSetting)

适配器会根据字段特性使用不同的合并策略：

- 简单字段（如name）：以最新修改为准
- 数值字段（如priority）：根据业务规则选择最大值或最小值
- 布尔字段（如isCompleted）：通常以true优先
- 复杂字段：可能需要字段级的合并逻辑

### 4. 本地存储与变更跟踪

本地数据变更需要详细跟踪，包括：

1. **变更检测**：

   - 在所有修改操作（增、删、改）时记录变更
   - 记录变更前后的完整状态，用于生成CRDT消息
2. **变更队列**：

   - 未同步的变更保存在本地队列
   - 队列包含：实体类型、操作类型、变更前数据、变更后数据、时间戳
   - 支持持久化，以便应用重启后继续同步
3. **业务逻辑适配**：

   - 修改现有Repository类，在数据操作后生成Synk消息
   - 确保数据库操作和Synk操作在同一事务中
   - 处理软删除逻辑（标记删除而非物理删除）

### 5. 消息同步服务详细设计

**消息队列管理**：

- 使用Room数据库存储待发送消息
- 消息表包含：ID、实体类型、内容、时间戳、同步状态
- 支持按优先级排序和批量处理

**消息发送流程**：

1. 用户操作触发数据变更
2. 生成CRDT消息并保存到本地队列
3. 检查网络状态，决定是否立即发送
4. 发送成功后标记消息为已同步
5. 网络异常时保留消息，等待下次同步

**消息接收流程**：

1. 定期或由事件触发从服务器获取新消息
2. 按实体类型和时间戳排序处理消息
3. 对每条消息进行CRDT合并
4. 将合并结果应用到本地数据库
5. 更新最后同步时间戳

**同步触发机制**：

- 应用启动时
- 网络状态变化时
- 用户手动刷新时
- 定时触发（如每15分钟）
- 重要数据变更后立即触发

### 6. 网络层详细设计

**API接口**：

1. **设备注册**：向服务器登记当前设备

   - 请求：设备ID、设备名称
   - 响应：注册状态、上次同步时间戳
2. **消息上传**：将本地变更发送到服务器

   - 请求：实体类型、设备ID、消息列表
   - 响应：成功/失败状态
3. **消息下载**：获取其他设备的变更

   - 请求：设备ID、最后同步时间戳
   - 响应：消息列表

**网络状态管理**：

- 实现ConnectivityManager监听器
- 网络恢复时自动触发同步
- 支持细粒度的网络类型判断（WiFi/移动数据）

**鉴权与安全**：

- 所有API请求携带用户认证令牌
- 设备ID在请求头中传递
- 支持令牌过期刷新机制

### 7. 设备管理详细设计

**设备标识**：

- 首次启动时生成UUID作为设备ID
- 使用DataStore安全存储设备ID
- 设备信息包含：ID、名称、厂商、型号

**设备注册流程**：

1. 用户登录后检查设备是否已注册
2. 未注册则生成设备ID并向服务器注册
3. 注册成功后保存设备ID和初始同步时间戳
4. 注册失败时实现指数退避重试

**设备同步状态管理**：

- 记录每台设备的最后同步时间
- 支持按设备筛选未同步的变更
- 记录与每台设备的连接历史

### 8. 冲突解决策略详细设计

Synk基于CRDT自动处理大部分冲突，但仍需定义一些业务规则：

**基础冲突解决**：

- 使用HLC时间戳确定优先级
- 同时考虑操作类型的优先级（删除通常优先于更新）

**实体级冲突策略**：

1. **课程(Course)**：

   - 课程名称冲突：选择非空且最新的版本
   - 时间冲突：保留较新的修改
   - 地点冲突：保留非空且最新的版本
2. **日程(OrdinarySchedule)**：

   - 标题冲突：选择非空且最新的版本
   - 完成状态冲突：如果有一个标记为完成，则合并为完成
   - 优先级冲突：选择最高优先级
3. **课表(Table)**：

   - 名称冲突：选择非空且最新的版本
   - 开始日期冲突：保持一致，必要时重新计算相关日期

**复杂场景处理**：

- 级联删除：处理父实体删除后子实体的状态
- 引用一致性：确保关联实体修改后引用关系保持一致
- 业务规则验证：合并后确保不违反业务规则（如时间段不重叠）

## 四、服务器实现详细设计

### 1. 服务器架构

服务器采用中继器架构，不直接参与CRDT操作，仅负责：

1. 消息的存储与转发
2. 用户认证与授权
3. 设备注册与管理
4. 消息持久化与查询

这种架构的优势是：

- 服务器无需理解消息内容
- 可以轻松扩展到多种实体类型
- 降低服务器处理复杂度

### 2. 数据库设计

服务器数据库包含以下核心表：

**用户表(users)**：

- id: 用户唯一标识
- username: 用户名
- password_hash: 密码哈希
- email: 电子邮件
- phone: 电话号码
- created_at: 创建时间
- updated_at: 更新时间

**设备表(devices)**：

- id: 设备唯一标识（UUID）
- user_id: 关联的用户ID
- name: 设备名称
- last_sync_timestamp: 最后同步时间戳
- created_at: 注册时间
- updated_at: 最后活动时间

**消息表(sync_messages)**：

- id: 消息唯一标识
- user_id: 消息所属用户ID
- entity_type: 实体类型（如"Course"）
- entity_key: 实体唯一标识
- message_data: 消息内容（JSON格式）
- timestamp: 消息时间戳
- origin_device_id: 来源设备ID
- created_at: 服务器接收时间

**索引设计**：

- user_id + entity_type + timestamp（优化下载查询）
- user_id + origin_device_id（设备特定查询）
- entity_type + entity_key（实体更新查询）

### 3. API接口详细设计

**设备注册API**：

- 端点：POST /sync/device/register
- 请求体：{deviceId: string, deviceName: string}
- 响应：{id: string, userId: number, name: string, lastSyncTimestamp: number}
- 描述：注册新设备或更新现有设备信息

**消息上传API**：

- 端点：POST /sync/messages/{entityType}
- 请求头：X-Device-ID: 设备ID, Authorization: Bearer {token}
- 请求体：[消息1, 消息2, ...]（JSON字符串数组）
- 响应：{success: boolean, message: string}
- 描述：批量上传特定实体类型的CRDT消息

**消息下载API（全量）**：

- 端点：GET /sync/messages
- 请求头：X-Device-ID: 设备ID, Authorization: Bearer {token}
- 参数：since (可选，时间戳)
- 响应：[{id, userId, entityType, entityKey, messageData, timestamp, originDeviceId}, ...]
- 描述：下载自特定时间戳后的所有消息

### 4. 安全设计

**身份验证**：

- 基于JWT的认证机制
- 令牌包含用户ID和权限信息
- 令牌有效期管理和刷新机制

**授权控制**：

- 验证用户只能访问自己的数据
- 设备ID验证，防止冒充其他设备
- API访问频率限制，防止滥用

**数据安全**：

- 消息内容传输加密（HTTPS）
- 敏感字段加密存储
- 日志脱敏处理

### 5. 消息处理流程

**消息接收流程**：

1. 验证请求的身份和权限
2. 解析消息批次
3. 验证每条消息的格式和完整性
4. 为每条消息生成服务器ID
5. 记录消息的接收时间
6. 批量保存到数据库
7. 返回处理结果

**消息分发流程**：

1. 接收客户端下载请求
2. 根据设备ID和时间戳筛选消息
3. 排除设备自己上传的消息（可选）
4. 按时间戳排序消息
5. 分页返回结果（如果数量大）
6. 更新设备的最后同步时间

**消息优化**：

- 合并相同实体的连续操作
- 删除被更高版本覆盖的消息
- 定期压缩历史消息
- 为大量消息提供分页机制

## 五、集成与测试计划

### 1. 集成步骤

将Synk集成到现有应用需要以下步骤：

**第一阶段：基础设施准备**

1. 添加Synk库依赖
2. 创建数据库迁移脚本，扩展实体字段
3. 实现服务器API端点
4. 配置网络监听服务

**第二阶段：单向同步**

1. 实现SynkAdapter
2. 开发消息生成逻辑
3. 实现上传功能，但不处理下载
4. 测试数据从设备到服务器的传输

**第三阶段：完整同步**

1. 实现消息下载和处理
2. 开发冲突解决策略
3. 集成到现有Repository层
4. 启用自动同步触发机制

**第四阶段：用户界面与体验**

1. 添加同步状态指示器
2. 实现手动同步触发
3. 开发冲突提示（必要时）
4. 完善错误处理和反馈机制

### 2. 测试策略

**单元测试**：

- SynkAdapter的序列化和反序列化
- 冲突解决策略
- Repository层的CRDT集成
- 消息队列管理

**集成测试**：

- 客户端与服务器通信
- 完整同步流程
- 网络异常处理
- 设备注册流程

**场景测试**：

- 多设备并发修改同一数据
- 离线使用后重新联网
- 不同网络条件下的同步性能
- 大量数据初次同步

**性能测试**：

- 同步操作内存占用
- 电池消耗评估
- 网络带宽使用
- 数据库操作效率

### 3. 部署与监控

**滚动部署**：

1. 先升级服务器
2. 再分批更新客户端
3. 保持向下兼容性

**运行监控**：

- 跟踪同步成功率
- 监控消息队列大小
- 分析冲突发生频率
- 设备连接统计

## 六、风险与挑战

### 1. 技术风险

**数据量挑战**：

- 随时间增长的消息数据库大小
- 需要实现消息压缩和清理策略
- 可能需要分片存储机制

**性能考量**：

- CRDT操作可能在大规模数据上较慢
- 需要优化合并算法和存储访问
- 批量处理可能引入延迟

**兼容性问题**：

- 不同版本客户端的消息格式兼容
- 模式演化时的数据迁移
- API向后兼容性保证

### 2. 用户体验风险

**同步不透明**：

- 用户可能不理解同步状态
- 需要清晰指示同步进度和结果
- 可能需要解释冲突解决决策

**数据一致性感知**：

- 用户期望即时同步
- 处理网络延迟导致的暂时不一致
- 确保关键操作的可见反馈

### 3. 缓解策略

**技术缓解**：

- 实现消息批处理和压缩
- 使用增量同步减少数据传输
- 设计版本兼容的消息格式

**用户体验缓解**：

- 提供清晰的同步状态指示
- 实现自动和手动同步选项
- 在关键操作后提供确认

## 七、后续优化方向

### 1. 高级功能

**选择性同步**：

- 允许用户选择需要同步的数据类型
- 支持特定时间范围的数据筛选
- 按优先级调整同步顺序

**冲突提示与手动解决**：

- 检测需要用户介入的复杂冲突
- 提供直观的冲突解决界面
- 记忆用户的解决偏好

**同步历史与回滚**：

- 查看数据变更历史
- 支持回滚到特定版本
- 审计重要数据的修改

### 2. 性能优化

**智能同步调度**：

- 基于电池、网络和使用模式调整同步频率
- 推迟非关键数据的同步
- 支持高优先级数据的即时同步

**存储优化**：

- 压缩历史消息
- 定期清理过时数据
- 根据访问频率优化本地存储

**带宽优化**：

- 消息差异化编码
- 压缩传输内容
- 批量处理小更新

### 3. 扩展应用

**多用户协作**：

- 支持数据共享和协作编辑
- 细粒度权限控制
- 实时编辑通知

**离线功能增强**：

- 更复杂的离线操作支持
- 智能冲突预测和避免
- 基于用户行为的预加载

## 八、总结

基于Synk库的数据同步方案为TodoSchedule应用提供了强大的离线优先能力，通过CRDT技术实现了可靠的数据一致性保障。该方案的主要特点包括：

1. **分布式架构**：无中心化设计，每个设备都是平等的节点。
2. **冲突自动解决**：使用CRDT技术处理并发编辑冲突。
3. **离线优先**：完整支持离线操作，网络恢复后自动同步。
4. **可扩展性**：适用于不同类型的实体和业务逻辑。
5. **用户透明**：同步过程对用户几乎无感知。

通过这套设计，TodoSchedule将能为用户提供流畅的跨设备体验，无论是在校园内、家中还是通勤途中，都能保持数据的一致性和可用性。

是的，服务器可以作为特殊节点实现微信提醒功能，同时保持Synk库的分布式特性。Synk设计上是无中心化的，但这不妨碍将服务器配置为"观察者节点"，它不主动生成CRDT消息，只消费同步数据。

实现方案如下：

1. 服务器作为特殊"只读节点"接收所有同步消息
2. 服务器维护自己的数据库，应用CRDT合并规则
3. 服务器不生成新的消息或修改数据
4. 基于接收到的数据构建课程、日程和时间槽信息
5. 使用这些数据触发微信提醒功能

这符合Synk的设计理念，因为：

- 保持了"消息必须传递给所有节点"的原则
- 服务器只是一个消费数据的特殊节点
- 不破坏CRDT的一致性保证

实现上需要：

1. 服务器实现相同的SynkAdapter解析逻辑
2. 建立必要的数据模型映射微信提醒所需信息
3. 基于时间触发器检查需要发送的提醒

这种方案完全可行，且不违背Synk的设计原则。

是的，服务器完全可以发送Sync消息。根据Synk文档中的第二个图表，展示了服务器和客户端之间的消息流动模式：

```
 Server--InboundMessage-->ClientA;
 Server--InboundMessage-->ClientB;
 ClientA--OutboundMessage-->Server;
 ClientB--OutboundMessage-->Server;
```

这表明服务器可以作为一个完整的节点参与CRDT消息的生成和发送。在你的扩展业务场景中，实现方式如下：

1. 服务器接收用户通过微信或其他渠道发送的日程信息
2. 服务器解析这些信息，创建日程实体
3. 服务器生成CRDT消息，包含必要的字段（全局唯一标识符、时间戳等）
4. 服务器将消息保存并分发给用户的所有设备

服务器生成的消息需要包含：

- crdtKey（全局唯一标识符）
- hlcTimestamp（逻辑时钟时间戳）
- 标识服务器作为originDeviceId
- operationType（通常是ADD）
- 日程的实际内容数据

这种方式完全符合Synk库的设计理念，服务器此时作为一个可以主动生成CRDT消息的节点，参与分布式系统的数据流转。

当用户设备联网时，它们会像接收其他设备的消息一样接收并处理服务器生成的消息，保持数据一致性。
