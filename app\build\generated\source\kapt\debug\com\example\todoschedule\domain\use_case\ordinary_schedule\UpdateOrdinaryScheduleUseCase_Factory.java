// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.ordinary_schedule;

import com.example.todoschedule.domain.repository.OrdinaryScheduleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateOrdinaryScheduleUseCase_Factory implements Factory<UpdateOrdinaryScheduleUseCase> {
  private final Provider<OrdinaryScheduleRepository> repositoryProvider;

  public UpdateOrdinaryScheduleUseCase_Factory(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public UpdateOrdinaryScheduleUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static UpdateOrdinaryScheduleUseCase_Factory create(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    return new UpdateOrdinaryScheduleUseCase_Factory(repositoryProvider);
  }

  public static UpdateOrdinaryScheduleUseCase newInstance(OrdinaryScheduleRepository repository) {
    return new UpdateOrdinaryScheduleUseCase(repository);
  }
}
