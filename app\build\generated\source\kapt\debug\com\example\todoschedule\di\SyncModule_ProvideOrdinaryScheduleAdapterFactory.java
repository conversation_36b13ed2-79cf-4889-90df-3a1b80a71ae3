// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideOrdinaryScheduleAdapterFactory implements Factory<OrdinaryScheduleAdapter> {
  @Override
  public OrdinaryScheduleAdapter get() {
    return provideOrdinaryScheduleAdapter();
  }

  public static SyncModule_ProvideOrdinaryScheduleAdapterFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static OrdinaryScheduleAdapter provideOrdinaryScheduleAdapter() {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideOrdinaryScheduleAdapter());
  }

  private static final class InstanceHolder {
    private static final SyncModule_ProvideOrdinaryScheduleAdapterFactory INSTANCE = new SyncModule_ProvideOrdinaryScheduleAdapterFactory();
  }
}
