// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.ordinary_schedule;

import com.example.todoschedule.domain.repository.OrdinaryScheduleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddOrdinaryScheduleUseCase_Factory implements Factory<AddOrdinaryScheduleUseCase> {
  private final Provider<OrdinaryScheduleRepository> repositoryProvider;

  public AddOrdinaryScheduleUseCase_Factory(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public AddOrdinaryScheduleUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static AddOrdinaryScheduleUseCase_Factory create(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    return new AddOrdinaryScheduleUseCase_Factory(repositoryProvider);
  }

  public static AddOrdinaryScheduleUseCase newInstance(OrdinaryScheduleRepository repository) {
    return new AddOrdinaryScheduleUseCase(repository);
  }
}
