package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.entity.GlobalTableSettingEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class GlobalSettingDao_Impl implements GlobalSettingDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<GlobalTableSettingEntity> __insertionAdapterOfGlobalTableSettingEntity;

  private final EntityDeletionOrUpdateAdapter<GlobalTableSettingEntity> __updateAdapterOfGlobalTableSettingEntity;

  public GlobalSettingDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfGlobalTableSettingEntity = new EntityInsertionAdapter<GlobalTableSettingEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `global_table_setting` (`id`,`userId`,`defaultTableIds`,`showWeekend`,`courseNotificationStyle`,`notifyBeforeMinutes`,`autoSwitchWeek`,`showCourseTime`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GlobalTableSettingEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getDefaultTableIds() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDefaultTableIds());
        }
        final int _tmp = entity.getShowWeekend() ? 1 : 0;
        statement.bindLong(4, _tmp);
        statement.bindLong(5, entity.getCourseNotificationStyle());
        statement.bindLong(6, entity.getNotifyBeforeMinutes());
        final int _tmp_1 = entity.getAutoSwitchWeek() ? 1 : 0;
        statement.bindLong(7, _tmp_1);
        final int _tmp_2 = entity.getShowCourseTime() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
      }
    };
    this.__updateAdapterOfGlobalTableSettingEntity = new EntityDeletionOrUpdateAdapter<GlobalTableSettingEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `global_table_setting` SET `id` = ?,`userId` = ?,`defaultTableIds` = ?,`showWeekend` = ?,`courseNotificationStyle` = ?,`notifyBeforeMinutes` = ?,`autoSwitchWeek` = ?,`showCourseTime` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GlobalTableSettingEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getDefaultTableIds() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDefaultTableIds());
        }
        final int _tmp = entity.getShowWeekend() ? 1 : 0;
        statement.bindLong(4, _tmp);
        statement.bindLong(5, entity.getCourseNotificationStyle());
        statement.bindLong(6, entity.getNotifyBeforeMinutes());
        final int _tmp_1 = entity.getAutoSwitchWeek() ? 1 : 0;
        statement.bindLong(7, _tmp_1);
        final int _tmp_2 = entity.getShowCourseTime() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        statement.bindLong(9, entity.getId());
      }
    };
  }

  @Override
  public Object insertGlobalSetting(final GlobalTableSettingEntity globalSetting,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfGlobalTableSettingEntity.insertAndReturnId(globalSetting);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateGlobalSetting(final GlobalTableSettingEntity globalSetting,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfGlobalTableSettingEntity.handle(globalSetting);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<GlobalTableSettingEntity> getGlobalSettingByUserId(final int userId) {
    final String _sql = "SELECT * FROM global_table_setting WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"global_table_setting"}, new Callable<GlobalTableSettingEntity>() {
      @Override
      @Nullable
      public GlobalTableSettingEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfDefaultTableIds = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTableIds");
          final int _cursorIndexOfShowWeekend = CursorUtil.getColumnIndexOrThrow(_cursor, "showWeekend");
          final int _cursorIndexOfCourseNotificationStyle = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNotificationStyle");
          final int _cursorIndexOfNotifyBeforeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "notifyBeforeMinutes");
          final int _cursorIndexOfAutoSwitchWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "autoSwitchWeek");
          final int _cursorIndexOfShowCourseTime = CursorUtil.getColumnIndexOrThrow(_cursor, "showCourseTime");
          final GlobalTableSettingEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpDefaultTableIds;
            if (_cursor.isNull(_cursorIndexOfDefaultTableIds)) {
              _tmpDefaultTableIds = null;
            } else {
              _tmpDefaultTableIds = _cursor.getString(_cursorIndexOfDefaultTableIds);
            }
            final boolean _tmpShowWeekend;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfShowWeekend);
            _tmpShowWeekend = _tmp != 0;
            final int _tmpCourseNotificationStyle;
            _tmpCourseNotificationStyle = _cursor.getInt(_cursorIndexOfCourseNotificationStyle);
            final int _tmpNotifyBeforeMinutes;
            _tmpNotifyBeforeMinutes = _cursor.getInt(_cursorIndexOfNotifyBeforeMinutes);
            final boolean _tmpAutoSwitchWeek;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfAutoSwitchWeek);
            _tmpAutoSwitchWeek = _tmp_1 != 0;
            final boolean _tmpShowCourseTime;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfShowCourseTime);
            _tmpShowCourseTime = _tmp_2 != 0;
            _result = new GlobalTableSettingEntity(_tmpId,_tmpUserId,_tmpDefaultTableIds,_tmpShowWeekend,_tmpCourseNotificationStyle,_tmpNotifyBeforeMinutes,_tmpAutoSwitchWeek,_tmpShowCourseTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getGlobalSettingById(final int id,
      final Continuation<? super GlobalTableSettingEntity> $completion) {
    final String _sql = "SELECT * FROM global_table_setting WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<GlobalTableSettingEntity>() {
      @Override
      @Nullable
      public GlobalTableSettingEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfDefaultTableIds = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTableIds");
          final int _cursorIndexOfShowWeekend = CursorUtil.getColumnIndexOrThrow(_cursor, "showWeekend");
          final int _cursorIndexOfCourseNotificationStyle = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNotificationStyle");
          final int _cursorIndexOfNotifyBeforeMinutes = CursorUtil.getColumnIndexOrThrow(_cursor, "notifyBeforeMinutes");
          final int _cursorIndexOfAutoSwitchWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "autoSwitchWeek");
          final int _cursorIndexOfShowCourseTime = CursorUtil.getColumnIndexOrThrow(_cursor, "showCourseTime");
          final GlobalTableSettingEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpDefaultTableIds;
            if (_cursor.isNull(_cursorIndexOfDefaultTableIds)) {
              _tmpDefaultTableIds = null;
            } else {
              _tmpDefaultTableIds = _cursor.getString(_cursorIndexOfDefaultTableIds);
            }
            final boolean _tmpShowWeekend;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfShowWeekend);
            _tmpShowWeekend = _tmp != 0;
            final int _tmpCourseNotificationStyle;
            _tmpCourseNotificationStyle = _cursor.getInt(_cursorIndexOfCourseNotificationStyle);
            final int _tmpNotifyBeforeMinutes;
            _tmpNotifyBeforeMinutes = _cursor.getInt(_cursorIndexOfNotifyBeforeMinutes);
            final boolean _tmpAutoSwitchWeek;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfAutoSwitchWeek);
            _tmpAutoSwitchWeek = _tmp_1 != 0;
            final boolean _tmpShowCourseTime;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfShowCourseTime);
            _tmpShowCourseTime = _tmp_2 != 0;
            _result = new GlobalTableSettingEntity(_tmpId,_tmpUserId,_tmpDefaultTableIds,_tmpShowWeekend,_tmpCourseNotificationStyle,_tmpNotifyBeforeMinutes,_tmpAutoSwitchWeek,_tmpShowCourseTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object hasGlobalSetting(final int userId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM global_table_setting WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
