%com.example.todoschedule.MainActivity.com.example.todoschedule.MainActivityViewModel0com.example.todoschedule.TodoScheduleApplication2com.example.todoschedule.data.database.AppDatabase?com.example.todoschedule.data.database.converter.ScheduleStatus=com.example.todoschedule.data.database.converter.ScheduleType=com.example.todoschedule.data.database.converter.ReminderType:com.example.todoschedule.data.database.entity.CourseEntity>com.example.todoschedule.data.database.entity.CourseNodeEntityDcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity9com.example.todoschedule.data.database.entity.TableEntity=com.example.todoschedule.data.repository.CourseRepositoryImplDcom.example.todoschedule.data.repository.GlobalSettingRepositoryImplGcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImplAcom.example.todoschedule.data.repository.RemoteUserRepositoryImpl>com.example.todoschedule.data.repository.SessionRepositoryImpl;com.example.todoschedule.data.repository.SyncRepositoryImpl<com.example.todoschedule.data.repository.TableRepositoryImplFcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl;com.example.todoschedule.data.repository.UserRepositoryImpl.com.example.todoschedule.data.sync.SyncApiImpl;com.example.todoschedule.data.sync.SyncConstants.EntityType;com.example.todoschedule.data.sync.SyncConstants.SyncStatus8com.example.todoschedule.data.sync.SyncManager.SyncState.com.example.todoschedule.data.sync.SyncService9com.example.todoschedule.data.sync.SyncService.SyncBinder-com.example.todoschedule.data.sync.SyncWorker8com.example.todoschedule.data.sync.adapter.CourseAdapter<com.example.todoschedule.data.sync.adapter.CourseNodeAdapterBcom.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter>com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter7com.example.todoschedule.data.sync.adapter.TableAdapter5com.example.todoschedule.data.sync.dto.SyncMessageDtoAcom.example.todoschedule.data.sync.dto.SyncMessageDto.$serializer=com.example.todoschedule.data.sync.dto.TimestampDtoSerializerHcom.example.todoschedule.data.sync.dto.DeviceRegistrationDto.$serializerPcom.example.todoschedule.data.sync.dto.DeviceRegistrationResponseDto.$serializer3com.example.todoschedule.data.sync.dto.TimestampDto?com.example.todoschedule.data.sync.dto.TimestampDto.$serializerBcom.example.todoschedule.data.sync.dto.SyncMessagesDto.$serializerHcom.example.todoschedule.data.sync.dto.UploadSyncResponseDto.$serializer(com.example.todoschedule.ui.MainActivity/com.example.todoschedule.ui.auth.LoginViewModel2com.example.todoschedule.ui.auth.RegisterViewModelEcom.example.todoschedule.ui.components.CalendarPermissionTextProvider9com.example.todoschedule.ui.course.add.AddCourseViewModel5com.example.todoschedule.ui.course.add.SaveState.Idle8com.example.todoschedule.ui.course.add.SaveState.Loading8com.example.todoschedule.ui.course.add.SaveState.Success6com.example.todoschedule.ui.course.add.SaveState.ErrorEcom.example.todoschedule.ui.course.detail.CourseDetailUiState.LoadingEcom.example.todoschedule.ui.course.detail.CourseDetailUiState.SuccessCcom.example.todoschedule.ui.course.detail.CourseDetailUiState.ErrorEcom.example.todoschedule.ui.course.detail.CourseDetailUiState.Deleted?com.example.todoschedule.ui.course.detail.CourseDetailViewModelAcom.example.todoschedule.ui.course.edit.EditCourseEvent.ShowErrorEcom.example.todoschedule.ui.course.edit.EditCourseEvent.CourseUpdated9com.example.todoschedule.ui.course.edit.EditCourseUiState;com.example.todoschedule.ui.course.edit.EditCourseViewModel7com.example.todoschedule.ui.course.load.SchoolViewModel<com.example.todoschedule.ui.course.load.SaveCourseState.Idle>com.example.todoschedule.ui.course.load.SaveCourseState.Saving?com.example.todoschedule.ui.course.load.SaveCourseState.Success=com.example.todoschedule.ui.course.load.SaveCourseState.Error>com.example.todoschedule.ui.course.load.WebViewScreenViewModel.com.example.todoschedule.ui.home.HomeViewModel7com.example.todoschedule.ui.navigation.SessionViewModel5com.example.todoschedule.ui.navigation.AppRoutes.Home9com.example.todoschedule.ui.navigation.AppRoutes.Schedule5com.example.todoschedule.ui.navigation.AppRoutes.Todo5com.example.todoschedule.ui.navigation.AppRoutes.Task6com.example.todoschedule.ui.navigation.AppRoutes.Study8com.example.todoschedule.ui.navigation.AppRoutes.Profile9com.example.todoschedule.ui.navigation.AppRoutes.Settings:com.example.todoschedule.ui.navigation.AppRoutes.AddCourse=com.example.todoschedule.ui.navigation.AppRoutes.CourseDetail;com.example.todoschedule.ui.navigation.AppRoutes.EditCourse?com.example.todoschedule.ui.navigation.AppRoutes.SchoolSelector><EMAIL>=com.example.todoschedule.ui.navigation.AppRoutes.TaskReminderAcom.example.todoschedule.ui.navigation.AppRoutes.TaskCalendarSync=com.example.todoschedule.ui.navigation.AppRoutes.SyncSettingsMcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModelRcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState.LoadingRcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState.SuccessPcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState.ErrorUcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEvent.NavigateBackRcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEvent.ShowErrorLcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel><EMAIL>@com.example.todoschedule.ui.schedule.model.ScheduleUiState.ErrorJcom.example.todoschedule.ui.schedule.model.ScheduleUiState.NoTableSelectedBcom.example.todoschedule.ui.schedule.model.ScheduleUiState.Success6com.example.todoschedule.ui.settings.SettingsViewModel;com.example.todoschedule.ui.settings.DatabaseOperation.Idle>com.example.todoschedule.ui.settings.DatabaseOperation.Loading>com.example.todoschedule.ui.settings.DatabaseOperation.Success<com.example.todoschedule.ui.settings.DatabaseOperation.Error.com.example.todoschedule.ui.sync.SyncViewModel8com.example.todoschedule.ui.sync.SyncViewModel.SyncState:com.example.todoschedule.ui.table.CreateEditTableViewModel6com.example.todoschedule.ui.task.TaskCalendarViewModel+com.example.todoschedule.ui.task.TaskFilter=com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask-com.example.todoschedule.ui.task.TaskItemType*com.example.todoschedule.ui.task.TaskGroup.com.example.todoschedule.ui.task.TaskViewModel7com.example.todoschedule.ui.theme.ColorSchemeEnum.Fixed9com.example.todoschedule.ui.theme.ColorSchemeEnum.PRIMARY;<EMAIL>;com.example.todoschedule.ui.theme.ColorSchemeEnum.SECONDARY=com.example.todoschedule.ui.theme.ColorSchemeEnum.ONSECONDARYDcom.example.todoschedule.ui.theme.ColorSchemeEnum.SECONDARYCONTAINERFcom.example.todoschedule.ui.theme.ColorSchemeEnum.ONSECONDARYCONTAINER:com.example.todoschedule.ui.theme.ColorSchemeEnum.TERTIARY<com.example.todoschedule.ui.theme.ColorSchemeEnum.ONTERTIARYCcom.example.todoschedule.ui.theme.ColorSchemeEnum.TERTIARYCONTAINEREcom.example.todoschedule.ui.theme.ColorSchemeEnum.ONTERTIARYCONTAINER<com.example.todoschedule.ui.theme.ColorSchemeEnum.BACKGROUND>com.example.todoschedule.ui.theme.ColorSchemeEnum.ONBACKGROUND9com.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACE;<EMAIL>=<EMAIL>@<EMAIL>?com.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACEBRIGHT<com.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACEDIMBcom.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACECONTAINERFcom.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACECONTAINERHIGHIcom.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACECONTAINERHIGHESTEcom.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACECONTAINERLOWHcom.example.todoschedule.ui.theme.ColorSchemeEnum.SURFACECONTAINERLOWEST-com.example.todoschedule.ui.todo.TodoPriority4com.example.todoschedule.ui.todo.TodoUiState.Loading2com.example.todoschedule.ui.todo.TodoUiState.Empty4com.example.todoschedule.ui.todo.TodoUiState.Success2com.example.todoschedule.ui.todo.TodoUiState.Error.com.example.todoschedule.ui.todo.TodoViewModel.com.example.todoschedule.util.Resource.Success,com.example.todoschedule.util.Resource.Error.com.example.todoschedule.util.Resource.Loading*main.java.parser.supwisdom.SupwisdomParsermain.java.parser.ZZUParser                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         