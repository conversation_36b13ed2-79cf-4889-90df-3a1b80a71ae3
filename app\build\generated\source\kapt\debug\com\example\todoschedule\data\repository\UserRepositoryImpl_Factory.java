// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.database.dao.UserDao;
import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepositoryImpl_Factory implements Factory<UserRepositoryImpl> {
  private final Provider<UserDao> userDaoProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  public UserRepositoryImpl_Factory(Provider<UserDao> userDaoProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    this.userDaoProvider = userDaoProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public UserRepositoryImpl get() {
    return newInstance(userDaoProvider.get(), sessionRepositoryProvider.get());
  }

  public static UserRepositoryImpl_Factory create(Provider<UserDao> userDaoProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new UserRepositoryImpl_Factory(userDaoProvider, sessionRepositoryProvider);
  }

  public static UserRepositoryImpl newInstance(UserDao userDao,
      SessionRepository sessionRepository) {
    return new UserRepositoryImpl(userDao, sessionRepository);
  }
}
