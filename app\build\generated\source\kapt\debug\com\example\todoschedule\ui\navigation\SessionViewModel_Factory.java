// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.navigation;

import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SessionViewModel_Factory implements Factory<SessionViewModel> {
  private final Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  public SessionViewModel_Factory(
      Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    this.getLoginUserIdFlowUseCaseProvider = getLoginUserIdFlowUseCaseProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public SessionViewModel get() {
    return newInstance(getLoginUserIdFlowUseCaseProvider.get(), sessionRepositoryProvider.get());
  }

  public static SessionViewModel_Factory create(
      Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new SessionViewModel_Factory(getLoginUserIdFlowUseCaseProvider, sessionRepositoryProvider);
  }

  public static SessionViewModel newInstance(GetLoginUserIdFlowUseCase getLoginUserIdFlowUseCase,
      SessionRepository sessionRepository) {
    return new SessionViewModel(getLoginUserIdFlowUseCase, sessionRepository);
  }
}
