package com.example.todoschedule.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.todoschedule.data.database.dao.CourseDao;
import com.example.todoschedule.data.database.dao.CourseDao_Impl;
import com.example.todoschedule.data.database.dao.CourseNodeDao;
import com.example.todoschedule.data.database.dao.CourseNodeDao_Impl;
import com.example.todoschedule.data.database.dao.GlobalSettingDao;
import com.example.todoschedule.data.database.dao.GlobalSettingDao_Impl;
import com.example.todoschedule.data.database.dao.OrdinaryScheduleDao;
import com.example.todoschedule.data.database.dao.OrdinaryScheduleDao_Impl;
import com.example.todoschedule.data.database.dao.SyncMessageDao;
import com.example.todoschedule.data.database.dao.SyncMessageDao_Impl;
import com.example.todoschedule.data.database.dao.TableDao;
import com.example.todoschedule.data.database.dao.TableDao_Impl;
import com.example.todoschedule.data.database.dao.TableTimeConfigDao;
import com.example.todoschedule.data.database.dao.TableTimeConfigDao_Impl;
import com.example.todoschedule.data.database.dao.TimeSlotDao;
import com.example.todoschedule.data.database.dao.TimeSlotDao_Impl;
import com.example.todoschedule.data.database.dao.UserDao;
import com.example.todoschedule.data.database.dao.UserDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile UserDao _userDao;

  private volatile GlobalSettingDao _globalSettingDao;

  private volatile TableDao _tableDao;

  private volatile CourseDao _courseDao;

  private volatile CourseNodeDao _courseNodeDao;

  private volatile TableTimeConfigDao _tableTimeConfigDao;

  private volatile OrdinaryScheduleDao _ordinaryScheduleDao;

  private volatile TimeSlotDao _timeSlotDao;

  private volatile SyncMessageDao _syncMessageDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(6) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `user` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `username` TEXT NOT NULL, `phoneNumber` TEXT, `email` TEXT, `avatar` TEXT, `createdAt` INTEGER NOT NULL, `passwordHash` TEXT, `lastOpen` INTEGER NOT NULL, `token` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `global_table_setting` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `defaultTableIds` TEXT NOT NULL, `showWeekend` INTEGER NOT NULL, `courseNotificationStyle` INTEGER NOT NULL, `notifyBeforeMinutes` INTEGER NOT NULL, `autoSwitchWeek` INTEGER NOT NULL, `showCourseTime` INTEGER NOT NULL, FOREIGN KEY(`userId`) REFERENCES `user`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_global_table_setting_userId` ON `global_table_setting` (`userId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `table` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `tableName` TEXT NOT NULL, `background` TEXT NOT NULL, `listPosition` INTEGER NOT NULL, `terms` TEXT NOT NULL, `startDate` TEXT NOT NULL, `totalWeeks` INTEGER NOT NULL, `crdtKey` TEXT NOT NULL, `userCrdtKey` TEXT, `update_timestamp` INTEGER, FOREIGN KEY(`userId`) REFERENCES `user`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_table_userId` ON `table` (`userId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_table_crdtKey` ON `table` (`crdtKey`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_table_userCrdtKey` ON `table` (`userCrdtKey`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `course` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `tableId` INTEGER NOT NULL, `courseName` TEXT NOT NULL, `color` TEXT NOT NULL, `room` TEXT, `teacher` TEXT, `startWeek` INTEGER NOT NULL, `endWeek` INTEGER NOT NULL, `credit` REAL, `courseCode` TEXT, `syllabusLink` TEXT, `crdtKey` TEXT NOT NULL, `tableCrdtKey` TEXT, `update_timestamp` INTEGER, FOREIGN KEY(`tableId`) REFERENCES `table`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_course_tableId` ON `course` (`tableId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_course_crdtKey` ON `course` (`crdtKey`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_course_tableCrdtKey` ON `course` (`tableCrdtKey`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `course_node` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `courseId` INTEGER NOT NULL, `courseNodeName` TEXT, `color` TEXT, `room` TEXT, `teacher` TEXT, `startNode` INTEGER NOT NULL, `step` INTEGER NOT NULL, `day` INTEGER NOT NULL, `startWeek` INTEGER NOT NULL, `endWeek` INTEGER NOT NULL, `weekType` INTEGER NOT NULL, `crdtKey` TEXT NOT NULL, `courseCrdtKey` TEXT, `update_timestamp` INTEGER, FOREIGN KEY(`courseId`) REFERENCES `course`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_course_node_courseId` ON `course_node` (`courseId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_course_node_crdtKey` ON `course_node` (`crdtKey`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_course_node_courseCrdtKey` ON `course_node` (`courseCrdtKey`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `table_time_config` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `table_id` INTEGER NOT NULL, `name` TEXT NOT NULL, `is_default` INTEGER NOT NULL, FOREIGN KEY(`table_id`) REFERENCES `table`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `idx_timeconfig_tableid` ON `table_time_config` (`table_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `table_time_config_node_detaile` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `table_time_config_id` INTEGER NOT NULL, `name` TEXT NOT NULL, `start_time` TEXT NOT NULL, `end_time` TEXT NOT NULL, `node` INTEGER NOT NULL, FOREIGN KEY(`table_time_config_id`) REFERENCES `table_time_config`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `idx_nodedetail_configid` ON `table_time_config_node_detaile` (`table_time_config_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `ordinary_schedule` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `title` TEXT NOT NULL, `description` TEXT, `location` TEXT, `category` TEXT, `color` TEXT, `is_all_day` INTEGER NOT NULL, `status` TEXT, `crdtKey` TEXT NOT NULL, `userCrdtKey` TEXT, `update_timestamp` INTEGER, FOREIGN KEY(`userId`) REFERENCES `user`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_ordinary_schedule_userId` ON `ordinary_schedule` (`userId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_ordinary_schedule_crdtKey` ON `ordinary_schedule` (`crdtKey`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_ordinary_schedule_userCrdtKey` ON `ordinary_schedule` (`userCrdtKey`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `time_slot` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `user_id` INTEGER NOT NULL, `start_time` INTEGER NOT NULL, `end_time` INTEGER NOT NULL, `schedule_type` TEXT NOT NULL, `schedule_id` INTEGER NOT NULL, `head` TEXT, `priority` INTEGER, `is_completed` INTEGER NOT NULL, `is_repeated` INTEGER NOT NULL, `repeat_pattern` TEXT, `reminder_type` TEXT, `reminder_offset` INTEGER, FOREIGN KEY(`user_id`) REFERENCES `user`(`id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `idx_timeslot_userid` ON `time_slot` (`user_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `sync_message` (`sync_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `crdt_key` TEXT NOT NULL, `entity_type` TEXT NOT NULL, `operation_type` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `device_id` TEXT NOT NULL, `timestamp_wall_clock` INTEGER NOT NULL, `timestamp_logical` INTEGER NOT NULL, `timestamp_node_id` TEXT NOT NULL, `payload` TEXT NOT NULL, `user_id` INTEGER NOT NULL, `sync_status` TEXT NOT NULL, `last_sync_attempt` INTEGER, `sync_error` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd8e9ca4bace1b5ded5947cb33b2c922d')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `user`");
        db.execSQL("DROP TABLE IF EXISTS `global_table_setting`");
        db.execSQL("DROP TABLE IF EXISTS `table`");
        db.execSQL("DROP TABLE IF EXISTS `course`");
        db.execSQL("DROP TABLE IF EXISTS `course_node`");
        db.execSQL("DROP TABLE IF EXISTS `table_time_config`");
        db.execSQL("DROP TABLE IF EXISTS `table_time_config_node_detaile`");
        db.execSQL("DROP TABLE IF EXISTS `ordinary_schedule`");
        db.execSQL("DROP TABLE IF EXISTS `time_slot`");
        db.execSQL("DROP TABLE IF EXISTS `sync_message`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsUser = new HashMap<String, TableInfo.Column>(9);
        _columnsUser.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("username", new TableInfo.Column("username", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("phoneNumber", new TableInfo.Column("phoneNumber", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("email", new TableInfo.Column("email", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("avatar", new TableInfo.Column("avatar", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("passwordHash", new TableInfo.Column("passwordHash", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("lastOpen", new TableInfo.Column("lastOpen", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUser.put("token", new TableInfo.Column("token", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUser = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUser = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUser = new TableInfo("user", _columnsUser, _foreignKeysUser, _indicesUser);
        final TableInfo _existingUser = TableInfo.read(db, "user");
        if (!_infoUser.equals(_existingUser)) {
          return new RoomOpenHelper.ValidationResult(false, "user(com.example.todoschedule.data.database.entity.UserEntity).\n"
                  + " Expected:\n" + _infoUser + "\n"
                  + " Found:\n" + _existingUser);
        }
        final HashMap<String, TableInfo.Column> _columnsGlobalTableSetting = new HashMap<String, TableInfo.Column>(8);
        _columnsGlobalTableSetting.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGlobalTableSetting.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGlobalTableSetting.put("defaultTableIds", new TableInfo.Column("defaultTableIds", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGlobalTableSetting.put("showWeekend", new TableInfo.Column("showWeekend", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGlobalTableSetting.put("courseNotificationStyle", new TableInfo.Column("courseNotificationStyle", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGlobalTableSetting.put("notifyBeforeMinutes", new TableInfo.Column("notifyBeforeMinutes", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGlobalTableSetting.put("autoSwitchWeek", new TableInfo.Column("autoSwitchWeek", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGlobalTableSetting.put("showCourseTime", new TableInfo.Column("showCourseTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysGlobalTableSetting = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysGlobalTableSetting.add(new TableInfo.ForeignKey("user", "CASCADE", "NO ACTION", Arrays.asList("userId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesGlobalTableSetting = new HashSet<TableInfo.Index>(1);
        _indicesGlobalTableSetting.add(new TableInfo.Index("index_global_table_setting_userId", false, Arrays.asList("userId"), Arrays.asList("ASC")));
        final TableInfo _infoGlobalTableSetting = new TableInfo("global_table_setting", _columnsGlobalTableSetting, _foreignKeysGlobalTableSetting, _indicesGlobalTableSetting);
        final TableInfo _existingGlobalTableSetting = TableInfo.read(db, "global_table_setting");
        if (!_infoGlobalTableSetting.equals(_existingGlobalTableSetting)) {
          return new RoomOpenHelper.ValidationResult(false, "global_table_setting(com.example.todoschedule.data.database.entity.GlobalTableSettingEntity).\n"
                  + " Expected:\n" + _infoGlobalTableSetting + "\n"
                  + " Found:\n" + _existingGlobalTableSetting);
        }
        final HashMap<String, TableInfo.Column> _columnsTable = new HashMap<String, TableInfo.Column>(11);
        _columnsTable.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("tableName", new TableInfo.Column("tableName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("background", new TableInfo.Column("background", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("listPosition", new TableInfo.Column("listPosition", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("terms", new TableInfo.Column("terms", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("startDate", new TableInfo.Column("startDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("totalWeeks", new TableInfo.Column("totalWeeks", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("crdtKey", new TableInfo.Column("crdtKey", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("userCrdtKey", new TableInfo.Column("userCrdtKey", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTable.put("update_timestamp", new TableInfo.Column("update_timestamp", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTable = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysTable.add(new TableInfo.ForeignKey("user", "CASCADE", "NO ACTION", Arrays.asList("userId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesTable = new HashSet<TableInfo.Index>(3);
        _indicesTable.add(new TableInfo.Index("index_table_userId", false, Arrays.asList("userId"), Arrays.asList("ASC")));
        _indicesTable.add(new TableInfo.Index("index_table_crdtKey", false, Arrays.asList("crdtKey"), Arrays.asList("ASC")));
        _indicesTable.add(new TableInfo.Index("index_table_userCrdtKey", false, Arrays.asList("userCrdtKey"), Arrays.asList("ASC")));
        final TableInfo _infoTable = new TableInfo("table", _columnsTable, _foreignKeysTable, _indicesTable);
        final TableInfo _existingTable = TableInfo.read(db, "table");
        if (!_infoTable.equals(_existingTable)) {
          return new RoomOpenHelper.ValidationResult(false, "table(com.example.todoschedule.data.database.entity.TableEntity).\n"
                  + " Expected:\n" + _infoTable + "\n"
                  + " Found:\n" + _existingTable);
        }
        final HashMap<String, TableInfo.Column> _columnsCourse = new HashMap<String, TableInfo.Column>(14);
        _columnsCourse.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("tableId", new TableInfo.Column("tableId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("courseName", new TableInfo.Column("courseName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("color", new TableInfo.Column("color", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("room", new TableInfo.Column("room", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("teacher", new TableInfo.Column("teacher", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("startWeek", new TableInfo.Column("startWeek", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("endWeek", new TableInfo.Column("endWeek", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("credit", new TableInfo.Column("credit", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("courseCode", new TableInfo.Column("courseCode", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("syllabusLink", new TableInfo.Column("syllabusLink", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("crdtKey", new TableInfo.Column("crdtKey", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("tableCrdtKey", new TableInfo.Column("tableCrdtKey", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourse.put("update_timestamp", new TableInfo.Column("update_timestamp", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCourse = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysCourse.add(new TableInfo.ForeignKey("table", "CASCADE", "NO ACTION", Arrays.asList("tableId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesCourse = new HashSet<TableInfo.Index>(3);
        _indicesCourse.add(new TableInfo.Index("index_course_tableId", false, Arrays.asList("tableId"), Arrays.asList("ASC")));
        _indicesCourse.add(new TableInfo.Index("index_course_crdtKey", false, Arrays.asList("crdtKey"), Arrays.asList("ASC")));
        _indicesCourse.add(new TableInfo.Index("index_course_tableCrdtKey", false, Arrays.asList("tableCrdtKey"), Arrays.asList("ASC")));
        final TableInfo _infoCourse = new TableInfo("course", _columnsCourse, _foreignKeysCourse, _indicesCourse);
        final TableInfo _existingCourse = TableInfo.read(db, "course");
        if (!_infoCourse.equals(_existingCourse)) {
          return new RoomOpenHelper.ValidationResult(false, "course(com.example.todoschedule.data.database.entity.CourseEntity).\n"
                  + " Expected:\n" + _infoCourse + "\n"
                  + " Found:\n" + _existingCourse);
        }
        final HashMap<String, TableInfo.Column> _columnsCourseNode = new HashMap<String, TableInfo.Column>(15);
        _columnsCourseNode.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("courseId", new TableInfo.Column("courseId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("courseNodeName", new TableInfo.Column("courseNodeName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("color", new TableInfo.Column("color", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("room", new TableInfo.Column("room", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("teacher", new TableInfo.Column("teacher", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("startNode", new TableInfo.Column("startNode", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("step", new TableInfo.Column("step", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("day", new TableInfo.Column("day", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("startWeek", new TableInfo.Column("startWeek", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("endWeek", new TableInfo.Column("endWeek", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("weekType", new TableInfo.Column("weekType", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("crdtKey", new TableInfo.Column("crdtKey", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("courseCrdtKey", new TableInfo.Column("courseCrdtKey", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCourseNode.put("update_timestamp", new TableInfo.Column("update_timestamp", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCourseNode = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysCourseNode.add(new TableInfo.ForeignKey("course", "CASCADE", "NO ACTION", Arrays.asList("courseId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesCourseNode = new HashSet<TableInfo.Index>(3);
        _indicesCourseNode.add(new TableInfo.Index("index_course_node_courseId", false, Arrays.asList("courseId"), Arrays.asList("ASC")));
        _indicesCourseNode.add(new TableInfo.Index("index_course_node_crdtKey", false, Arrays.asList("crdtKey"), Arrays.asList("ASC")));
        _indicesCourseNode.add(new TableInfo.Index("index_course_node_courseCrdtKey", false, Arrays.asList("courseCrdtKey"), Arrays.asList("ASC")));
        final TableInfo _infoCourseNode = new TableInfo("course_node", _columnsCourseNode, _foreignKeysCourseNode, _indicesCourseNode);
        final TableInfo _existingCourseNode = TableInfo.read(db, "course_node");
        if (!_infoCourseNode.equals(_existingCourseNode)) {
          return new RoomOpenHelper.ValidationResult(false, "course_node(com.example.todoschedule.data.database.entity.CourseNodeEntity).\n"
                  + " Expected:\n" + _infoCourseNode + "\n"
                  + " Found:\n" + _existingCourseNode);
        }
        final HashMap<String, TableInfo.Column> _columnsTableTimeConfig = new HashMap<String, TableInfo.Column>(4);
        _columnsTableTimeConfig.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfig.put("table_id", new TableInfo.Column("table_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfig.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfig.put("is_default", new TableInfo.Column("is_default", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTableTimeConfig = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysTableTimeConfig.add(new TableInfo.ForeignKey("table", "CASCADE", "NO ACTION", Arrays.asList("table_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesTableTimeConfig = new HashSet<TableInfo.Index>(1);
        _indicesTableTimeConfig.add(new TableInfo.Index("idx_timeconfig_tableid", false, Arrays.asList("table_id"), Arrays.asList("ASC")));
        final TableInfo _infoTableTimeConfig = new TableInfo("table_time_config", _columnsTableTimeConfig, _foreignKeysTableTimeConfig, _indicesTableTimeConfig);
        final TableInfo _existingTableTimeConfig = TableInfo.read(db, "table_time_config");
        if (!_infoTableTimeConfig.equals(_existingTableTimeConfig)) {
          return new RoomOpenHelper.ValidationResult(false, "table_time_config(com.example.todoschedule.data.database.entity.TableTimeConfigEntity).\n"
                  + " Expected:\n" + _infoTableTimeConfig + "\n"
                  + " Found:\n" + _existingTableTimeConfig);
        }
        final HashMap<String, TableInfo.Column> _columnsTableTimeConfigNodeDetaile = new HashMap<String, TableInfo.Column>(6);
        _columnsTableTimeConfigNodeDetaile.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfigNodeDetaile.put("table_time_config_id", new TableInfo.Column("table_time_config_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfigNodeDetaile.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfigNodeDetaile.put("start_time", new TableInfo.Column("start_time", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfigNodeDetaile.put("end_time", new TableInfo.Column("end_time", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTableTimeConfigNodeDetaile.put("node", new TableInfo.Column("node", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTableTimeConfigNodeDetaile = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysTableTimeConfigNodeDetaile.add(new TableInfo.ForeignKey("table_time_config", "CASCADE", "NO ACTION", Arrays.asList("table_time_config_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesTableTimeConfigNodeDetaile = new HashSet<TableInfo.Index>(1);
        _indicesTableTimeConfigNodeDetaile.add(new TableInfo.Index("idx_nodedetail_configid", false, Arrays.asList("table_time_config_id"), Arrays.asList("ASC")));
        final TableInfo _infoTableTimeConfigNodeDetaile = new TableInfo("table_time_config_node_detaile", _columnsTableTimeConfigNodeDetaile, _foreignKeysTableTimeConfigNodeDetaile, _indicesTableTimeConfigNodeDetaile);
        final TableInfo _existingTableTimeConfigNodeDetaile = TableInfo.read(db, "table_time_config_node_detaile");
        if (!_infoTableTimeConfigNodeDetaile.equals(_existingTableTimeConfigNodeDetaile)) {
          return new RoomOpenHelper.ValidationResult(false, "table_time_config_node_detaile(com.example.todoschedule.data.database.entity.TableTimeConfigNodeDetaileEntity).\n"
                  + " Expected:\n" + _infoTableTimeConfigNodeDetaile + "\n"
                  + " Found:\n" + _existingTableTimeConfigNodeDetaile);
        }
        final HashMap<String, TableInfo.Column> _columnsOrdinarySchedule = new HashMap<String, TableInfo.Column>(12);
        _columnsOrdinarySchedule.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("location", new TableInfo.Column("location", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("category", new TableInfo.Column("category", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("color", new TableInfo.Column("color", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("is_all_day", new TableInfo.Column("is_all_day", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("status", new TableInfo.Column("status", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("crdtKey", new TableInfo.Column("crdtKey", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("userCrdtKey", new TableInfo.Column("userCrdtKey", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOrdinarySchedule.put("update_timestamp", new TableInfo.Column("update_timestamp", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOrdinarySchedule = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysOrdinarySchedule.add(new TableInfo.ForeignKey("user", "CASCADE", "NO ACTION", Arrays.asList("userId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesOrdinarySchedule = new HashSet<TableInfo.Index>(3);
        _indicesOrdinarySchedule.add(new TableInfo.Index("index_ordinary_schedule_userId", false, Arrays.asList("userId"), Arrays.asList("ASC")));
        _indicesOrdinarySchedule.add(new TableInfo.Index("index_ordinary_schedule_crdtKey", false, Arrays.asList("crdtKey"), Arrays.asList("ASC")));
        _indicesOrdinarySchedule.add(new TableInfo.Index("index_ordinary_schedule_userCrdtKey", false, Arrays.asList("userCrdtKey"), Arrays.asList("ASC")));
        final TableInfo _infoOrdinarySchedule = new TableInfo("ordinary_schedule", _columnsOrdinarySchedule, _foreignKeysOrdinarySchedule, _indicesOrdinarySchedule);
        final TableInfo _existingOrdinarySchedule = TableInfo.read(db, "ordinary_schedule");
        if (!_infoOrdinarySchedule.equals(_existingOrdinarySchedule)) {
          return new RoomOpenHelper.ValidationResult(false, "ordinary_schedule(com.example.todoschedule.data.database.entity.OrdinaryScheduleEntity).\n"
                  + " Expected:\n" + _infoOrdinarySchedule + "\n"
                  + " Found:\n" + _existingOrdinarySchedule);
        }
        final HashMap<String, TableInfo.Column> _columnsTimeSlot = new HashMap<String, TableInfo.Column>(13);
        _columnsTimeSlot.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("user_id", new TableInfo.Column("user_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("start_time", new TableInfo.Column("start_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("end_time", new TableInfo.Column("end_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("schedule_type", new TableInfo.Column("schedule_type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("schedule_id", new TableInfo.Column("schedule_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("head", new TableInfo.Column("head", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("priority", new TableInfo.Column("priority", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("is_completed", new TableInfo.Column("is_completed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("is_repeated", new TableInfo.Column("is_repeated", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("repeat_pattern", new TableInfo.Column("repeat_pattern", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("reminder_type", new TableInfo.Column("reminder_type", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSlot.put("reminder_offset", new TableInfo.Column("reminder_offset", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTimeSlot = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysTimeSlot.add(new TableInfo.ForeignKey("user", "CASCADE", "CASCADE", Arrays.asList("user_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesTimeSlot = new HashSet<TableInfo.Index>(1);
        _indicesTimeSlot.add(new TableInfo.Index("idx_timeslot_userid", false, Arrays.asList("user_id"), Arrays.asList("ASC")));
        final TableInfo _infoTimeSlot = new TableInfo("time_slot", _columnsTimeSlot, _foreignKeysTimeSlot, _indicesTimeSlot);
        final TableInfo _existingTimeSlot = TableInfo.read(db, "time_slot");
        if (!_infoTimeSlot.equals(_existingTimeSlot)) {
          return new RoomOpenHelper.ValidationResult(false, "time_slot(com.example.todoschedule.data.database.entity.TimeSlotEntity).\n"
                  + " Expected:\n" + _infoTimeSlot + "\n"
                  + " Found:\n" + _existingTimeSlot);
        }
        final HashMap<String, TableInfo.Column> _columnsSyncMessage = new HashMap<String, TableInfo.Column>(14);
        _columnsSyncMessage.put("sync_id", new TableInfo.Column("sync_id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("crdt_key", new TableInfo.Column("crdt_key", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("entity_type", new TableInfo.Column("entity_type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("operation_type", new TableInfo.Column("operation_type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("device_id", new TableInfo.Column("device_id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("timestamp_wall_clock", new TableInfo.Column("timestamp_wall_clock", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("timestamp_logical", new TableInfo.Column("timestamp_logical", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("timestamp_node_id", new TableInfo.Column("timestamp_node_id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("payload", new TableInfo.Column("payload", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("user_id", new TableInfo.Column("user_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("sync_status", new TableInfo.Column("sync_status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("last_sync_attempt", new TableInfo.Column("last_sync_attempt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSyncMessage.put("sync_error", new TableInfo.Column("sync_error", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSyncMessage = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSyncMessage = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSyncMessage = new TableInfo("sync_message", _columnsSyncMessage, _foreignKeysSyncMessage, _indicesSyncMessage);
        final TableInfo _existingSyncMessage = TableInfo.read(db, "sync_message");
        if (!_infoSyncMessage.equals(_existingSyncMessage)) {
          return new RoomOpenHelper.ValidationResult(false, "sync_message(com.example.todoschedule.data.database.entity.SyncMessageEntity).\n"
                  + " Expected:\n" + _infoSyncMessage + "\n"
                  + " Found:\n" + _existingSyncMessage);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "d8e9ca4bace1b5ded5947cb33b2c922d", "8133d9bd313b79fcd1a8b196e4312c6b");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "user","global_table_setting","table","course","course_node","table_time_config","table_time_config_node_detaile","ordinary_schedule","time_slot","sync_message");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `user`");
      _db.execSQL("DELETE FROM `global_table_setting`");
      _db.execSQL("DELETE FROM `table`");
      _db.execSQL("DELETE FROM `course`");
      _db.execSQL("DELETE FROM `course_node`");
      _db.execSQL("DELETE FROM `table_time_config`");
      _db.execSQL("DELETE FROM `table_time_config_node_detaile`");
      _db.execSQL("DELETE FROM `ordinary_schedule`");
      _db.execSQL("DELETE FROM `time_slot`");
      _db.execSQL("DELETE FROM `sync_message`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(UserDao.class, UserDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(GlobalSettingDao.class, GlobalSettingDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TableDao.class, TableDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CourseDao.class, CourseDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CourseNodeDao.class, CourseNodeDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TableTimeConfigDao.class, TableTimeConfigDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(OrdinaryScheduleDao.class, OrdinaryScheduleDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TimeSlotDao.class, TimeSlotDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SyncMessageDao.class, SyncMessageDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public UserDao userDao() {
    if (_userDao != null) {
      return _userDao;
    } else {
      synchronized(this) {
        if(_userDao == null) {
          _userDao = new UserDao_Impl(this);
        }
        return _userDao;
      }
    }
  }

  @Override
  public GlobalSettingDao globalSettingDao() {
    if (_globalSettingDao != null) {
      return _globalSettingDao;
    } else {
      synchronized(this) {
        if(_globalSettingDao == null) {
          _globalSettingDao = new GlobalSettingDao_Impl(this);
        }
        return _globalSettingDao;
      }
    }
  }

  @Override
  public TableDao tableDao() {
    if (_tableDao != null) {
      return _tableDao;
    } else {
      synchronized(this) {
        if(_tableDao == null) {
          _tableDao = new TableDao_Impl(this);
        }
        return _tableDao;
      }
    }
  }

  @Override
  public CourseDao courseDao() {
    if (_courseDao != null) {
      return _courseDao;
    } else {
      synchronized(this) {
        if(_courseDao == null) {
          _courseDao = new CourseDao_Impl(this);
        }
        return _courseDao;
      }
    }
  }

  @Override
  public CourseNodeDao courseNodeDao() {
    if (_courseNodeDao != null) {
      return _courseNodeDao;
    } else {
      synchronized(this) {
        if(_courseNodeDao == null) {
          _courseNodeDao = new CourseNodeDao_Impl(this);
        }
        return _courseNodeDao;
      }
    }
  }

  @Override
  public TableTimeConfigDao tableTimeConfigDao() {
    if (_tableTimeConfigDao != null) {
      return _tableTimeConfigDao;
    } else {
      synchronized(this) {
        if(_tableTimeConfigDao == null) {
          _tableTimeConfigDao = new TableTimeConfigDao_Impl(this);
        }
        return _tableTimeConfigDao;
      }
    }
  }

  @Override
  public OrdinaryScheduleDao ordinaryScheduleDao() {
    if (_ordinaryScheduleDao != null) {
      return _ordinaryScheduleDao;
    } else {
      synchronized(this) {
        if(_ordinaryScheduleDao == null) {
          _ordinaryScheduleDao = new OrdinaryScheduleDao_Impl(this);
        }
        return _ordinaryScheduleDao;
      }
    }
  }

  @Override
  public TimeSlotDao timeSlotDao() {
    if (_timeSlotDao != null) {
      return _timeSlotDao;
    } else {
      synchronized(this) {
        if(_timeSlotDao == null) {
          _timeSlotDao = new TimeSlotDao_Impl(this);
        }
        return _timeSlotDao;
      }
    }
  }

  @Override
  public SyncMessageDao syncMessageDao() {
    if (_syncMessageDao != null) {
      return _syncMessageDao;
    } else {
      synchronized(this) {
        if(_syncMessageDao == null) {
          _syncMessageDao = new SyncMessageDao_Impl(this);
        }
        return _syncMessageDao;
      }
    }
  }
}
