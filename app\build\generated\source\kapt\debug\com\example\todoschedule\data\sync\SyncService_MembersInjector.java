// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import com.example.todoschedule.data.repository.SyncRepository;
import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncService_MembersInjector implements MembersInjector<SyncService> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<SyncManager> syncManagerProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  public SyncService_MembersInjector(Provider<SyncRepository> syncRepositoryProvider,
      Provider<SyncManager> syncManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.syncManagerProvider = syncManagerProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  public static MembersInjector<SyncService> create(Provider<SyncRepository> syncRepositoryProvider,
      Provider<SyncManager> syncManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new SyncService_MembersInjector(syncRepositoryProvider, syncManagerProvider, sessionRepositoryProvider);
  }

  @Override
  public void injectMembers(SyncService instance) {
    injectSyncRepository(instance, syncRepositoryProvider.get());
    injectSyncManager(instance, syncManagerProvider.get());
    injectSessionRepository(instance, sessionRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.example.todoschedule.data.sync.SyncService.syncRepository")
  public static void injectSyncRepository(SyncService instance, SyncRepository syncRepository) {
    instance.syncRepository = syncRepository;
  }

  @InjectedFieldSignature("com.example.todoschedule.data.sync.SyncService.syncManager")
  public static void injectSyncManager(SyncService instance, SyncManager syncManager) {
    instance.syncManager = syncManager;
  }

  @InjectedFieldSignature("com.example.todoschedule.data.sync.SyncService.sessionRepository")
  public static void injectSessionRepository(SyncService instance,
      SessionRepository sessionRepository) {
    instance.sessionRepository = sessionRepository;
  }
}
