package com.example.todoschedule.data.database.entity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\u001e\u0010\u0003\u001a\u00020\u0002*\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a8\u0006\b"}, d2 = {"toDto", "Lcom/example/todoschedule/data/sync/dto/SyncMessageDto;", "Lcom/example/todoschedule/data/database/entity/SyncMessageEntity;", "withStatus", "status", "Lcom/example/todoschedule/data/sync/SyncConstants$SyncStatus;", "message", "", "app_debug"})
public final class SyncMessageEntityExtensionsKt {
    
    /**
     * 将同步消息实体转换为DTO对象
     *
     * 注意：实际的SyncMessageEntity类中已经有了toDto方法，
     * 这个扩展函数是多余的，仅作为编译时兼容使用
     */
    @org.jetbrains.annotations.NotNull()
    public static final com.example.todoschedule.data.sync.dto.SyncMessageDto toDto(@org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.database.entity.SyncMessageEntity $this$toDto) {
        return null;
    }
    
    /**
     * 更新同步消息状态
     *
     * 注意：实际的SyncMessageEntity类中已经有了withStatus方法，
     * 这个扩展函数是多余的，仅作为编译时兼容使用
     *
     * @param status 新状态
     * @param message 可选的状态消息
     * @return 更新状态后的新实体
     */
    @org.jetbrains.annotations.NotNull()
    public static final com.example.todoschedule.data.database.entity.SyncMessageEntity withStatus(@org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.database.entity.SyncMessageEntity $this$withStatus, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.SyncConstants.SyncStatus status, @org.jetbrains.annotations.Nullable()
    java.lang.String message) {
        return null;
    }
}