// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.ordinary_schedule;

import com.example.todoschedule.domain.repository.OrdinaryScheduleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetOrdinaryScheduleByIdUseCase_Factory implements Factory<GetOrdinaryScheduleByIdUseCase> {
  private final Provider<OrdinaryScheduleRepository> repositoryProvider;

  public GetOrdinaryScheduleByIdUseCase_Factory(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetOrdinaryScheduleByIdUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetOrdinaryScheduleByIdUseCase_Factory create(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    return new GetOrdinaryScheduleByIdUseCase_Factory(repositoryProvider);
  }

  public static GetOrdinaryScheduleByIdUseCase newInstance(OrdinaryScheduleRepository repository) {
    return new GetOrdinaryScheduleByIdUseCase(repository);
  }
}
