// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.database.dao.SyncMessageDao;
import com.example.todoschedule.data.remote.api.SyncApi;
import com.example.todoschedule.data.sync.DeviceIdManager;
import com.example.todoschedule.data.sync.SyncManager;
import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncRepositoryImpl_Factory implements Factory<SyncRepositoryImpl> {
  private final Provider<SyncMessageDao> syncMessageDaoProvider;

  private final Provider<SyncApi> syncApiProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  private final Provider<AppDatabase> databaseProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<SyncManager> syncManagerProvider;

  public SyncRepositoryImpl_Factory(Provider<SyncMessageDao> syncMessageDaoProvider,
      Provider<SyncApi> syncApiProvider, Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<AppDatabase> databaseProvider, Provider<SessionRepository> sessionRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    this.syncMessageDaoProvider = syncMessageDaoProvider;
    this.syncApiProvider = syncApiProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
    this.databaseProvider = databaseProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.syncManagerProvider = syncManagerProvider;
  }

  @Override
  public SyncRepositoryImpl get() {
    return newInstance(syncMessageDaoProvider.get(), syncApiProvider.get(), deviceIdManagerProvider.get(), databaseProvider.get(), sessionRepositoryProvider.get(), syncManagerProvider);
  }

  public static SyncRepositoryImpl_Factory create(Provider<SyncMessageDao> syncMessageDaoProvider,
      Provider<SyncApi> syncApiProvider, Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<AppDatabase> databaseProvider, Provider<SessionRepository> sessionRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    return new SyncRepositoryImpl_Factory(syncMessageDaoProvider, syncApiProvider, deviceIdManagerProvider, databaseProvider, sessionRepositoryProvider, syncManagerProvider);
  }

  public static SyncRepositoryImpl newInstance(SyncMessageDao syncMessageDao, SyncApi syncApi,
      DeviceIdManager deviceIdManager, AppDatabase database, SessionRepository sessionRepository,
      Provider<SyncManager> syncManagerProvider) {
    return new SyncRepositoryImpl(syncMessageDao, syncApi, deviceIdManager, database, sessionRepository, syncManagerProvider);
  }
}
