// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.Interceptor;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideAuthInterceptorFactory implements Factory<Interceptor> {
  private final Provider<SessionRepository> sessionRepositoryProvider;

  public NetworkModule_ProvideAuthInterceptorFactory(
      Provider<SessionRepository> sessionRepositoryProvider) {
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public Interceptor get() {
    return provideAuthInterceptor(sessionRepositoryProvider.get());
  }

  public static NetworkModule_ProvideAuthInterceptorFactory create(
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new NetworkModule_ProvideAuthInterceptorFactory(sessionRepositoryProvider);
  }

  public static Interceptor provideAuthInterceptor(SessionRepository sessionRepository) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideAuthInterceptor(sessionRepository));
  }
}
