// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import com.example.todoschedule.data.remote.api.SyncApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncApiImpl_Factory implements Factory<SyncApiImpl> {
  private final Provider<SyncApi> remoteSyncApiProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  public SyncApiImpl_Factory(Provider<SyncApi> remoteSyncApiProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider) {
    this.remoteSyncApiProvider = remoteSyncApiProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
  }

  @Override
  public SyncApiImpl get() {
    return newInstance(remoteSyncApiProvider.get(), deviceIdManagerProvider.get());
  }

  public static SyncApiImpl_Factory create(Provider<SyncApi> remoteSyncApiProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider) {
    return new SyncApiImpl_Factory(remoteSyncApiProvider, deviceIdManagerProvider);
  }

  public static SyncApiImpl newInstance(SyncApi remoteSyncApi, DeviceIdManager deviceIdManager) {
    return new SyncApiImpl(remoteSyncApi, deviceIdManager);
  }
}
