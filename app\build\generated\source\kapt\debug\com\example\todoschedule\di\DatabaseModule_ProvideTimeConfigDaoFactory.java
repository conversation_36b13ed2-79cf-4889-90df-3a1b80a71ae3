// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.database.dao.TableTimeConfigDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideTimeConfigDaoFactory implements Factory<TableTimeConfigDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideTimeConfigDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public TableTimeConfigDao get() {
    return provideTimeConfigDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideTimeConfigDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideTimeConfigDaoFactory(databaseProvider);
  }

  public static TableTimeConfigDao provideTimeConfigDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideTimeConfigDao(database));
  }
}
