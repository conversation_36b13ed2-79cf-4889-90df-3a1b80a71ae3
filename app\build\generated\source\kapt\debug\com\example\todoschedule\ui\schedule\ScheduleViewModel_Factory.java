// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.schedule;

import com.example.todoschedule.domain.repository.CourseRepository;
import com.example.todoschedule.domain.repository.GlobalSettingRepository;
import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.repository.TableRepository;
import com.example.todoschedule.domain.repository.TableTimeConfigRepository;
import com.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.DeleteOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.UpdateOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ScheduleViewModel_Factory implements Factory<ScheduleViewModel> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<TableRepository> tableRepositoryProvider;

  private final Provider<GlobalSettingRepository> globalSettingRepositoryProvider;

  private final Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider;

  private final Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider;

  private final Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider;

  private final Provider<DeleteOrdinaryScheduleUseCase> deleteOrdinaryScheduleUseCaseProvider;

  private final Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider;

  private final Provider<TableTimeConfigRepository> tableTimeConfigRepositoryProvider;

  public ScheduleViewModel_Factory(Provider<CourseRepository> courseRepositoryProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider,
      Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider,
      Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider,
      Provider<DeleteOrdinaryScheduleUseCase> deleteOrdinaryScheduleUseCaseProvider,
      Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider,
      Provider<TableTimeConfigRepository> tableTimeConfigRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.tableRepositoryProvider = tableRepositoryProvider;
    this.globalSettingRepositoryProvider = globalSettingRepositoryProvider;
    this.getOrdinarySchedulesUseCaseProvider = getOrdinarySchedulesUseCaseProvider;
    this.addOrdinaryScheduleUseCaseProvider = addOrdinaryScheduleUseCaseProvider;
    this.updateOrdinaryScheduleUseCaseProvider = updateOrdinaryScheduleUseCaseProvider;
    this.deleteOrdinaryScheduleUseCaseProvider = deleteOrdinaryScheduleUseCaseProvider;
    this.getDefaultTableTimeConfigUseCaseProvider = getDefaultTableTimeConfigUseCaseProvider;
    this.tableTimeConfigRepositoryProvider = tableTimeConfigRepositoryProvider;
  }

  @Override
  public ScheduleViewModel get() {
    return newInstance(courseRepositoryProvider.get(), sessionRepositoryProvider.get(), tableRepositoryProvider.get(), globalSettingRepositoryProvider.get(), getOrdinarySchedulesUseCaseProvider.get(), addOrdinaryScheduleUseCaseProvider.get(), updateOrdinaryScheduleUseCaseProvider.get(), deleteOrdinaryScheduleUseCaseProvider.get(), getDefaultTableTimeConfigUseCaseProvider.get(), tableTimeConfigRepositoryProvider.get());
  }

  public static ScheduleViewModel_Factory create(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider,
      Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider,
      Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider,
      Provider<DeleteOrdinaryScheduleUseCase> deleteOrdinaryScheduleUseCaseProvider,
      Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider,
      Provider<TableTimeConfigRepository> tableTimeConfigRepositoryProvider) {
    return new ScheduleViewModel_Factory(courseRepositoryProvider, sessionRepositoryProvider, tableRepositoryProvider, globalSettingRepositoryProvider, getOrdinarySchedulesUseCaseProvider, addOrdinaryScheduleUseCaseProvider, updateOrdinaryScheduleUseCaseProvider, deleteOrdinaryScheduleUseCaseProvider, getDefaultTableTimeConfigUseCaseProvider, tableTimeConfigRepositoryProvider);
  }

  public static ScheduleViewModel newInstance(CourseRepository courseRepository,
      SessionRepository sessionRepository, TableRepository tableRepository,
      GlobalSettingRepository globalSettingRepository,
      GetOrdinarySchedulesUseCase getOrdinarySchedulesUseCase,
      AddOrdinaryScheduleUseCase addOrdinaryScheduleUseCase,
      UpdateOrdinaryScheduleUseCase updateOrdinaryScheduleUseCase,
      DeleteOrdinaryScheduleUseCase deleteOrdinaryScheduleUseCase,
      GetDefaultTableTimeConfigUseCase getDefaultTableTimeConfigUseCase,
      TableTimeConfigRepository tableTimeConfigRepository) {
    return new ScheduleViewModel(courseRepository, sessionRepository, tableRepository, globalSettingRepository, getOrdinarySchedulesUseCase, addOrdinaryScheduleUseCase, updateOrdinaryScheduleUseCase, deleteOrdinaryScheduleUseCase, getDefaultTableTimeConfigUseCase, tableTimeConfigRepository);
  }
}
