// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.remote.api.SyncApi;
import com.example.todoschedule.data.sync.DeviceIdManager;
import com.example.todoschedule.data.sync.SyncMessageUploader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModuleExtensions_ProvideSyncMessageUploaderFactory implements Factory<SyncMessageUploader> {
  private final Provider<SyncApi> syncApiProvider;

  private final Provider<AppDatabase> databaseProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  public SyncModuleExtensions_ProvideSyncMessageUploaderFactory(Provider<SyncApi> syncApiProvider,
      Provider<AppDatabase> databaseProvider, Provider<DeviceIdManager> deviceIdManagerProvider) {
    this.syncApiProvider = syncApiProvider;
    this.databaseProvider = databaseProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
  }

  @Override
  public SyncMessageUploader get() {
    return provideSyncMessageUploader(syncApiProvider.get(), databaseProvider.get(), deviceIdManagerProvider.get());
  }

  public static SyncModuleExtensions_ProvideSyncMessageUploaderFactory create(
      Provider<SyncApi> syncApiProvider, Provider<AppDatabase> databaseProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider) {
    return new SyncModuleExtensions_ProvideSyncMessageUploaderFactory(syncApiProvider, databaseProvider, deviceIdManagerProvider);
  }

  public static SyncMessageUploader provideSyncMessageUploader(SyncApi syncApi,
      AppDatabase database, DeviceIdManager deviceIdManager) {
    return Preconditions.checkNotNullFromProvides(SyncModuleExtensions.INSTANCE.provideSyncMessageUploader(syncApi, database, deviceIdManager));
  }
}
