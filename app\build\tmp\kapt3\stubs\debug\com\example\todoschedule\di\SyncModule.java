package com.example.todoschedule.di;

/**
 * 同步模块依赖注入
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\b\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\u0007\u001a\u00020\b2\b\b\u0001\u0010\t\u001a\u00020\nH\u0007J\b\u0010\u000b\u001a\u00020\fH\u0007J\u0018\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\bH\u0007J(\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0011\u001a\u00020\b2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0019H\u0007J \u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u00102\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u0011\u001a\u00020\bH\u0007J6\u0010\u001f\u001a\u00020\u00152\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\b2\u0006\u0010 \u001a\u00020!2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00130#H\u0007J(\u0010$\u001a\u00020\u00172\u0006\u0010%\u001a\u00020\u00042\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\fH\u0007J\b\u0010*\u001a\u00020\'H\u0007J\u0012\u0010+\u001a\u00020,2\b\b\u0001\u0010\t\u001a\u00020\nH\u0007\u00a8\u0006-"}, d2 = {"Lcom/example/todoschedule/di/SyncModule;", "", "()V", "provideCourseAdapter", "Lcom/example/todoschedule/data/sync/adapter/CourseAdapter;", "provideCourseNodeAdapter", "Lcom/example/todoschedule/data/sync/adapter/CourseNodeAdapter;", "provideDeviceIdManager", "Lcom/example/todoschedule/data/sync/DeviceIdManager;", "context", "Landroid/content/Context;", "provideOrdinaryScheduleAdapter", "Lcom/example/todoschedule/data/sync/adapter/OrdinaryScheduleAdapter;", "provideSyncApi", "Lcom/example/todoschedule/data/sync/SyncApi;", "remoteSyncApi", "Lcom/example/todoschedule/data/remote/api/SyncApi;", "deviceIdManager", "provideSyncManager", "Lcom/example/todoschedule/data/sync/SyncManager;", "syncRepository", "Lcom/example/todoschedule/data/repository/SyncRepository;", "synkAdapterRegistry", "Lcom/example/todoschedule/data/sync/adapter/SynkAdapterRegistry;", "crdtKeyResolver", "Lcom/example/todoschedule/data/sync/CrdtKeyResolver;", "provideSyncMessageUploader", "Lcom/example/todoschedule/data/sync/SyncMessageUploader;", "syncApi", "database", "Lcom/example/todoschedule/data/database/AppDatabase;", "provideSyncRepository", "sessionRepository", "Lcom/example/todoschedule/domain/repository/SessionRepository;", "syncManagerProvider", "Ljavax/inject/Provider;", "provideSynkAdapterRegistry", "courseAdapter", "tableAdapter", "Lcom/example/todoschedule/data/sync/adapter/TableAdapter;", "courseNodeAdapter", "ordinaryScheduleAdapter", "provideTableAdapter", "provideWorkManager", "Landroidx/work/WorkManager;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class SyncModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.todoschedule.di.SyncModule INSTANCE = null;
    
    private SyncModule() {
        super();
    }
    
    /**
     * 提供WorkManager实例
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final androidx.work.WorkManager provideWorkManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 提供设备ID管理器
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.DeviceIdManager provideDeviceIdManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 提供同步仓库
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.repository.SyncRepository provideSyncRepository(@org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.database.AppDatabase database, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.remote.api.SyncApi remoteSyncApi, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.DeviceIdManager deviceIdManager, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.domain.repository.SessionRepository sessionRepository, @org.jetbrains.annotations.NotNull()
    javax.inject.Provider<com.example.todoschedule.data.sync.SyncManager> syncManagerProvider) {
        return null;
    }
    
    /**
     * 提供同步API
     *
     * 创建匿名实现替代已删除的SyncApiImpl
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.SyncApi provideSyncApi(@org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.remote.api.SyncApi remoteSyncApi, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.DeviceIdManager deviceIdManager) {
        return null;
    }
    
    /**
     * 提供同步消息上传器
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.SyncMessageUploader provideSyncMessageUploader(@org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.remote.api.SyncApi syncApi, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.database.AppDatabase database, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.DeviceIdManager deviceIdManager) {
        return null;
    }
    
    /**
     * 提供课程适配器
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.adapter.CourseAdapter provideCourseAdapter() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.adapter.TableAdapter provideTableAdapter() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.adapter.CourseNodeAdapter provideCourseNodeAdapter() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter provideOrdinaryScheduleAdapter() {
        return null;
    }
    
    /**
     * 提供Synk适配器注册表
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry provideSynkAdapterRegistry(@org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.adapter.CourseAdapter courseAdapter, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.adapter.TableAdapter tableAdapter, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.adapter.CourseNodeAdapter courseNodeAdapter, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter ordinaryScheduleAdapter) {
        return null;
    }
    
    /**
     * 提供SyncManager实例
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.data.sync.SyncManager provideSyncManager(@org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.repository.SyncRepository syncRepository, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.DeviceIdManager deviceIdManager, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry synkAdapterRegistry, @org.jetbrains.annotations.NotNull()
    com.example.todoschedule.data.sync.CrdtKeyResolver crdtKeyResolver) {
        return null;
    }
}