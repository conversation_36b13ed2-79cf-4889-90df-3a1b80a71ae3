// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CalendarSyncManager_Factory implements Factory<CalendarSyncManager> {
  private final Provider<Context> contextProvider;

  public CalendarSyncManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CalendarSyncManager get() {
    return newInstance(contextProvider.get());
  }

  public static CalendarSyncManager_Factory create(Provider<Context> contextProvider) {
    return new CalendarSyncManager_Factory(contextProvider);
  }

  public static CalendarSyncManager newInstance(Context context) {
    return new CalendarSyncManager(context);
  }
}
