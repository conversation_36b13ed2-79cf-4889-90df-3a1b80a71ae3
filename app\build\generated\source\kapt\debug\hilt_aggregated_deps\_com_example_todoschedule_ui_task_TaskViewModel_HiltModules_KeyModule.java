package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.example.todoschedule.ui.task.TaskViewModel_HiltModules.KeyModule"
)
public class _com_example_todoschedule_ui_task_TaskViewModel_HiltModules_KeyModule {
}
