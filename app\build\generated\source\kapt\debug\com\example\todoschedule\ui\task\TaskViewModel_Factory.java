// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.task;

import com.example.todoschedule.domain.repository.CourseRepository;
import com.example.todoschedule.domain.repository.GlobalSettingRepository;
import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.repository.TableRepository;
import com.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.UpdateOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskViewModel_Factory implements Factory<TaskViewModel> {
  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider;

  private final Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider;

  private final Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider;

  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<GlobalSettingRepository> globalSettingRepositoryProvider;

  private final Provider<TableRepository> tableRepositoryProvider;

  public TaskViewModel_Factory(Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider,
      Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider,
      Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider,
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider) {
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.getOrdinarySchedulesUseCaseProvider = getOrdinarySchedulesUseCaseProvider;
    this.updateOrdinaryScheduleUseCaseProvider = updateOrdinaryScheduleUseCaseProvider;
    this.getDefaultTableTimeConfigUseCaseProvider = getDefaultTableTimeConfigUseCaseProvider;
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.globalSettingRepositoryProvider = globalSettingRepositoryProvider;
    this.tableRepositoryProvider = tableRepositoryProvider;
  }

  @Override
  public TaskViewModel get() {
    return newInstance(sessionRepositoryProvider.get(), getOrdinarySchedulesUseCaseProvider.get(), updateOrdinaryScheduleUseCaseProvider.get(), getDefaultTableTimeConfigUseCaseProvider.get(), courseRepositoryProvider.get(), globalSettingRepositoryProvider.get(), tableRepositoryProvider.get());
  }

  public static TaskViewModel_Factory create(Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider,
      Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider,
      Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider,
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider) {
    return new TaskViewModel_Factory(sessionRepositoryProvider, getOrdinarySchedulesUseCaseProvider, updateOrdinaryScheduleUseCaseProvider, getDefaultTableTimeConfigUseCaseProvider, courseRepositoryProvider, globalSettingRepositoryProvider, tableRepositoryProvider);
  }

  public static TaskViewModel newInstance(SessionRepository sessionRepository,
      GetOrdinarySchedulesUseCase getOrdinarySchedulesUseCase,
      UpdateOrdinaryScheduleUseCase updateOrdinaryScheduleUseCase,
      GetDefaultTableTimeConfigUseCase getDefaultTableTimeConfigUseCase,
      CourseRepository courseRepository, GlobalSettingRepository globalSettingRepository,
      TableRepository tableRepository) {
    return new TaskViewModel(sessionRepository, getOrdinarySchedulesUseCase, updateOrdinaryScheduleUseCase, getDefaultTableTimeConfigUseCase, courseRepository, globalSettingRepository, tableRepository);
  }
}
