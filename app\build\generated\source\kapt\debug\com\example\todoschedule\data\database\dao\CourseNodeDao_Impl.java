package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.entity.CourseNodeEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class CourseNodeDao_Impl implements CourseNodeDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CourseNodeEntity> __insertionAdapterOfCourseNodeEntity;

  private final EntityDeletionOrUpdateAdapter<CourseNodeEntity> __deletionAdapterOfCourseNodeEntity;

  private final EntityDeletionOrUpdateAdapter<CourseNodeEntity> __updateAdapterOfCourseNodeEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteCourseNode;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllNodesOfCourse;

  public CourseNodeDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCourseNodeEntity = new EntityInsertionAdapter<CourseNodeEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `course_node` (`id`,`courseId`,`courseNodeName`,`color`,`room`,`teacher`,`startNode`,`step`,`day`,`startWeek`,`endWeek`,`weekType`,`crdtKey`,`courseCrdtKey`,`update_timestamp`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseNodeEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getCourseId());
        if (entity.getCourseNodeName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCourseNodeName());
        }
        if (entity.getColor() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getColor());
        }
        if (entity.getRoom() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getRoom());
        }
        if (entity.getTeacher() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTeacher());
        }
        statement.bindLong(7, entity.getStartNode());
        statement.bindLong(8, entity.getStep());
        statement.bindLong(9, entity.getDay());
        statement.bindLong(10, entity.getStartWeek());
        statement.bindLong(11, entity.getEndWeek());
        statement.bindLong(12, entity.getWeekType());
        if (entity.getCrdtKey() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getCrdtKey());
        }
        if (entity.getCourseCrdtKey() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getCourseCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getUpdateTimestamp());
        }
      }
    };
    this.__deletionAdapterOfCourseNodeEntity = new EntityDeletionOrUpdateAdapter<CourseNodeEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `course_node` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseNodeEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfCourseNodeEntity = new EntityDeletionOrUpdateAdapter<CourseNodeEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `course_node` SET `id` = ?,`courseId` = ?,`courseNodeName` = ?,`color` = ?,`room` = ?,`teacher` = ?,`startNode` = ?,`step` = ?,`day` = ?,`startWeek` = ?,`endWeek` = ?,`weekType` = ?,`crdtKey` = ?,`courseCrdtKey` = ?,`update_timestamp` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseNodeEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getCourseId());
        if (entity.getCourseNodeName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCourseNodeName());
        }
        if (entity.getColor() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getColor());
        }
        if (entity.getRoom() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getRoom());
        }
        if (entity.getTeacher() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTeacher());
        }
        statement.bindLong(7, entity.getStartNode());
        statement.bindLong(8, entity.getStep());
        statement.bindLong(9, entity.getDay());
        statement.bindLong(10, entity.getStartWeek());
        statement.bindLong(11, entity.getEndWeek());
        statement.bindLong(12, entity.getWeekType());
        if (entity.getCrdtKey() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getCrdtKey());
        }
        if (entity.getCourseCrdtKey() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getCourseCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getUpdateTimestamp());
        }
        statement.bindLong(16, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteCourseNode = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM course_node WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllNodesOfCourse = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM course_node WHERE courseId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertCourseNode(final CourseNodeEntity courseNode,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfCourseNodeEntity.insertAndReturnId(courseNode);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertCourseNodes(final List<CourseNodeEntity> courseNodes,
      final Continuation<? super List<Long>> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<List<Long>>() {
      @Override
      @NonNull
      public List<Long> call() throws Exception {
        __db.beginTransaction();
        try {
          final List<Long> _result = __insertionAdapterOfCourseNodeEntity.insertAndReturnIdsList(courseNodes);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCourseNode(final CourseNodeEntity courseNode,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCourseNodeEntity.handle(courseNode);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCourseNode(final CourseNodeEntity courseNode,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCourseNodeEntity.handle(courseNode);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCourseNode(final int id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteCourseNode.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteCourseNode.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllNodesOfCourse(final int courseId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllNodesOfCourse.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, courseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllNodesOfCourse.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseNodeById(final int id,
      final Continuation<? super CourseNodeEntity> $completion) {
    final String _sql = "SELECT * FROM course_node WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseNodeEntity>() {
      @Override
      @Nullable
      public CourseNodeEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCourseNodeName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNodeName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartNode = CursorUtil.getColumnIndexOrThrow(_cursor, "startNode");
          final int _cursorIndexOfStep = CursorUtil.getColumnIndexOrThrow(_cursor, "step");
          final int _cursorIndexOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "day");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfWeekType = CursorUtil.getColumnIndexOrThrow(_cursor, "weekType");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfCourseCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final CourseNodeEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpCourseId;
            _tmpCourseId = _cursor.getInt(_cursorIndexOfCourseId);
            final String _tmpCourseNodeName;
            if (_cursor.isNull(_cursorIndexOfCourseNodeName)) {
              _tmpCourseNodeName = null;
            } else {
              _tmpCourseNodeName = _cursor.getString(_cursorIndexOfCourseNodeName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartNode;
            _tmpStartNode = _cursor.getInt(_cursorIndexOfStartNode);
            final int _tmpStep;
            _tmpStep = _cursor.getInt(_cursorIndexOfStep);
            final int _tmpDay;
            _tmpDay = _cursor.getInt(_cursorIndexOfDay);
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final int _tmpWeekType;
            _tmpWeekType = _cursor.getInt(_cursorIndexOfWeekType);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpCourseCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCourseCrdtKey)) {
              _tmpCourseCrdtKey = null;
            } else {
              _tmpCourseCrdtKey = _cursor.getString(_cursorIndexOfCourseCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new CourseNodeEntity(_tmpId,_tmpCourseId,_tmpCourseNodeName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartNode,_tmpStep,_tmpDay,_tmpStartWeek,_tmpEndWeek,_tmpWeekType,_tmpCrdtKey,_tmpCourseCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CourseNodeEntity>> getCourseNodesByCourseId(final int courseId) {
    final String _sql = "SELECT * FROM course_node WHERE courseId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, courseId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"course_node"}, new Callable<List<CourseNodeEntity>>() {
      @Override
      @NonNull
      public List<CourseNodeEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCourseNodeName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNodeName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartNode = CursorUtil.getColumnIndexOrThrow(_cursor, "startNode");
          final int _cursorIndexOfStep = CursorUtil.getColumnIndexOrThrow(_cursor, "step");
          final int _cursorIndexOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "day");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfWeekType = CursorUtil.getColumnIndexOrThrow(_cursor, "weekType");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfCourseCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<CourseNodeEntity> _result = new ArrayList<CourseNodeEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseNodeEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpCourseId;
            _tmpCourseId = _cursor.getInt(_cursorIndexOfCourseId);
            final String _tmpCourseNodeName;
            if (_cursor.isNull(_cursorIndexOfCourseNodeName)) {
              _tmpCourseNodeName = null;
            } else {
              _tmpCourseNodeName = _cursor.getString(_cursorIndexOfCourseNodeName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartNode;
            _tmpStartNode = _cursor.getInt(_cursorIndexOfStartNode);
            final int _tmpStep;
            _tmpStep = _cursor.getInt(_cursorIndexOfStep);
            final int _tmpDay;
            _tmpDay = _cursor.getInt(_cursorIndexOfDay);
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final int _tmpWeekType;
            _tmpWeekType = _cursor.getInt(_cursorIndexOfWeekType);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpCourseCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCourseCrdtKey)) {
              _tmpCourseCrdtKey = null;
            } else {
              _tmpCourseCrdtKey = _cursor.getString(_cursorIndexOfCourseCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new CourseNodeEntity(_tmpId,_tmpCourseId,_tmpCourseNodeName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartNode,_tmpStep,_tmpDay,_tmpStartWeek,_tmpEndWeek,_tmpWeekType,_tmpCrdtKey,_tmpCourseCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getIdByCrdtKey(final String crdtKey,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT id FROM course_node WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            if (_cursor.isNull(0)) {
              _result = null;
            } else {
              _result = _cursor.getInt(0);
            }
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseNodeByCrdtKey(final String crdtKey,
      final Continuation<? super CourseNodeEntity> $completion) {
    final String _sql = "SELECT * FROM course_node WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseNodeEntity>() {
      @Override
      @Nullable
      public CourseNodeEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCourseNodeName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNodeName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartNode = CursorUtil.getColumnIndexOrThrow(_cursor, "startNode");
          final int _cursorIndexOfStep = CursorUtil.getColumnIndexOrThrow(_cursor, "step");
          final int _cursorIndexOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "day");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfWeekType = CursorUtil.getColumnIndexOrThrow(_cursor, "weekType");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfCourseCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final CourseNodeEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpCourseId;
            _tmpCourseId = _cursor.getInt(_cursorIndexOfCourseId);
            final String _tmpCourseNodeName;
            if (_cursor.isNull(_cursorIndexOfCourseNodeName)) {
              _tmpCourseNodeName = null;
            } else {
              _tmpCourseNodeName = _cursor.getString(_cursorIndexOfCourseNodeName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartNode;
            _tmpStartNode = _cursor.getInt(_cursorIndexOfStartNode);
            final int _tmpStep;
            _tmpStep = _cursor.getInt(_cursorIndexOfStep);
            final int _tmpDay;
            _tmpDay = _cursor.getInt(_cursorIndexOfDay);
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final int _tmpWeekType;
            _tmpWeekType = _cursor.getInt(_cursorIndexOfWeekType);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpCourseCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCourseCrdtKey)) {
              _tmpCourseCrdtKey = null;
            } else {
              _tmpCourseCrdtKey = _cursor.getString(_cursorIndexOfCourseCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new CourseNodeEntity(_tmpId,_tmpCourseId,_tmpCourseNodeName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartNode,_tmpStep,_tmpDay,_tmpStartWeek,_tmpEndWeek,_tmpWeekType,_tmpCrdtKey,_tmpCourseCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseNodesByCourseCrdtKey(final String courseCrdtKey,
      final Continuation<? super List<CourseNodeEntity>> $completion) {
    final String _sql = "SELECT * FROM course_node WHERE courseCrdtKey = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (courseCrdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, courseCrdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseNodeEntity>>() {
      @Override
      @NonNull
      public List<CourseNodeEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCourseNodeName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNodeName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartNode = CursorUtil.getColumnIndexOrThrow(_cursor, "startNode");
          final int _cursorIndexOfStep = CursorUtil.getColumnIndexOrThrow(_cursor, "step");
          final int _cursorIndexOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "day");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfWeekType = CursorUtil.getColumnIndexOrThrow(_cursor, "weekType");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfCourseCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<CourseNodeEntity> _result = new ArrayList<CourseNodeEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseNodeEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpCourseId;
            _tmpCourseId = _cursor.getInt(_cursorIndexOfCourseId);
            final String _tmpCourseNodeName;
            if (_cursor.isNull(_cursorIndexOfCourseNodeName)) {
              _tmpCourseNodeName = null;
            } else {
              _tmpCourseNodeName = _cursor.getString(_cursorIndexOfCourseNodeName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartNode;
            _tmpStartNode = _cursor.getInt(_cursorIndexOfStartNode);
            final int _tmpStep;
            _tmpStep = _cursor.getInt(_cursorIndexOfStep);
            final int _tmpDay;
            _tmpDay = _cursor.getInt(_cursorIndexOfDay);
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final int _tmpWeekType;
            _tmpWeekType = _cursor.getInt(_cursorIndexOfWeekType);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpCourseCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCourseCrdtKey)) {
              _tmpCourseCrdtKey = null;
            } else {
              _tmpCourseCrdtKey = _cursor.getString(_cursorIndexOfCourseCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new CourseNodeEntity(_tmpId,_tmpCourseId,_tmpCourseNodeName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartNode,_tmpStep,_tmpDay,_tmpStartWeek,_tmpEndWeek,_tmpWeekType,_tmpCrdtKey,_tmpCourseCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
