// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.ordinary_schedule;

import com.example.todoschedule.domain.repository.OrdinaryScheduleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DeleteOrdinaryScheduleUseCase_Factory implements Factory<DeleteOrdinaryScheduleUseCase> {
  private final Provider<OrdinaryScheduleRepository> repositoryProvider;

  public DeleteOrdinaryScheduleUseCase_Factory(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public DeleteOrdinaryScheduleUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static DeleteOrdinaryScheduleUseCase_Factory create(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    return new DeleteOrdinaryScheduleUseCase_Factory(repositoryProvider);
  }

  public static DeleteOrdinaryScheduleUseCase newInstance(OrdinaryScheduleRepository repository) {
    return new DeleteOrdinaryScheduleUseCase(repository);
  }
}
