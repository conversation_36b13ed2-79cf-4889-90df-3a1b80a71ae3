// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.database.dao.GlobalSettingDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideGlobalSettingDaoFactory implements Factory<GlobalSettingDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideGlobalSettingDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public GlobalSettingDao get() {
    return provideGlobalSettingDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideGlobalSettingDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideGlobalSettingDaoFactory(databaseProvider);
  }

  public static GlobalSettingDao provideGlobalSettingDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideGlobalSettingDao(database));
  }
}
