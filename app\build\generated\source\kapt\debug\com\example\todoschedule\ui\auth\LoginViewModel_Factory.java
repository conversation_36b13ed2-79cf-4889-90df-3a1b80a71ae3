// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.auth;

import com.example.todoschedule.data.sync.SyncManager;
import com.example.todoschedule.domain.use_case.auth.LoginUserUseCase;
import com.example.todoschedule.domain.use_case.auth.SaveLoginSessionUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LoginViewModel_Factory implements Factory<LoginViewModel> {
  private final Provider<LoginUserUseCase> loginUserUseCaseProvider;

  private final Provider<SaveLoginSessionUseCase> saveLoginSessionUseCaseProvider;

  private final Provider<SyncManager> syncManagerProvider;

  public LoginViewModel_Factory(Provider<LoginUserUseCase> loginUserUseCaseProvider,
      Provider<SaveLoginSessionUseCase> saveLoginSessionUseCaseProvider,
      Provider<SyncManager> syncManagerProvider) {
    this.loginUserUseCaseProvider = loginUserUseCaseProvider;
    this.saveLoginSessionUseCaseProvider = saveLoginSessionUseCaseProvider;
    this.syncManagerProvider = syncManagerProvider;
  }

  @Override
  public LoginViewModel get() {
    return newInstance(loginUserUseCaseProvider.get(), saveLoginSessionUseCaseProvider.get(), syncManagerProvider.get());
  }

  public static LoginViewModel_Factory create(Provider<LoginUserUseCase> loginUserUseCaseProvider,
      Provider<SaveLoginSessionUseCase> saveLoginSessionUseCaseProvider,
      Provider<SyncManager> syncManagerProvider) {
    return new LoginViewModel_Factory(loginUserUseCaseProvider, saveLoginSessionUseCaseProvider, syncManagerProvider);
  }

  public static LoginViewModel newInstance(LoginUserUseCase loginUserUseCase,
      SaveLoginSessionUseCase saveLoginSessionUseCase, SyncManager syncManager) {
    return new LoginViewModel(loginUserUseCase, saveLoginSessionUseCase, syncManager);
  }
}
