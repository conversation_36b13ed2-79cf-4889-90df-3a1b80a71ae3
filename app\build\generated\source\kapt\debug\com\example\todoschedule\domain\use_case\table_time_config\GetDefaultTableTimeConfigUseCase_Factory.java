// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.table_time_config;

import com.example.todoschedule.domain.repository.TableTimeConfigRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetDefaultTableTimeConfigUseCase_Factory implements Factory<GetDefaultTableTimeConfigUseCase> {
  private final Provider<TableTimeConfigRepository> repositoryProvider;

  public GetDefaultTableTimeConfigUseCase_Factory(
      Provider<TableTimeConfigRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetDefaultTableTimeConfigUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetDefaultTableTimeConfigUseCase_Factory create(
      Provider<TableTimeConfigRepository> repositoryProvider) {
    return new GetDefaultTableTimeConfigUseCase_Factory(repositoryProvider);
  }

  public static GetDefaultTableTimeConfigUseCase newInstance(TableTimeConfigRepository repository) {
    return new GetDefaultTableTimeConfigUseCase(repository);
  }
}
