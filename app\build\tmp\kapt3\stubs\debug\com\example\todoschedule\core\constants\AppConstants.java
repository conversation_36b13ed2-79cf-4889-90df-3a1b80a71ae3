package com.example.todoschedule.core.constants;

/**
 * 应用中使用的常量
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\f\b\u00c7\u0002\u0018\u00002\u00020\u0001:\t\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0014\u0010\u0007\u001a\u00020\bX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants;", "", "()V", "DEFAULT_COURSE_COLOR", "Lcom/example/todoschedule/ui/theme/ColorSchemeEnum$ONERROR;", "getDEFAULT_COURSE_COLOR", "()Lcom/example/todoschedule/ui/theme/ColorSchemeEnum$ONERROR;", "UserPreferencesName", "", "getUserPreferencesName", "()Ljava/lang/String;", "Api", "Database", "Ids", "Notification", "Priority", "Routes", "Status", "Sync", "WeekTypes", "app_debug"})
public final class AppConstants {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String UserPreferencesName = "user_preferences";
    @org.jetbrains.annotations.NotNull()
    private static final com.example.todoschedule.ui.theme.ColorSchemeEnum.ONERROR DEFAULT_COURSE_COLOR = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.todoschedule.core.constants.AppConstants INSTANCE = null;
    
    private AppConstants() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUserPreferencesName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.todoschedule.ui.theme.ColorSchemeEnum.ONERROR getDEFAULT_COURSE_COLOR() {
        return null;
    }
    
    /**
     * 网络API相关常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0006\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0003\t\n\u000bB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Api;", "", "()V", "BASE_URL", "", "CONNECT_TIMEOUT", "", "READ_TIMEOUT", "WRITE_TIMEOUT", "Endpoints", "Headers", "Sync", "app_debug"})
    public static final class Api {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BASE_URL = "10.172.60.115";
        public static final long CONNECT_TIMEOUT = 15L;
        public static final long READ_TIMEOUT = 15L;
        public static final long WRITE_TIMEOUT = 15L;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Api INSTANCE = null;
        
        private Api() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Api$Endpoints;", "", "()V", "LOGIN", "", "REGISTER", "app_debug"})
        public static final class Endpoints {
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String LOGIN = "/users/login";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String REGISTER = "/users/register";
            @org.jetbrains.annotations.NotNull()
            public static final com.example.todoschedule.core.constants.AppConstants.Api.Endpoints INSTANCE = null;
            
            private Endpoints() {
                super();
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Api$Headers;", "", "()V", "AUTHORIZATION", "", "BEARER_PREFIX", "DEVICE_ID", "app_debug"})
        public static final class Headers {
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String AUTHORIZATION = "Authorization";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String BEARER_PREFIX = "Bearer ";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String DEVICE_ID = "X-Device-ID";
            @org.jetbrains.annotations.NotNull()
            public static final com.example.todoschedule.core.constants.AppConstants.Api.Headers INSTANCE = null;
            
            private Headers() {
                super();
            }
        }
        
        /**
         * 同步相关API
         */
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Api$Sync;", "", "()V", "DOWNLOAD_ALL_MESSAGES", "", "DOWNLOAD_ENTITY_EXCLUDE_ORIGIN", "DOWNLOAD_ENTITY_MESSAGES", "DOWNLOAD_EXCLUDE_ORIGIN", "REGISTER_DEVICE", "UPLOAD_MESSAGES", "app_debug"})
        public static final class Sync {
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String REGISTER_DEVICE = "/sync/device/register";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String UPLOAD_MESSAGES = "/sync/messages/{entityType}";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String DOWNLOAD_ALL_MESSAGES = "/sync/messages/all";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String DOWNLOAD_ENTITY_MESSAGES = "/sync/messages/{entityType}";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String DOWNLOAD_EXCLUDE_ORIGIN = "/sync/messages/all/exclude-origin";
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String DOWNLOAD_ENTITY_EXCLUDE_ORIGIN = "/sync/messages/{entityType}/exclude-origin";
            @org.jetbrains.annotations.NotNull()
            public static final com.example.todoschedule.core.constants.AppConstants.Api.Sync INSTANCE = null;
            
            private Sync() {
                super();
            }
        }
    }
    
    /**
     * 数据库相关常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0001\u000fB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Database;", "", "()V", "DATABASE_NAME", "", "DB_NAME", "DB_VERSION", "", "DEFAULT_TABLE_NAME", "DEFAULT_TABLE_START_DATE", "Lkotlinx/datetime/LocalDate;", "getDEFAULT_TABLE_START_DATE", "()Lkotlinx/datetime/LocalDate;", "DEFAULT_TIME_CONFIG_TABLE_NAME", "DEFAULT_USER_NAME", "Sync", "app_debug"})
    public static final class Database {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DB_NAME = "todo_schedule.db";
        public static final int DB_VERSION = 6;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DEFAULT_TABLE_NAME = "\u9ed8\u8ba4\u8bfe\u8868";
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.datetime.LocalDate DEFAULT_TABLE_START_DATE = null;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DEFAULT_USER_NAME = "\u9ed8\u8ba4\u7528\u6237";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DEFAULT_TIME_CONFIG_TABLE_NAME = "\u9ed8\u8ba4\u65f6\u95f4\u914d\u7f6e\u8868";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DATABASE_NAME = "todo_schedule_database";
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Database INSTANCE = null;
        
        private Database() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.datetime.LocalDate getDEFAULT_TABLE_START_DATE() {
            return null;
        }
        
        /**
         * 数据同步相关
         */
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Database$Sync;", "", "()V", "SYNC_MESSAGE_TABLE", "", "app_debug"})
        public static final class Sync {
            @org.jetbrains.annotations.NotNull()
            public static final java.lang.String SYNC_MESSAGE_TABLE = "sync_message";
            @org.jetbrains.annotations.NotNull()
            public static final com.example.todoschedule.core.constants.AppConstants.Database.Sync INSTANCE = null;
            
            private Sync() {
                super();
            }
        }
    }
    
    /**
     * ID相关常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Ids;", "", "()V", "INVALID_COURSE_ID", "", "INVALID_ID", "INVALID_SETTING_ID", "INVALID_TABLE_ID", "INVALID_USER_ID", "", "app_debug"})
    public static final class Ids {
        public static final int INVALID_ID = -1;
        public static final long INVALID_USER_ID = -1L;
        public static final int INVALID_TABLE_ID = -1;
        public static final int INVALID_SETTING_ID = -1;
        public static final int INVALID_COURSE_ID = -1;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Ids INSTANCE = null;
        
        private Ids() {
            super();
        }
    }
    
    /**
     * 通知相关常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Notification;", "", "()V", "DEFAULT_NOTIFY_MINUTES", "", "STYLE_DETAILED", "STYLE_SILENT", "STYLE_SIMPLE", "app_debug"})
    public static final class Notification {
        public static final int DEFAULT_NOTIFY_MINUTES = 15;
        public static final int STYLE_SIMPLE = 0;
        public static final int STYLE_DETAILED = 1;
        public static final int STYLE_SILENT = 2;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Notification INSTANCE = null;
        
        private Notification() {
            super();
        }
    }
    
    /**
     * 优先级常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Priority;", "", "()V", "HIGH", "", "LOW", "MEDIUM", "app_debug"})
    public static final class Priority {
        public static final int LOW = 0;
        public static final int MEDIUM = 1;
        public static final int HIGH = 2;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Priority INSTANCE = null;
        
        private Priority() {
            super();
        }
    }
    
    /**
     * 路由相关常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Routes;", "", "()V", "START_SCREEN", "Lcom/example/todoschedule/ui/navigation/AppRoutes$Home;", "getSTART_SCREEN", "()Lcom/example/todoschedule/ui/navigation/AppRoutes$Home;", "app_debug"})
    public static final class Routes {
        @org.jetbrains.annotations.NotNull()
        private static final com.example.todoschedule.ui.navigation.AppRoutes.Home START_SCREEN = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Routes INSTANCE = null;
        
        private Routes() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.todoschedule.ui.navigation.AppRoutes.Home getSTART_SCREEN() {
            return null;
        }
    }
    
    /**
     * 状态常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Status;", "", "()V", "COMPLETED", "", "IN_PROGRESS", "PENDING", "app_debug"})
    public static final class Status {
        public static final int PENDING = 0;
        public static final int IN_PROGRESS = 1;
        public static final int COMPLETED = 2;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Status INSTANCE = null;
        
        private Status() {
            super();
        }
    }
    
    /**
     * 同步相关常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$Sync;", "", "()V", "SYNC_INITIAL_DELAY", "", "SYNC_INTERVAL", "app_debug"})
    public static final class Sync {
        public static final long SYNC_INITIAL_DELAY = 10000L;
        public static final long SYNC_INTERVAL = 10000L;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.Sync INSTANCE = null;
        
        private Sync() {
            super();
        }
    }
    
    /**
     * 周类型常量
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/example/todoschedule/core/constants/AppConstants$WeekTypes;", "", "()V", "ALL", "", "EVEN", "ODD", "app_debug"})
    public static final class WeekTypes {
        public static final int ALL = 0;
        public static final int ODD = 1;
        public static final int EVEN = 2;
        @org.jetbrains.annotations.NotNull()
        public static final com.example.todoschedule.core.constants.AppConstants.WeekTypes INSTANCE = null;
        
        private WeekTypes() {
            super();
        }
    }
}