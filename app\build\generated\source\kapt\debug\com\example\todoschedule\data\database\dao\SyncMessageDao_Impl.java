package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.entity.SyncMessageEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class SyncMessageDao_Impl implements SyncMessageDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SyncMessageEntity> __insertionAdapterOfSyncMessageEntity;

  private final EntityDeletionOrUpdateAdapter<SyncMessageEntity> __deletionAdapterOfSyncMessageEntity;

  private final EntityDeletionOrUpdateAdapter<SyncMessageEntity> __updateAdapterOfSyncMessageEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProcessedMessages;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  public SyncMessageDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSyncMessageEntity = new EntityInsertionAdapter<SyncMessageEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `sync_message` (`sync_id`,`crdt_key`,`entity_type`,`operation_type`,`created_at`,`device_id`,`timestamp_wall_clock`,`timestamp_logical`,`timestamp_node_id`,`payload`,`user_id`,`sync_status`,`last_sync_attempt`,`sync_error`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SyncMessageEntity entity) {
        statement.bindLong(1, entity.getSyncId());
        if (entity.getCrdtKey() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCrdtKey());
        }
        if (entity.getEntityType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getEntityType());
        }
        if (entity.getOperationType() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getOperationType());
        }
        statement.bindLong(5, entity.getCreatedAt());
        if (entity.getDeviceId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getDeviceId());
        }
        statement.bindLong(7, entity.getTimestampWallClock());
        statement.bindLong(8, entity.getTimestampLogical());
        if (entity.getTimestampNodeId() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getTimestampNodeId());
        }
        if (entity.getPayload() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getPayload());
        }
        statement.bindLong(11, entity.getUserId());
        if (entity.getSyncStatus() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getSyncStatus());
        }
        if (entity.getLastSyncAttempt() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getLastSyncAttempt());
        }
        if (entity.getSyncError() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getSyncError());
        }
      }
    };
    this.__deletionAdapterOfSyncMessageEntity = new EntityDeletionOrUpdateAdapter<SyncMessageEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `sync_message` WHERE `sync_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SyncMessageEntity entity) {
        statement.bindLong(1, entity.getSyncId());
      }
    };
    this.__updateAdapterOfSyncMessageEntity = new EntityDeletionOrUpdateAdapter<SyncMessageEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `sync_message` SET `sync_id` = ?,`crdt_key` = ?,`entity_type` = ?,`operation_type` = ?,`created_at` = ?,`device_id` = ?,`timestamp_wall_clock` = ?,`timestamp_logical` = ?,`timestamp_node_id` = ?,`payload` = ?,`user_id` = ?,`sync_status` = ?,`last_sync_attempt` = ?,`sync_error` = ? WHERE `sync_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SyncMessageEntity entity) {
        statement.bindLong(1, entity.getSyncId());
        if (entity.getCrdtKey() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCrdtKey());
        }
        if (entity.getEntityType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getEntityType());
        }
        if (entity.getOperationType() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getOperationType());
        }
        statement.bindLong(5, entity.getCreatedAt());
        if (entity.getDeviceId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getDeviceId());
        }
        statement.bindLong(7, entity.getTimestampWallClock());
        statement.bindLong(8, entity.getTimestampLogical());
        if (entity.getTimestampNodeId() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getTimestampNodeId());
        }
        if (entity.getPayload() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getPayload());
        }
        statement.bindLong(11, entity.getUserId());
        if (entity.getSyncStatus() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getSyncStatus());
        }
        if (entity.getLastSyncAttempt() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getLastSyncAttempt());
        }
        if (entity.getSyncError() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getSyncError());
        }
        statement.bindLong(15, entity.getSyncId());
      }
    };
    this.__preparedStmtOfDeleteProcessedMessages = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM sync_message WHERE sync_status = 'SYNCED' AND created_at < ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM sync_message";
        return _query;
      }
    };
  }

  @Override
  public Object insert(final SyncMessageEntity message,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfSyncMessageEntity.insertAndReturnId(message);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAll(final List<SyncMessageEntity> messages,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSyncMessageEntity.insert(messages);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final SyncMessageEntity message,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfSyncMessageEntity.handle(message);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object update(final SyncMessageEntity message,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSyncMessageEntity.handle(message);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAll(final List<SyncMessageEntity> messages,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSyncMessageEntity.handleMultiple(messages);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProcessedMessages(final long beforeTimestamp,
      final Continuation<? super Integer> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProcessedMessages.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, beforeTimestamp);
        try {
          __db.beginTransaction();
          try {
            final Integer _result = _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteProcessedMessages.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getBySyncId(final int syncId,
      final Continuation<? super SyncMessageEntity> $completion) {
    final String _sql = "SELECT * FROM sync_message WHERE sync_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, syncId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SyncMessageEntity>() {
      @Override
      @Nullable
      public SyncMessageEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final SyncMessageEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _result = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SyncMessageEntity>> getAllMessages() {
    final String _sql = "SELECT * FROM sync_message ORDER BY created_at DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"sync_message"}, new Callable<List<SyncMessageEntity>>() {
      @Override
      @NonNull
      public List<SyncMessageEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final List<SyncMessageEntity> _result = new ArrayList<SyncMessageEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SyncMessageEntity _item;
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _item = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getPendingMessages(
      final Continuation<? super List<SyncMessageEntity>> $completion) {
    final String _sql = "SELECT * FROM sync_message WHERE sync_status = 'PENDING' ORDER BY created_at ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SyncMessageEntity>>() {
      @Override
      @NonNull
      public List<SyncMessageEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final List<SyncMessageEntity> _result = new ArrayList<SyncMessageEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SyncMessageEntity _item;
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _item = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPendingMessagesByUserId(final int userId,
      final Continuation<? super List<SyncMessageEntity>> $completion) {
    final String _sql = "SELECT * FROM sync_message WHERE sync_status = 'PENDING' AND user_id = ? ORDER BY created_at ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SyncMessageEntity>>() {
      @Override
      @NonNull
      public List<SyncMessageEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final List<SyncMessageEntity> _result = new ArrayList<SyncMessageEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SyncMessageEntity _item;
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _item = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPendingMessagesByType(final String entityType,
      final Continuation<? super List<SyncMessageEntity>> $completion) {
    final String _sql = "SELECT * FROM sync_message WHERE sync_status = 'PENDING' AND entity_type = ? ORDER BY created_at ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (entityType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, entityType);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SyncMessageEntity>>() {
      @Override
      @NonNull
      public List<SyncMessageEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final List<SyncMessageEntity> _result = new ArrayList<SyncMessageEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SyncMessageEntity _item;
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _item = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMessagesByDeviceId(final String deviceId,
      final Continuation<? super List<SyncMessageEntity>> $completion) {
    final String _sql = "SELECT * FROM sync_message WHERE device_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (deviceId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, deviceId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SyncMessageEntity>>() {
      @Override
      @NonNull
      public List<SyncMessageEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final List<SyncMessageEntity> _result = new ArrayList<SyncMessageEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SyncMessageEntity _item;
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _item = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getFailedMessages(final Continuation<? super List<SyncMessageEntity>> $completion) {
    final String _sql = "SELECT * FROM sync_message WHERE sync_status = 'FAILED'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SyncMessageEntity>>() {
      @Override
      @NonNull
      public List<SyncMessageEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final List<SyncMessageEntity> _result = new ArrayList<SyncMessageEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SyncMessageEntity _item;
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _item = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLatestMessageForEntity(final String crdtKey, final String entityType,
      final Continuation<? super SyncMessageEntity> $completion) {
    final String _sql = "SELECT * FROM sync_message WHERE crdt_key = ? AND entity_type = ? ORDER BY timestamp_wall_clock DESC, timestamp_logical DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    _argIndex = 2;
    if (entityType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, entityType);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SyncMessageEntity>() {
      @Override
      @Nullable
      public SyncMessageEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSyncId = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_id");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdt_key");
          final int _cursorIndexOfEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "entity_type");
          final int _cursorIndexOfOperationType = CursorUtil.getColumnIndexOrThrow(_cursor, "operation_type");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfDeviceId = CursorUtil.getColumnIndexOrThrow(_cursor, "device_id");
          final int _cursorIndexOfTimestampWallClock = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_wall_clock");
          final int _cursorIndexOfTimestampLogical = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_logical");
          final int _cursorIndexOfTimestampNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp_node_id");
          final int _cursorIndexOfPayload = CursorUtil.getColumnIndexOrThrow(_cursor, "payload");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfSyncStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_status");
          final int _cursorIndexOfLastSyncAttempt = CursorUtil.getColumnIndexOrThrow(_cursor, "last_sync_attempt");
          final int _cursorIndexOfSyncError = CursorUtil.getColumnIndexOrThrow(_cursor, "sync_error");
          final SyncMessageEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpSyncId;
            _tmpSyncId = _cursor.getInt(_cursorIndexOfSyncId);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpEntityType;
            if (_cursor.isNull(_cursorIndexOfEntityType)) {
              _tmpEntityType = null;
            } else {
              _tmpEntityType = _cursor.getString(_cursorIndexOfEntityType);
            }
            final String _tmpOperationType;
            if (_cursor.isNull(_cursorIndexOfOperationType)) {
              _tmpOperationType = null;
            } else {
              _tmpOperationType = _cursor.getString(_cursorIndexOfOperationType);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpDeviceId;
            if (_cursor.isNull(_cursorIndexOfDeviceId)) {
              _tmpDeviceId = null;
            } else {
              _tmpDeviceId = _cursor.getString(_cursorIndexOfDeviceId);
            }
            final long _tmpTimestampWallClock;
            _tmpTimestampWallClock = _cursor.getLong(_cursorIndexOfTimestampWallClock);
            final long _tmpTimestampLogical;
            _tmpTimestampLogical = _cursor.getLong(_cursorIndexOfTimestampLogical);
            final String _tmpTimestampNodeId;
            if (_cursor.isNull(_cursorIndexOfTimestampNodeId)) {
              _tmpTimestampNodeId = null;
            } else {
              _tmpTimestampNodeId = _cursor.getString(_cursorIndexOfTimestampNodeId);
            }
            final String _tmpPayload;
            if (_cursor.isNull(_cursorIndexOfPayload)) {
              _tmpPayload = null;
            } else {
              _tmpPayload = _cursor.getString(_cursorIndexOfPayload);
            }
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpSyncStatus;
            if (_cursor.isNull(_cursorIndexOfSyncStatus)) {
              _tmpSyncStatus = null;
            } else {
              _tmpSyncStatus = _cursor.getString(_cursorIndexOfSyncStatus);
            }
            final Long _tmpLastSyncAttempt;
            if (_cursor.isNull(_cursorIndexOfLastSyncAttempt)) {
              _tmpLastSyncAttempt = null;
            } else {
              _tmpLastSyncAttempt = _cursor.getLong(_cursorIndexOfLastSyncAttempt);
            }
            final String _tmpSyncError;
            if (_cursor.isNull(_cursorIndexOfSyncError)) {
              _tmpSyncError = null;
            } else {
              _tmpSyncError = _cursor.getString(_cursorIndexOfSyncError);
            }
            _result = new SyncMessageEntity(_tmpSyncId,_tmpCrdtKey,_tmpEntityType,_tmpOperationType,_tmpCreatedAt,_tmpDeviceId,_tmpTimestampWallClock,_tmpTimestampLogical,_tmpTimestampNodeId,_tmpPayload,_tmpUserId,_tmpSyncStatus,_tmpLastSyncAttempt,_tmpSyncError);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object markAsProcessed(final List<Integer> syncIds,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
        _stringBuilder.append("UPDATE sync_message SET sync_status = 'SYNCED' WHERE sync_id IN (");
        final int _inputSize = syncIds.size();
        StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
        _stringBuilder.append(")");
        final String _sql = _stringBuilder.toString();
        final SupportSQLiteStatement _stmt = __db.compileStatement(_sql);
        int _argIndex = 1;
        for (Integer _item : syncIds) {
          if (_item == null) {
            _stmt.bindNull(_argIndex);
          } else {
            _stmt.bindLong(_argIndex, _item);
          }
          _argIndex++;
        }
        __db.beginTransaction();
        try {
          _stmt.executeUpdateDelete();
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
