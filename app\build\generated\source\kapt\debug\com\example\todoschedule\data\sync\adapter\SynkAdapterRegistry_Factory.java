// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync.adapter;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SynkAdapterRegistry_Factory implements Factory<SynkAdapterRegistry> {
  private final Provider<CourseAdapter> courseAdapterProvider;

  private final Provider<TableAdapter> tableAdapterProvider;

  private final Provider<CourseNodeAdapter> courseNodeAdapterProvider;

  private final Provider<OrdinaryScheduleAdapter> ordinaryScheduleAdapterProvider;

  public SynkAdapterRegistry_Factory(Provider<CourseAdapter> courseAdapterProvider,
      Provider<TableAdapter> tableAdapterProvider,
      Provider<CourseNodeAdapter> courseNodeAdapterProvider,
      Provider<OrdinaryScheduleAdapter> ordinaryScheduleAdapterProvider) {
    this.courseAdapterProvider = courseAdapterProvider;
    this.tableAdapterProvider = tableAdapterProvider;
    this.courseNodeAdapterProvider = courseNodeAdapterProvider;
    this.ordinaryScheduleAdapterProvider = ordinaryScheduleAdapterProvider;
  }

  @Override
  public SynkAdapterRegistry get() {
    return newInstance(courseAdapterProvider.get(), tableAdapterProvider.get(), courseNodeAdapterProvider.get(), ordinaryScheduleAdapterProvider.get());
  }

  public static SynkAdapterRegistry_Factory create(Provider<CourseAdapter> courseAdapterProvider,
      Provider<TableAdapter> tableAdapterProvider,
      Provider<CourseNodeAdapter> courseNodeAdapterProvider,
      Provider<OrdinaryScheduleAdapter> ordinaryScheduleAdapterProvider) {
    return new SynkAdapterRegistry_Factory(courseAdapterProvider, tableAdapterProvider, courseNodeAdapterProvider, ordinaryScheduleAdapterProvider);
  }

  public static SynkAdapterRegistry newInstance(CourseAdapter courseAdapter,
      TableAdapter tableAdapter, CourseNodeAdapter courseNodeAdapter,
      OrdinaryScheduleAdapter ordinaryScheduleAdapter) {
    return new SynkAdapterRegistry(courseAdapter, tableAdapter, courseNodeAdapter, ordinaryScheduleAdapter);
  }
}
