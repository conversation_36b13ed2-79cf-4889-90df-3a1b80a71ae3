package com.example.todoschedule.data.sync;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = SyncService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
public interface SyncService_GeneratedInjector {
  void injectSyncService(SyncService syncService);
}
