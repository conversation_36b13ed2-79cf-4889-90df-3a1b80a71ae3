package com.example.todoschedule.ui.sync

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.example.todoschedule.data.repository.SyncRepository
import com.example.todoschedule.data.sync.SyncManager
import com.example.todoschedule.data.sync.SyncWorker
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

/**
 * 同步视图模型
 *
 * 负责管理同步状态并提供UI层所需的数据和操作
 */
@HiltViewModel
class SyncViewModel @Inject constructor(
    private val syncRepository: SyncRepository,
    private val syncManager: SyncManager,
    @ApplicationContext private val context: Context
) : ViewModel() {
    
    // 同步状态
    private val _syncState = MutableStateFlow(SyncState.IDLE)
    val syncState: StateFlow<SyncState> = _syncState.asStateFlow()
    
    // 最后同步时间
    private val _lastSyncTime = MutableStateFlow<Long?>(null)
    val lastSyncTime: StateFlow<Long?> = _lastSyncTime.asStateFlow()
    
    // 待同步消息数量
    private val _pendingMessageCount = MutableStateFlow(0)
    val pendingMessageCount: StateFlow<Int> = _pendingMessageCount.asStateFlow()
    
    // 错误消息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 同步状态文本
    private val _syncStatusText = MutableStateFlow("")
    val syncStatusText: StateFlow<String> = _syncStatusText
    
    // 最后同步时间文本
    private val _lastSyncTimeText = MutableStateFlow("")
    val lastSyncTimeText: StateFlow<String> = _lastSyncTimeText
    
    init {
        // 初始化状态文本
        updateSyncStatusText()
        updateLastSyncTimeText()
        
        // 初始化监听WorkManager工作状态
        viewModelScope.launch {
            WorkManager.getInstance(context)
                .getWorkInfosForUniqueWorkLiveData(SYNC_WORK_NAME)
                .observeForever { workInfoList ->
                    if (workInfoList != null && workInfoList.isNotEmpty()) {
                        val workInfo = workInfoList[0]
                        updateSyncState(workInfo.state)
                    }
                }
        }
        
        // 监听待同步消息数量
        viewModelScope.launch {
            try {
                val pendingMessages = syncRepository.getPendingMessages()
                _pendingMessageCount.value = pendingMessages.size
            } catch (e: Exception) {
                _errorMessage.value = "读取待同步消息失败: ${e.message}"
            }
        }
        
        // 初始化ViewModel内部状态
        initialize()
    }
    
    /**
     * 立即执行同步
     */
    fun syncNow() {
        _syncState.value = SyncState.SYNCING
        viewModelScope.launch {
            try {
                val success = syncManager.syncNow()
                _syncState.value = if (success) SyncState.SYNCED else SyncState.FAILED
                if (!success) {
                    _errorMessage.value = "同步失败，请检查网络连接"
                } else {
                    updateLastSyncTime()
                    updatePendingMessageCount()
                }
            } catch (e: Exception) {
                _syncState.value = SyncState.FAILED
                _errorMessage.value = e.message ?: "同步操作出错"
                Log.e(TAG, "同步错误", e)
            }
        }
    }
    
    /**
     * 安排周期性同步
     */
    fun schedulePeriodic() {
        SyncWorker.schedulePeriodic(context)
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 更新同步状态文本
     */
    private fun updateSyncStatusText() {
        _syncStatusText.value = when (_syncState.value) {
            SyncState.IDLE -> "等待同步"
            SyncState.SYNCING -> "正在同步..."
            SyncState.SYNCED -> "同步成功"
            SyncState.FAILED -> "同步失败"
        }
    }
    
    /**
     * 更新最后同步时间文本
     */
    private fun updateLastSyncTimeText() {
        val time = _lastSyncTime.value
        _lastSyncTimeText.value = if (time != null) {
            "上次同步: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date(time))}"
        } else {
            "从未同步"
        }
    }
    
    /**
     * 更新同步状态
     */
    private fun updateSyncState(workState: WorkInfo.State) {
        _syncState.value = when (workState) {
            WorkInfo.State.RUNNING -> SyncState.SYNCING
            WorkInfo.State.SUCCEEDED -> SyncState.SYNCED
            WorkInfo.State.FAILED -> SyncState.FAILED
            else -> SyncState.IDLE
        }
        updateSyncStatusText()
    }
    
    /**
     * 更新最后同步时间
     */
    private fun updateLastSyncTime() {
        val currentTime = System.currentTimeMillis()
        _lastSyncTime.value = currentTime
        updateLastSyncTimeText()
        saveLastSyncTimeToPrefs(currentTime)
    }
    
    /**
     * 更新待同步消息数量
     */
    private fun updatePendingMessageCount() {
        viewModelScope.launch {
            try {
                val pendingMessages = syncRepository.getPendingMessages()
                _pendingMessageCount.value = pendingMessages.size
            } catch (e: Exception) {
                Log.e(TAG, "获取待同步消息失败", e)
            }
        }
    }
    
    /**
     * 初始化ViewModel内部状态
     */
    private fun initialize() {
        // 从SharedPreferences加载最后同步时间
        loadLastSyncTimeFromPrefs()
        
        // 加载待同步消息数量
        updatePendingMessageCount()
        
        // 更新状态文本
        updateSyncStatusText()
    }
    
    /**
     * 从SharedPreferences获取最后同步时间
     */
    private fun loadLastSyncTimeFromPrefs() {
        val sharedPrefs = context.getSharedPreferences(SYNC_PREFS_NAME, Context.MODE_PRIVATE)
        val lastSyncTime = sharedPrefs.getLong(PREF_LAST_SYNC_TIME, 0)
        if (lastSyncTime > 0) {
            _lastSyncTime.value = lastSyncTime
            updateLastSyncTimeText()
        }
    }
    
    /**
     * 保存最后同步时间到SharedPreferences
     */
    private fun saveLastSyncTimeToPrefs(timeMs: Long) {
        val sharedPrefs = context.getSharedPreferences(SYNC_PREFS_NAME, Context.MODE_PRIVATE)
        sharedPrefs.edit().putLong(PREF_LAST_SYNC_TIME, timeMs).apply()
    }
    
    companion object {
        private const val SYNC_WORK_NAME = "sync_work"
        private const val TAG = "SyncViewModel"
        private const val SYNC_PREFS_NAME = "sync_preferences"
        private const val PREF_LAST_SYNC_TIME = "last_sync_time"
    }
    
    /**
     * 同步状态枚举
     */
    enum class SyncState {
        IDLE,       // 空闲
        SYNCING,    // 同步中
        SYNCED,     // 同步完成
        FAILED      // 同步失败
    }
} 