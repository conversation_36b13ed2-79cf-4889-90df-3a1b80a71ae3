// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync.adapter;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OrdinaryScheduleAdapter_Factory implements Factory<OrdinaryScheduleAdapter> {
  @Override
  public OrdinaryScheduleAdapter get() {
    return newInstance();
  }

  public static OrdinaryScheduleAdapter_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static OrdinaryScheduleAdapter newInstance() {
    return new OrdinaryScheduleAdapter();
  }

  private static final class InstanceHolder {
    private static final OrdinaryScheduleAdapter_Factory INSTANCE = new OrdinaryScheduleAdapter_Factory();
  }
}
