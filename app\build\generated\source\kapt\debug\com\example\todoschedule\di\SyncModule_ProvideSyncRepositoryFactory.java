// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.remote.api.SyncApi;
import com.example.todoschedule.data.repository.SyncRepository;
import com.example.todoschedule.data.sync.DeviceIdManager;
import com.example.todoschedule.data.sync.SyncManager;
import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideSyncRepositoryFactory implements Factory<SyncRepository> {
  private final Provider<AppDatabase> databaseProvider;

  private final Provider<SyncApi> remoteSyncApiProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<SyncManager> syncManagerProvider;

  public SyncModule_ProvideSyncRepositoryFactory(Provider<AppDatabase> databaseProvider,
      Provider<SyncApi> remoteSyncApiProvider, Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    this.databaseProvider = databaseProvider;
    this.remoteSyncApiProvider = remoteSyncApiProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.syncManagerProvider = syncManagerProvider;
  }

  @Override
  public SyncRepository get() {
    return provideSyncRepository(databaseProvider.get(), remoteSyncApiProvider.get(), deviceIdManagerProvider.get(), sessionRepositoryProvider.get(), syncManagerProvider);
  }

  public static SyncModule_ProvideSyncRepositoryFactory create(
      Provider<AppDatabase> databaseProvider, Provider<SyncApi> remoteSyncApiProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    return new SyncModule_ProvideSyncRepositoryFactory(databaseProvider, remoteSyncApiProvider, deviceIdManagerProvider, sessionRepositoryProvider, syncManagerProvider);
  }

  public static SyncRepository provideSyncRepository(AppDatabase database, SyncApi remoteSyncApi,
      DeviceIdManager deviceIdManager, SessionRepository sessionRepository,
      Provider<SyncManager> syncManagerProvider) {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideSyncRepository(database, remoteSyncApi, deviceIdManager, sessionRepository, syncManagerProvider));
  }
}
