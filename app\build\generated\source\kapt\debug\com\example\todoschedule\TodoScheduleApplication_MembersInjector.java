// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule;

import com.example.todoschedule.core.utils.DevUtils;
import com.example.todoschedule.data.sync.SyncManager;
import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TodoScheduleApplication_MembersInjector implements MembersInjector<TodoScheduleApplication> {
  private final Provider<DevUtils> devUtilsProvider;

  private final Provider<SyncManager> syncManagerProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  public TodoScheduleApplication_MembersInjector(Provider<DevUtils> devUtilsProvider,
      Provider<SyncManager> syncManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    this.devUtilsProvider = devUtilsProvider;
    this.syncManagerProvider = syncManagerProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  public static MembersInjector<TodoScheduleApplication> create(Provider<DevUtils> devUtilsProvider,
      Provider<SyncManager> syncManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new TodoScheduleApplication_MembersInjector(devUtilsProvider, syncManagerProvider, sessionRepositoryProvider);
  }

  @Override
  public void injectMembers(TodoScheduleApplication instance) {
    injectDevUtils(instance, devUtilsProvider.get());
    injectSyncManager(instance, syncManagerProvider.get());
    injectSessionRepository(instance, sessionRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.example.todoschedule.TodoScheduleApplication.devUtils")
  public static void injectDevUtils(TodoScheduleApplication instance, DevUtils devUtils) {
    instance.devUtils = devUtils;
  }

  @InjectedFieldSignature("com.example.todoschedule.TodoScheduleApplication.syncManager")
  public static void injectSyncManager(TodoScheduleApplication instance, SyncManager syncManager) {
    instance.syncManager = syncManager;
  }

  @InjectedFieldSignature("com.example.todoschedule.TodoScheduleApplication.sessionRepository")
  public static void injectSessionRepository(TodoScheduleApplication instance,
      SessionRepository sessionRepository) {
    instance.sessionRepository = sessionRepository;
  }
}
