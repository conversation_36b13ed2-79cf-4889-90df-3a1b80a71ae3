// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.settings;

import android.app.Application;
import com.example.todoschedule.core.utils.DevUtils;
import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.use_case.auth.ClearLoginSessionUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<ClearLoginSessionUseCase> clearLoginSessionUseCaseProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<DevUtils> devUtilsProvider;

  public SettingsViewModel_Factory(Provider<Application> applicationProvider,
      Provider<ClearLoginSessionUseCase> clearLoginSessionUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider, Provider<DevUtils> devUtilsProvider) {
    this.applicationProvider = applicationProvider;
    this.clearLoginSessionUseCaseProvider = clearLoginSessionUseCaseProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.devUtilsProvider = devUtilsProvider;
  }

  @Override
  public SettingsViewModel get() {
    SettingsViewModel instance = newInstance(applicationProvider.get(), clearLoginSessionUseCaseProvider.get(), sessionRepositoryProvider.get());
    SettingsViewModel_MembersInjector.injectDevUtils(instance, devUtilsProvider.get());
    return instance;
  }

  public static SettingsViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<ClearLoginSessionUseCase> clearLoginSessionUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider, Provider<DevUtils> devUtilsProvider) {
    return new SettingsViewModel_Factory(applicationProvider, clearLoginSessionUseCaseProvider, sessionRepositoryProvider, devUtilsProvider);
  }

  public static SettingsViewModel newInstance(Application application,
      ClearLoginSessionUseCase clearLoginSessionUseCase, SessionRepository sessionRepository) {
    return new SettingsViewModel(application, clearLoginSessionUseCase, sessionRepository);
  }
}
