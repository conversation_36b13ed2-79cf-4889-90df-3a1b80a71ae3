// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.ordinary_schedule;

import com.example.todoschedule.domain.repository.OrdinaryScheduleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetOrdinarySchedulesUseCase_Factory implements Factory<GetOrdinarySchedulesUseCase> {
  private final Provider<OrdinaryScheduleRepository> repositoryProvider;

  public GetOrdinarySchedulesUseCase_Factory(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetOrdinarySchedulesUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetOrdinarySchedulesUseCase_Factory create(
      Provider<OrdinaryScheduleRepository> repositoryProvider) {
    return new GetOrdinarySchedulesUseCase_Factory(repositoryProvider);
  }

  public static GetOrdinarySchedulesUseCase newInstance(OrdinaryScheduleRepository repository) {
    return new GetOrdinarySchedulesUseCase(repository);
  }
}
