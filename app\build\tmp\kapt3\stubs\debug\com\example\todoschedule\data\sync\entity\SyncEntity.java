package com.example.todoschedule.data.sync.entity;

/**
 * 同步实体接口
 *
 * 所有需要在设备间同步的实体都必须实现这个接口。该接口定义了CRDT数据同步所需的
 * 基本属性，包括全局唯一标识符、时间戳和节点信息。
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\b\bf\u0018\u00002\u00020\u0001J\b\u0010\u0015\u001a\u00020\u0003H&J\b\u0010\u0016\u001a\u00020\u0003H&R\u0012\u0010\u0002\u001a\u00020\u0003X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0004\u0010\u0005R\u0012\u0010\u0006\u001a\u00020\u0007X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\tR\u0012\u0010\n\u001a\u00020\u000bX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\rR\u0012\u0010\u000e\u001a\u00020\u000fX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\u0010R\u0012\u0010\u0011\u001a\u00020\u0003X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0012\u0010\u0005R\u0012\u0010\u0013\u001a\u00020\u0007X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0014\u0010\t\u00a8\u0006\u0017"}, d2 = {"Lcom/example/todoschedule/data/sync/entity/SyncEntity;", "", "crdtKey", "", "getCrdtKey", "()Ljava/lang/String;", "createdAt", "Lkotlinx/datetime/Instant;", "getCreatedAt", "()Lkotlinx/datetime/Instant;", "id", "", "getId", "()I", "isDeleted", "", "()Z", "nodeId", "getNodeId", "updatedAt", "getUpdatedAt", "getEntityType", "toSyncPayload", "app_debug"})
public abstract interface SyncEntity {
    
    /**
     * 实体的本地数据库ID
     *
     * 注意：这个ID只在本地设备上有意义，不应用于跨设备同步
     */
    public abstract int getId();
    
    /**
     * 实体的CRDT全局唯一标识符
     *
     * 用于在所有设备间唯一标识该实体，通常使用UUID生成
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.lang.String getCrdtKey();
    
    /**
     * 实体的创建时间戳
     *
     * 记录实体首次创建的时间，用于CRDT的因果一致性维护
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.datetime.Instant getCreatedAt();
    
    /**
     * 实体的最后更新时间戳
     *
     * 记录实体最后一次更新的时间，用于冲突解决
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.datetime.Instant getUpdatedAt();
    
    /**
     * 删除标记
     *
     * CRDT系统中不会真正删除数据，而是标记为已删除
     * 这样可以正确处理删除操作与其他操作之间的冲突
     */
    public abstract boolean isDeleted();
    
    /**
     * 创建该实体的设备/节点ID
     *
     * 记录首次创建该实体的设备标识符，用于追踪数据来源
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.lang.String getNodeId();
    
    /**
     * 获取该实体对应的实体类型
     *
     * @return 实体类型标识符
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.lang.String getEntityType();
    
    /**
     * 将实体转换为同步消息负载
     *
     * @return 序列化后的实体数据
     */
    @org.jetbrains.annotations.NotNull()
    public abstract java.lang.String toSyncPayload();
}