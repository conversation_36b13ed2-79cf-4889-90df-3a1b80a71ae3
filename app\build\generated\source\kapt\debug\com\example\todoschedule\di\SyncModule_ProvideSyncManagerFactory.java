// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.repository.SyncRepository;
import com.example.todoschedule.data.sync.CrdtKeyResolver;
import com.example.todoschedule.data.sync.DeviceIdManager;
import com.example.todoschedule.data.sync.SyncManager;
import com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideSyncManagerFactory implements Factory<SyncManager> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  private final Provider<SynkAdapterRegistry> synkAdapterRegistryProvider;

  private final Provider<CrdtKeyResolver> crdtKeyResolverProvider;

  public SyncModule_ProvideSyncManagerFactory(Provider<SyncRepository> syncRepositoryProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<SynkAdapterRegistry> synkAdapterRegistryProvider,
      Provider<CrdtKeyResolver> crdtKeyResolverProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
    this.synkAdapterRegistryProvider = synkAdapterRegistryProvider;
    this.crdtKeyResolverProvider = crdtKeyResolverProvider;
  }

  @Override
  public SyncManager get() {
    return provideSyncManager(syncRepositoryProvider.get(), deviceIdManagerProvider.get(), synkAdapterRegistryProvider.get(), crdtKeyResolverProvider.get());
  }

  public static SyncModule_ProvideSyncManagerFactory create(
      Provider<SyncRepository> syncRepositoryProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<SynkAdapterRegistry> synkAdapterRegistryProvider,
      Provider<CrdtKeyResolver> crdtKeyResolverProvider) {
    return new SyncModule_ProvideSyncManagerFactory(syncRepositoryProvider, deviceIdManagerProvider, synkAdapterRegistryProvider, crdtKeyResolverProvider);
  }

  public static SyncManager provideSyncManager(SyncRepository syncRepository,
      DeviceIdManager deviceIdManager, SynkAdapterRegistry synkAdapterRegistry,
      CrdtKeyResolver crdtKeyResolver) {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideSyncManager(syncRepository, deviceIdManager, synkAdapterRegistry, crdtKeyResolver));
  }
}
