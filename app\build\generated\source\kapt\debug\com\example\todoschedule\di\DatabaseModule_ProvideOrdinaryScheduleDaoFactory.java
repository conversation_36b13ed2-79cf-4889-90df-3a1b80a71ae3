// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.database.dao.OrdinaryScheduleDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideOrdinaryScheduleDaoFactory implements Factory<OrdinaryScheduleDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideOrdinaryScheduleDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public OrdinaryScheduleDao get() {
    return provideOrdinaryScheduleDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideOrdinaryScheduleDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideOrdinaryScheduleDaoFactory(databaseProvider);
  }

  public static OrdinaryScheduleDao provideOrdinaryScheduleDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideOrdinaryScheduleDao(database));
  }
}
