package com.example.todoschedule.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.todoschedule.data.database.entity.TableEntity
import kotlinx.coroutines.flow.Flow

/** 课表DAO接口 */
@Dao
interface TableDao {
    /** 获取所有课表 */
    @Query("SELECT * FROM `table` ORDER BY listPosition")
    fun getAllTables(): Flow<List<TableEntity>>

    /** 获取默认课表（目前仅取第一个） */
    @Query("SELECT * FROM `table` ORDER BY id LIMIT 1")
    fun getDefaultTable(): Flow<TableEntity?>

    /** [Flow] 根据ID观察课表 */
    @Query("SELECT * FROM `table` WHERE id = :tableId")
    fun getTableById(tableId: Int): Flow<TableEntity?>

    /** [Flow] 根据用户ID观察课表 */
    @Query("SELECT * FROM `table` WHERE userId = :userId")
    fun getTableByUserId(userId: Int): Flow<List<TableEntity>>

    /** [Suspend] 根据ID获取课表（一次性） */
    @Query("SELECT * FROM `table` WHERE id = :tableId")
    suspend fun fetchTableById(tableId: Int): TableEntity?

    /** [Suspend] 根据用户ID获取课表（一次性） */
    @Query("SELECT * FROM `table` WHERE userId = :userId")
    suspend fun fetchTablesByUserId(userId: Int): List<TableEntity>

    /** 插入课表 */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTable(table: TableEntity): Long

    /** 更新课表 */
    @Update
    suspend fun updateTable(table: TableEntity)

    /** 删除课表 */
    @Query("DELETE FROM `table` WHERE id = :tableId")
    suspend fun deleteTable(tableId: Int)
    
    /** 根据crdtKey查询课表本地ID */
    @Query("SELECT id FROM `table` WHERE crdtKey = :crdtKey LIMIT 1")
    suspend fun getIdByCrdtKey(crdtKey: String): Int?
    
    /** 根据crdtKey查询课表实体 */
    @Query("SELECT * FROM `table` WHERE crdtKey = :crdtKey LIMIT 1")
    suspend fun getTableByCrdtKey(crdtKey: String): TableEntity?
    
    /** 根据userCrdtKey查询课表列表 */
    @Query("SELECT * FROM `table` WHERE userCrdtKey = :userCrdtKey")
    suspend fun getTablesByUserCrdtKey(userCrdtKey: String): List<TableEntity>
}
