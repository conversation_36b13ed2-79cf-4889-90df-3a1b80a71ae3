package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel_HiltModules.KeyModule"
)
public class _com_example_todoschedule_ui_ordinaryschedule_AddEditOrdinaryScheduleViewModel_HiltModules_KeyModule {
}
