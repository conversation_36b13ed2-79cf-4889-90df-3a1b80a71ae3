// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.task;

import com.example.todoschedule.core.utils.PermissionManager;
import com.example.todoschedule.domain.repository.CourseRepository;
import com.example.todoschedule.domain.repository.GlobalSettingRepository;
import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.repository.TableRepository;
import com.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase;
import com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase;
import com.example.todoschedule.domain.utils.CalendarSyncManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskCalendarViewModel_Factory implements Factory<TaskCalendarViewModel> {
  private final Provider<CalendarSyncManager> calendarSyncManagerProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider;

  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<GlobalSettingRepository> globalSettingRepositoryProvider;

  private final Provider<TableRepository> tableRepositoryProvider;

  private final Provider<PermissionManager> permissionManagerProvider;

  private final Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider;

  public TaskCalendarViewModel_Factory(Provider<CalendarSyncManager> calendarSyncManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider,
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<PermissionManager> permissionManagerProvider,
      Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider) {
    this.calendarSyncManagerProvider = calendarSyncManagerProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.getOrdinarySchedulesUseCaseProvider = getOrdinarySchedulesUseCaseProvider;
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.globalSettingRepositoryProvider = globalSettingRepositoryProvider;
    this.tableRepositoryProvider = tableRepositoryProvider;
    this.permissionManagerProvider = permissionManagerProvider;
    this.getDefaultTableTimeConfigUseCaseProvider = getDefaultTableTimeConfigUseCaseProvider;
  }

  @Override
  public TaskCalendarViewModel get() {
    return newInstance(calendarSyncManagerProvider.get(), sessionRepositoryProvider.get(), getOrdinarySchedulesUseCaseProvider.get(), courseRepositoryProvider.get(), globalSettingRepositoryProvider.get(), tableRepositoryProvider.get(), permissionManagerProvider.get(), getDefaultTableTimeConfigUseCaseProvider.get());
  }

  public static TaskCalendarViewModel_Factory create(
      Provider<CalendarSyncManager> calendarSyncManagerProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GetOrdinarySchedulesUseCase> getOrdinarySchedulesUseCaseProvider,
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<PermissionManager> permissionManagerProvider,
      Provider<GetDefaultTableTimeConfigUseCase> getDefaultTableTimeConfigUseCaseProvider) {
    return new TaskCalendarViewModel_Factory(calendarSyncManagerProvider, sessionRepositoryProvider, getOrdinarySchedulesUseCaseProvider, courseRepositoryProvider, globalSettingRepositoryProvider, tableRepositoryProvider, permissionManagerProvider, getDefaultTableTimeConfigUseCaseProvider);
  }

  public static TaskCalendarViewModel newInstance(CalendarSyncManager calendarSyncManager,
      SessionRepository sessionRepository, GetOrdinarySchedulesUseCase getOrdinarySchedulesUseCase,
      CourseRepository courseRepository, GlobalSettingRepository globalSettingRepository,
      TableRepository tableRepository, PermissionManager permissionManager,
      GetDefaultTableTimeConfigUseCase getDefaultTableTimeConfigUseCase) {
    return new TaskCalendarViewModel(calendarSyncManager, sessionRepository, getOrdinarySchedulesUseCase, courseRepository, globalSettingRepository, tableRepository, permissionManager, getDefaultTableTimeConfigUseCase);
  }
}
