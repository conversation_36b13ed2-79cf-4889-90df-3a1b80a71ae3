# 客户端基于Synk库的分阶段实现设计方案

## 第一阶段：基础设施准备

### 1. 项目依赖配置

1. 在 `build.gradle`添加Synk库及其依赖：

```kotlin
// 添加Synk库
implementation("com.github.CharlieTap:synk:$synkVersion")
// 混合逻辑时钟依赖
implementation("com.github.CharlieTap:hlc:$hlcVersion")
// 支持数据序列化
implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:$serializationVersion")
```

### 2. 数据库迁移设计

1. 为所有需同步的实体类添加CRDT字段：

```kotlin
@Entity
data class Course(
    @PrimaryKey val id: Long = 0,
    val name: String,
    val lecturer: String,
    // 原有业务字段
  
    // CRDT字段
    val crdtKey: String,                // 全局唯一标识符
    val isDeleted: Boolean = false,     // 逻辑删除标记
    val hlcTimestamp: Long,             // 混合逻辑时钟时间戳
    val originDeviceId: String,         // 来源设备ID
    val operationType: String           // 操作类型：ADD/UPDATE/DELETE
)
```

2. 创建Room数据库迁移脚本：

```kotlin
val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 为Course表添加CRDT字段
        database.execSQL("ALTER TABLE Course ADD COLUMN crdtKey TEXT NOT NULL DEFAULT ''")
        database.execSQL("ALTER TABLE Course ADD COLUMN isDeleted INTEGER NOT NULL DEFAULT 0")
        database.execSQL("ALTER TABLE Course ADD COLUMN hlcTimestamp INTEGER NOT NULL DEFAULT 0")
        database.execSQL("ALTER TABLE Course ADD COLUMN originDeviceId TEXT NOT NULL DEFAULT ''")
        database.execSQL("ALTER TABLE Course ADD COLUMN operationType TEXT NOT NULL DEFAULT 'ADD'")
      
        // 为其他需同步的表添加类似字段
    }
}
```

### 3. 设备标识实现

```kotlin
class DeviceIdManager @Inject constructor(
    private val dataStore: DataStore<Preferences>
) {
    companion object {
        private val DEVICE_ID_KEY = stringPreferencesKey("device_id")
    }
  
    suspend fun getOrCreateDeviceId(): String {
        return dataStore.data.map { preferences ->
            preferences[DEVICE_ID_KEY] ?: generateAndSaveDeviceId()
        }.first()
    }
  
    private suspend fun generateAndSaveDeviceId(): String {
        val deviceId = UUID.randomUUID().toString()
        dataStore.edit { preferences ->
            preferences[DEVICE_ID_KEY] = deviceId
        }
        return deviceId
    }
}
```

### 4. 网络模块配置

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Provides
    @Singleton
    fun provideSyncApiService(retrofit: Retrofit): SyncApiService {
        return retrofit.create(SyncApiService::class.java)
    }
  
    @Provides
    @Singleton
    fun provideRetrofit(
        okHttpClient: OkHttpClient,
        gson: Gson
    ): Retrofit {
        return Retrofit.Builder()
            .baseUrl(AppConstants.API_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }
  
    @Provides
    @Singleton
    fun provideOkHttpClient(
        authInterceptor: AuthInterceptor,
        deviceIdInterceptor: DeviceIdInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .addInterceptor(deviceIdInterceptor)
            .build()
    }
}
```

## 第二阶段：SynkAdapter实现与本地存储

### 1. SynkAdapter实现

每种实体类型实现一个适配器：

```kotlin
class CourseAdapter : SynkAdapter<Course> {
  
    override fun key(value: Course): String = value.crdtKey
  
    override fun serialize(value: Course): Map<String, Any?> {
        return mapOf(
            "id" to value.id,
            "name" to value.name,
            "lecturer" to value.lecturer,
            // 序列化其他业务字段
          
            // CRDT字段也需要序列化
            "crdtKey" to value.crdtKey,
            "isDeleted" to value.isDeleted,
            "hlcTimestamp" to value.hlcTimestamp,
            "originDeviceId" to value.originDeviceId,
            "operationType" to value.operationType
        )
    }
  
    override fun deserialize(serialized: Map<String, Any?>): Course {
        return Course(
            id = (serialized["id"] as Number).toLong(),
            name = serialized["name"] as String,
            lecturer = serialized["lecturer"] as String,
            // 反序列化其他业务字段
          
            // CRDT字段
            crdtKey = serialized["crdtKey"] as String,
            isDeleted = serialized["isDeleted"] as Boolean,
            hlcTimestamp = (serialized["hlcTimestamp"] as Number).toLong(),
            originDeviceId = serialized["originDeviceId"] as String,
            operationType = serialized["operationType"] as String
        )
    }
  
    override fun merge(local: Course, remote: Course): Course {
        // 基于hlcTimestamp的合并策略
        return if (remote.hlcTimestamp > local.hlcTimestamp) {
            remote
        } else {
            local
        }
    }
}
```

### 2. 变更跟踪与消息生成

扩展Repository层实现CRDT消息生成：

```kotlin
class CourseRepositoryImpl @Inject constructor(
    private val courseDao: CourseDao,
    private val deviceIdManager: DeviceIdManager,
    private val hlcClock: HybridLogicalClock,
    private val syncManager: SyncManager
) : CourseRepository {

    override suspend fun insertCourse(course: Course): Long {
        // 生成CRDT元数据
        val deviceId = deviceIdManager.getOrCreateDeviceId()
        val timestamp = hlcClock.now()
        val crdtKey = UUID.randomUUID().toString()
      
        val crdtCourse = course.copy(
            crdtKey = crdtKey,
            hlcTimestamp = timestamp,
            originDeviceId = deviceId,
            operationType = "ADD",
            isDeleted = false
        )
      
        // 保存到本地数据库
        val id = courseDao.insert(crdtCourse)
      
        // 生成并存储Synk消息
        syncManager.trackChange(EntityType.COURSE, crdtCourse, null)
      
        return id
    }
  
    override suspend fun updateCourse(course: Course) {
        // 查询原有数据
        val oldCourse = courseDao.getCourseById(course.id)
      
        // 生成CRDT元数据
        val deviceId = deviceIdManager.getOrCreateDeviceId()
        val timestamp = hlcClock.now()
      
        val crdtCourse = course.copy(
            crdtKey = oldCourse.crdtKey,
            hlcTimestamp = timestamp,
            originDeviceId = deviceId,
            operationType = "UPDATE"
        )
      
        // 保存到本地数据库
        courseDao.update(crdtCourse)
      
        // 生成并存储Synk消息
        syncManager.trackChange(EntityType.COURSE, crdtCourse, oldCourse)
    }
  
    override suspend fun deleteCourse(courseId: Long) {
        // 查询要删除的课程
        val course = courseDao.getCourseById(courseId)
      
        // 软删除：生成带有isDeleted=true的新版本
        val deviceId = deviceIdManager.getOrCreateDeviceId()
        val timestamp = hlcClock.now()
      
        val deletedCourse = course.copy(
            isDeleted = true,
            hlcTimestamp = timestamp,
            originDeviceId = deviceId,
            operationType = "DELETE"
        )
      
        // 更新到本地数据库（保留记录，标记为已删除）
        courseDao.update(deletedCourse)
      
        // 生成并存储Synk消息
        syncManager.trackChange(EntityType.COURSE, deletedCourse, course)
    }
}
```

### 3. 消息存储队列实现

```kotlin
@Entity
data class SyncMessage(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val entityType: String,
    val messageData: String, // JSON字符串
    val timestamp: Long,
    val status: String = "PENDING" // PENDING, SYNCED, FAILED
)

@Dao
interface SyncMessageDao {
    @Insert
    suspend fun insert(message: SyncMessage): Long
  
    @Query("SELECT * FROM SyncMessage WHERE status = 'PENDING' ORDER BY timestamp ASC")
    fun getPendingMessages(): Flow<List<SyncMessage>>
  
    @Update
    suspend fun update(message: SyncMessage)
}

class SyncManager @Inject constructor(
    private val syncMessageDao: SyncMessageDao,
    private val gson: Gson
) {
    suspend fun trackChange(entityType: EntityType, newData: Any, oldData: Any?) {
        val message = SyncMessage(
            entityType = entityType.name,
            messageData = gson.toJson(newData),
            timestamp = System.currentTimeMillis()
        )
      
        syncMessageDao.insert(message)
    }
}
```

## 第三阶段：网络同步与冲突解决

### 1. API服务接口

```kotlin
interface SyncApiService {
    @POST("sync/device/register")
    suspend fun registerDevice(
        @Body request: DeviceRegisterRequest
    ): Response<DeviceResponse>
  
    @POST("sync/messages/{entityType}")
    suspend fun uploadMessages(
        @Path("entityType") entityType: String,
        @Body messages: List<String>
    ): Response<UploadResponse>
  
    @GET("sync/messages")
    suspend fun downloadMessages(
        @Query("since") since: Long? = null
    ): Response<DownloadResponse>
  
    @GET("sync/messages/{entityType}")
    suspend fun downloadMessagesByType(
        @Path("entityType") entityType: String,
        @Query("since") since: Long? = null
    ): Response<DownloadResponse>
}
```

### 2. 同步服务实现

```kotlin
@Singleton
class SyncRepositoryImpl @Inject constructor(
    private val syncApiService: SyncApiService,
    private val syncMessageDao: SyncMessageDao,
    private val deviceIdManager: DeviceIdManager,
    private val dataStore: DataStore<Preferences>,
    private val gson: Gson
) : SyncRepository {
  
    companion object {
        private val LAST_SYNC_TIMESTAMP = longPreferencesKey("last_sync_timestamp")
    }

    override suspend fun uploadPendingMessages() {
        // 按实体类型分组获取待同步消息
        val pendingMessages = syncMessageDao.getPendingMessages().first()
        val messagesByType = pendingMessages.groupBy { it.entityType }
      
        // 对每种实体类型的消息进行上传
        messagesByType.forEach { (entityType, messages) ->
            try {
                val messageDataList = messages.map { it.messageData }
                val response = syncApiService.uploadMessages(entityType, messageDataList)
              
                if (response.isSuccessful) {
                    // 标记消息为已同步
                    messages.forEach { message ->
                        syncMessageDao.update(message.copy(status = "SYNCED"))
                    }
                }
            } catch (e: Exception) {
                // 处理网络错误
                Log.e("SyncRepository", "Upload failed for $entityType", e)
            }
        }
    }

    override suspend fun downloadNewMessages() {
        try {
            // 获取上次同步时间戳
            val lastSyncTimestamp = dataStore.data.map { preferences ->
                preferences[LAST_SYNC_TIMESTAMP] ?: 0
            }.first()
          
            // 从服务器获取新消息
            val response = syncApiService.downloadMessages(lastSyncTimestamp)
          
            if (response.isSuccessful) {
                val downloadResponse = response.body()
                downloadResponse?.messages?.forEach { serverMessage ->
                    // 根据消息类型获取对应的适配器
                    val adapter = getAdapterForEntityType(serverMessage.entityType)
                  
                    // 解析服务器消息
                    val remoteData = gson.fromJson(serverMessage.messageData, adapter.javaClass)
                  
                    // 获取本地数据
                    val entityKey = adapter.key(remoteData)
                    val localData = getLocalDataByKey(serverMessage.entityType, entityKey)
                  
                    if (localData != null) {
                        // 有本地数据，需要解决冲突
                        val mergedData = adapter.merge(localData, remoteData)
                        // 更新到本地数据库
                        updateLocalData(serverMessage.entityType, mergedData)
                    } else {
                        // 无本地数据，直接插入
                        insertLocalData(serverMessage.entityType, remoteData)
                    }
                }
              
                // 更新最后同步时间戳
                dataStore.edit { preferences ->
                    preferences[LAST_SYNC_TIMESTAMP] = System.currentTimeMillis()
                }
            }
        } catch (e: Exception) {
            // 处理网络错误
            Log.e("SyncRepository", "Download failed", e)
        }
    }
  
    private fun getAdapterForEntityType(entityType: String): SynkAdapter<*> {
        return when (entityType) {
            "COURSE" -> CourseAdapter()
            "ORDINARY_SCHEDULE" -> OrdinaryScheduleAdapter()
            // 其他实体类型的适配器
            else -> throw IllegalArgumentException("Unknown entity type: $entityType")
        }
    }
  
    // 这些方法需要根据具体数据访问层实现
    private suspend fun getLocalDataByKey(entityType: String, key: String): Any? {
        // 实现从本地数据库获取特定entityType和key的数据
        return null
    }
  
    private suspend fun updateLocalData(entityType: String, data: Any) {
        // 实现更新本地数据库中的数据
    }
  
    private suspend fun insertLocalData(entityType: String, data: Any) {
        // 实现向本地数据库插入新数据
    }
}
```

### 3. 同步触发机制

```kotlin
@HiltWorker
class SyncWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val syncRepository: SyncRepository,
    private val connectivityManager: ConnectivityManager
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        // 检查网络连接
        if (!isNetworkAvailable()) {
            return Result.retry()
        }
      
        try {
            // 上传本地待同步消息
            syncRepository.uploadPendingMessages()
          
            // 下载新消息
            syncRepository.downloadNewMessages()
          
            return Result.success()
        } catch (e: Exception) {
            Log.e("SyncWorker", "Sync failed", e)
            return Result.retry()
        }
    }
  
    private fun isNetworkAvailable(): Boolean {
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        return capabilities != null && (
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        )
    }
  
    companion object {
        fun schedulePeriodic(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()
              
            val syncRequest = PeriodicWorkRequestBuilder<SyncWorker>(
                15, TimeUnit.MINUTES,  // 每15分钟执行一次
                5, TimeUnit.MINUTES    // 灵活窗口
            )
            .setConstraints(constraints)
            .build()
          
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                "sync_work",
                ExistingPeriodicWorkPolicy.KEEP,
                syncRequest
            )
        }
      
        fun scheduleImmediate(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()
              
            val syncRequest = OneTimeWorkRequestBuilder<SyncWorker>()
                .setConstraints(constraints)
                .build()
              
            WorkManager.getInstance(context).enqueue(syncRequest)
        }
    }
}
```

## 第四阶段：用户界面与体验优化

### 1. 同步状态管理

```kotlin
@HiltViewModel
class SyncViewModel @Inject constructor(
    private val syncRepository: SyncRepository,
    private val workManager: WorkManager
) : ViewModel() {

    // 同步状态
    private val _syncState = MutableStateFlow(SyncState.IDLE)
    val syncState: StateFlow<SyncState> = _syncState.asStateFlow()
  
    // 最后同步时间
    private val _lastSyncTime = MutableStateFlow<Long?>(null)
    val lastSyncTime: StateFlow<Long?> = _lastSyncTime.asStateFlow()
  
    // 待同步消息数量
    private val _pendingMessageCount = MutableStateFlow(0)
    val pendingMessageCount: StateFlow<Int> = _pendingMessageCount.asStateFlow()
  
    init {
        // 监听WorkManager工作状态
        viewModelScope.launch {
            workManager.getWorkInfosForUniqueWorkLiveData("sync_work")
                .asFlow()
                .collect { workInfoList ->
                    if (workInfoList.isNotEmpty()) {
                        val workInfo = workInfoList[0]
                        when (workInfo.state) {
                            WorkInfo.State.RUNNING -> _syncState.value = SyncState.SYNCING
                            WorkInfo.State.SUCCEEDED -> {
                                _syncState.value = SyncState.SUCCESS
                                _lastSyncTime.value = System.currentTimeMillis()
                            }
                            WorkInfo.State.FAILED -> _syncState.value = SyncState.ERROR
                            else -> _syncState.value = SyncState.IDLE
                        }
                    }
                }
        }
      
        // 监听待同步消息数量
        viewModelScope.launch {
            syncRepository.getPendingMessageCount().collect { count ->
                _pendingMessageCount.value = count
            }
        }
    }
  
    // 手动触发同步
    fun syncNow() {
        _syncState.value = SyncState.SYNCING
        SyncWorker.scheduleImmediate(context)
    }
}

enum class SyncState {
    IDLE, SYNCING, SUCCESS, ERROR
}
```

### 2. 同步状态指示UI组件

```kotlin
@Composable
fun SyncStatusIndicator(
    syncState: SyncState,
    lastSyncTime: Long?,
    pendingCount: Int,
    onSyncNowClick: () -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(8.dp)
    ) {
        // 同步状态图标
        val iconRes = when (syncState) {
            SyncState.SYNCING -> R.drawable.ic_sync_in_progress
            SyncState.SUCCESS -> R.drawable.ic_sync_success
            SyncState.ERROR -> R.drawable.ic_sync_error
            SyncState.IDLE -> R.drawable.ic_sync
        }
      
        Icon(
            painter = painterResource(id = iconRes),
            contentDescription = null,
            tint = when (syncState) {
                SyncState.SUCCESS -> Color.Green
                SyncState.ERROR -> Color.Red
                else -> LocalContentColor.current
            }
        )
      
        Spacer(modifier = Modifier.width(8.dp))
      
        // 同步状态文本
        val statusText = when (syncState) {
            SyncState.SYNCING -> "正在同步..."
            SyncState.SUCCESS -> "同步成功"
            SyncState.ERROR -> "同步失败"
            SyncState.IDLE -> "等待同步"
        }
      
        Column {
            Text(text = statusText)
          
            if (lastSyncTime != null) {
                Text(
                    text = "上次同步: ${SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date(lastSyncTime))}",
                    style = MaterialTheme.typography.caption
                )
            }
          
            if (pendingCount > 0) {
                Text(
                    text = "待同步: $pendingCount 项",
                    style = MaterialTheme.typography.caption
                )
            }
        }
      
        Spacer(modifier = Modifier.weight(1f))
      
        // 手动同步按钮
        if (syncState != SyncState.SYNCING) {
            IconButton(onClick = onSyncNowClick) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_sync_now),
                    contentDescription = "立即同步"
                )
            }
        } else {
            // 同步中显示进度指示器
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                strokeWidth = 2.dp
            )
        }
    }
}
```

### 3. 网络状态监听与同步触发

```kotlin
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private lateinit var connectivityManager: ConnectivityManager
    private lateinit var networkCallback: ConnectivityManager.NetworkCallback
  
    @Inject
    lateinit var workManager: WorkManager
  
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
      
        // 设置网络状态监听
        setupNetworkMonitoring()
      
        // 注册设备并启动定期同步
        lifecycleScope.launch {
            registerDeviceIfNeeded()
            SyncWorker.schedulePeriodic(this@MainActivity)
        }
    }
  
    private fun setupNetworkMonitoring() {
        connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
      
        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                // 网络恢复时触发同步
                SyncWorker.scheduleImmediate(this@MainActivity)
            }
        }
      
        // 注册网络回调
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
          
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
    }
  
    private suspend fun registerDeviceIfNeeded() {
        // 实现设备注册逻辑
    }
  
    override fun onDestroy() {
        super.onDestroy()
        // 取消注册网络回调
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }
}
```

### 4. 用户反馈与错误处理

```kotlin
@Composable
fun SyncErrorDialog(
    errorMessage: String,
    onRetry: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("同步错误") },
        text = { Text(errorMessage) },
        confirmButton = {
            Button(onClick = onRetry) {
                Text("重试")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("稍后再试")
            }
        }
    )
}

// 在ViewModel中添加错误处理
class SyncViewModel @Inject constructor(/* ... */) : ViewModel() {
    // ...
  
    private val _syncError = MutableStateFlow<String?>(null)
    val syncError: StateFlow<String?> = _syncError.asStateFlow()
  
    fun syncNow() {
        viewModelScope.launch {
            _syncState.value = SyncState.SYNCING
            try {
                syncRepository.uploadPendingMessages()
                syncRepository.downloadNewMessages()
                _syncState.value = SyncState.SUCCESS
                _lastSyncTime.value = System.currentTimeMillis()
            } catch (e: Exception) {
                _syncState.value = SyncState.ERROR
                when (e) {
                    is IOException -> _syncError.value = "网络连接问题，请检查网络设置"
                    is HttpException -> _syncError.value = "服务器错误 (${e.code()})"
                    else -> _syncError.value = "同步失败: ${e.message}"
                }
            }
        }
    }
  
    fun clearError() {
        _syncError.value = null
    }
}
```

这个分阶段设计方案涵盖了基于Synk库实现客户端数据同步的全过程，从基础设施准备、CRDT适配与本地存储，到网络同步与冲突解决，最后完善用户界面体验。每个阶段都包含详细的代码示例，可以指导开发团队逐步实现这个复杂的功能。
