package com.example.todoschedule.data.sync

import com.example.todoschedule.data.sync.dto.TimestampDto
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * TimestampDto序列化测试
 * 验证序列化格式是否符合API要求
 */
class TimestampDtoTest {
    
    private val json = Json { encodeDefaults = true }
    
    @Test
    fun `test timestamp serialization to long value`() {
        // 创建测试数据
        val timestamp = TimestampDto(
            wallClockTime = 1621234567890,
            logicalTime = 42,
            nodeId = "device-123"
        )
        
        // 序列化
        val serialized = json.encodeToString(timestamp)
        
        // 验证序列化结果是否为长整型数值
        assertEquals("1621234567890", serialized)
    }
    
    @Test
    fun `test full message serialization with timestamp`() {
        // 创建测试数据
        val timestamp = TimestampDto(
            wallClockTime = 1621234567890,
            logicalTime = 42,
            nodeId = "device-123"
        )
        
        // 创建完整消息对象
        val message = com.example.todoschedule.data.sync.dto.SyncMessageDto(
            crdtKey = "sch_12345",
            entityType = "OrdinarySchedule",
            operationType = "UPDATE",
            deviceId = "device_12345678",
            timestamp = timestamp,
            payload = "{\"id\":\"sch_12345\",\"title\":\"复习考试\"}",
            userId = 87654321
        )
        
        // 序列化
        val serialized = json.encodeToString(message)
        
        // 验证hlcTimestamp字段是否为数值而不是对象
        assert(!serialized.contains("\"hlcTimestamp\":{"))
        assert(serialized.contains("\"hlcTimestamp\":1621234567890"))
    }
}
