// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.auth;

import com.example.todoschedule.domain.repository.RemoteUserRepository;
import com.example.todoschedule.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RegisterUserUseCase_Factory implements Factory<RegisterUserUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<RemoteUserRepository> remoteUserRepositoryProvider;

  private final Provider<HashPasswordUseCase> hashPasswordUseCaseProvider;

  public RegisterUserUseCase_Factory(Provider<UserRepository> userRepositoryProvider,
      Provider<RemoteUserRepository> remoteUserRepositoryProvider,
      Provider<HashPasswordUseCase> hashPasswordUseCaseProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.remoteUserRepositoryProvider = remoteUserRepositoryProvider;
    this.hashPasswordUseCaseProvider = hashPasswordUseCaseProvider;
  }

  @Override
  public RegisterUserUseCase get() {
    return newInstance(userRepositoryProvider.get(), remoteUserRepositoryProvider.get(), hashPasswordUseCaseProvider.get());
  }

  public static RegisterUserUseCase_Factory create(Provider<UserRepository> userRepositoryProvider,
      Provider<RemoteUserRepository> remoteUserRepositoryProvider,
      Provider<HashPasswordUseCase> hashPasswordUseCaseProvider) {
    return new RegisterUserUseCase_Factory(userRepositoryProvider, remoteUserRepositoryProvider, hashPasswordUseCaseProvider);
  }

  public static RegisterUserUseCase newInstance(UserRepository userRepository,
      RemoteUserRepository remoteUserRepository, HashPasswordUseCase hashPasswordUseCase) {
    return new RegisterUserUseCase(userRepository, remoteUserRepository, hashPasswordUseCase);
  }
}
