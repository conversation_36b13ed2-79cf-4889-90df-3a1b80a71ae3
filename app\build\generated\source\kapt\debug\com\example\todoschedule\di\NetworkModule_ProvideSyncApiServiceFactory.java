// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.remote.api.SyncApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideSyncApiServiceFactory implements Factory<SyncApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideSyncApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public SyncApi get() {
    return provideSyncApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideSyncApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideSyncApiServiceFactory(retrofitProvider);
  }

  public static SyncApi provideSyncApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideSyncApiService(retrofit));
  }
}
