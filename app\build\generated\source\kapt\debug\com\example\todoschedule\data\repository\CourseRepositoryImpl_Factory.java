// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.database.dao.CourseDao;
import com.example.todoschedule.data.sync.SyncManager;
import com.example.todoschedule.domain.repository.GlobalSettingRepository;
import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CourseRepositoryImpl_Factory implements Factory<CourseRepositoryImpl> {
  private final Provider<CourseDao> courseDaoProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<GlobalSettingRepository> globalSettingRepositoryProvider;

  private final Provider<SyncManager> syncManagerProvider;

  public CourseRepositoryImpl_Factory(Provider<CourseDao> courseDaoProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    this.courseDaoProvider = courseDaoProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.globalSettingRepositoryProvider = globalSettingRepositoryProvider;
    this.syncManagerProvider = syncManagerProvider;
  }

  @Override
  public CourseRepositoryImpl get() {
    return newInstance(courseDaoProvider.get(), sessionRepositoryProvider.get(), globalSettingRepositoryProvider.get(), syncManagerProvider.get());
  }

  public static CourseRepositoryImpl_Factory create(Provider<CourseDao> courseDaoProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<SyncManager> syncManagerProvider) {
    return new CourseRepositoryImpl_Factory(courseDaoProvider, sessionRepositoryProvider, globalSettingRepositoryProvider, syncManagerProvider);
  }

  public static CourseRepositoryImpl newInstance(CourseDao courseDao,
      SessionRepository sessionRepository, GlobalSettingRepository globalSettingRepository,
      SyncManager syncManager) {
    return new CourseRepositoryImpl(courseDao, sessionRepository, globalSettingRepository, syncManager);
  }
}
