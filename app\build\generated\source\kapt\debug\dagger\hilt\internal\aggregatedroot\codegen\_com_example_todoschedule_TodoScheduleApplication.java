package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.todoschedule.TodoScheduleApplication",
    rootPackage = "com.example.todoschedule",
    originatingRoot = "com.example.todoschedule.TodoScheduleApplication",
    originatingRootPackage = "com.example.todoschedule",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "TodoScheduleApplication",
    originatingRootSimpleNames = "TodoScheduleApplication"
)
public class _com_example_todoschedule_TodoScheduleApplication {
}
