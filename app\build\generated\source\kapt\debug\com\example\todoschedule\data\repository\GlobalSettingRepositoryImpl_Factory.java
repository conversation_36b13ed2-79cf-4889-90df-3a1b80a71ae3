// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.database.dao.GlobalSettingDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GlobalSettingRepositoryImpl_Factory implements Factory<GlobalSettingRepositoryImpl> {
  private final Provider<GlobalSettingDao> globalSettingDaoProvider;

  public GlobalSettingRepositoryImpl_Factory(Provider<GlobalSettingDao> globalSettingDaoProvider) {
    this.globalSettingDaoProvider = globalSettingDaoProvider;
  }

  @Override
  public GlobalSettingRepositoryImpl get() {
    return newInstance(globalSettingDaoProvider.get());
  }

  public static GlobalSettingRepositoryImpl_Factory create(
      Provider<GlobalSettingDao> globalSettingDaoProvider) {
    return new GlobalSettingRepositoryImpl_Factory(globalSettingDaoProvider);
  }

  public static GlobalSettingRepositoryImpl newInstance(GlobalSettingDao globalSettingDao) {
    return new GlobalSettingRepositoryImpl(globalSettingDao);
  }
}
