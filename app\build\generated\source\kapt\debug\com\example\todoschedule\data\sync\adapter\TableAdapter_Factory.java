// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync.adapter;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TableAdapter_Factory implements Factory<TableAdapter> {
  @Override
  public TableAdapter get() {
    return newInstance();
  }

  public static TableAdapter_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TableAdapter newInstance() {
    return new TableAdapter();
  }

  private static final class InstanceHolder {
    private static final TableAdapter_Factory INSTANCE = new TableAdapter_Factory();
  }
}
