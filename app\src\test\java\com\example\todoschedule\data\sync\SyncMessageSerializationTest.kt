package com.example.todoschedule.data.sync

import com.example.todoschedule.data.sync.dto.SyncMessageDto
import com.example.todoschedule.data.sync.dto.TimestampDto
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.Assert.assertTrue
import org.junit.Test

/**
 * SyncMessage序列化测试
 * 
 * 验证SyncMessage的序列化格式是否符合API要求
 */
class SyncMessageSerializationTest {
    
    private val json = Json { 
        encodeDefaults = true
        prettyPrint = true 
    }
    
    @Test
    fun `test sync message serialization matches API format`() {
        // 创建测试数据
        val timestamp = TimestampDto(
            wallClockTime = 1621234567890,
            logicalTime = 42,
            nodeId = "device-123"
        )
        
        val message = SyncMessageDto(
            crdtKey = "sch_12345",
            entityType = "OrdinarySchedule",
            operationType = "UPDATE",
            deviceId = "device_12345678",
            timestamp = timestamp,
            payload = """{"id":"sch_12345","title":"复习考试","content":"期末考试复习"}""",
            userId = 87654321
        )
        
        // 序列化消息
        val serialized = json.encodeToString(message)
        println("序列化结果: $serialized")
        
        // 验证关键字段
        assertTrue("消息应包含crdtKey字段", serialized.contains("\"crdt_key\""))
        assertTrue("消息应包含deviceId字段", serialized.contains("\"originDeviceId\""))
        assertTrue("消息应包含hlcTimestamp字段作为数值", serialized.contains("\"hlcTimestamp\":"))
        assertTrue("hlcTimestamp应该是数值而不是对象", !serialized.contains("\"hlcTimestamp\":{"))
        assertTrue("消息应包含entityType字段", serialized.contains("\"entityType\""))
        assertTrue("消息应包含operationType字段", serialized.contains("\"operationType\""))
        assertTrue("消息应包含payload字段", serialized.contains("\"messageData\""))
        
        // 检查生成的JSON是否符合API文档要求的格式
        // 期望的格式类似：
        // {
        //   "crdt_key": "sch_12345",
        //   "hlcTimestamp": 1621234567890,
        //   "originDeviceId": "device_12345678",
        //   ...
        // }
    }
    
    @Test
    fun `test message list serialization for API upload`() {
        // 创建测试数据 - 两条消息
        val timestamp1 = TimestampDto(wallClockTime = 1621234567890, logicalTime = 1, nodeId = "dev1")
        val timestamp2 = TimestampDto(wallClockTime = 1621234569999, logicalTime = 2, nodeId = "dev1")
        
        val message1 = SyncMessageDto(
            crdtKey = "sch_12345",
            entityType = "OrdinarySchedule",
            operationType = "UPDATE",
            deviceId = "device_12345678",
            timestamp = timestamp1,
            payload = """{"id":"sch_12345","title":"复习考试"}""",
            userId = 87654321
        )
        
        val message2 = SyncMessageDto(
            crdtKey = "sch_67890",
            entityType = "OrdinarySchedule",
            operationType = "DELETE",
            deviceId = "device_12345678",
            timestamp = timestamp2,
            payload = """{"id":"sch_67890"}""",
            userId = 87654321
        )
        
        val messages = listOf(message1, message2)
        
        // 模拟API上传时的序列化 - 转换为JSON字符串数组
        val serializedMessages = messages.map { json.encodeToString(it) }
        
        // 输出结果进行检查
        println("消息1序列化结果: ${serializedMessages[0]}")
        println("消息2序列化结果: ${serializedMessages[1]}")
        
        // 验证每条消息是否正确序列化
        serializedMessages.forEach { serialized ->
            assertTrue("每条消息都应该是有效的JSON", serialized.startsWith("{") && serialized.endsWith("}"))
            assertTrue("hlcTimestamp应该是数值而不是对象", !serialized.contains("\"hlcTimestamp\":{"))
        }
    }
}
