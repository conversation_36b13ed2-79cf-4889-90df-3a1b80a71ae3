package com.example.todoschedule.data.repository

import android.util.Log
import com.example.todoschedule.data.database.AppDatabase
import com.example.todoschedule.data.database.dao.CourseDao
import com.example.todoschedule.data.database.dao.CourseNodeDao
import com.example.todoschedule.data.database.dao.OrdinaryScheduleDao
import com.example.todoschedule.data.database.dao.SyncMessageDao
import com.example.todoschedule.data.database.dao.TableDao
import com.example.todoschedule.data.database.entity.SyncMessageEntity
import com.example.todoschedule.data.database.entity.toEntity
import com.example.todoschedule.data.database.entity.CourseEntity
import com.example.todoschedule.data.database.entity.CourseNodeEntity
import com.example.todoschedule.data.database.entity.OrdinaryScheduleEntity
import com.example.todoschedule.data.database.entity.TableEntity
import com.example.todoschedule.data.remote.api.SyncApi
import com.example.todoschedule.data.sync.DeviceIdManager
import com.example.todoschedule.data.sync.SyncConstants
import com.example.todoschedule.data.sync.SyncManager
import com.example.todoschedule.data.sync.dto.DeviceRegistrationDto
import com.example.todoschedule.data.sync.dto.SyncMessagesDto
import com.example.todoschedule.data.sync.dto.SyncMessageDto
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import retrofit2.HttpException
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.Dispatchers
import javax.inject.Provider
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * 同步仓库实现类
 */
@Singleton
class SyncRepositoryImpl @Inject constructor(
    private val syncMessageDao: SyncMessageDao,
    private val syncApi: SyncApi,
    private val deviceIdManager: DeviceIdManager,
    private val database: AppDatabase,
    private val sessionRepository: com.example.todoschedule.domain.repository.SessionRepository,
    private val syncManagerProvider: Provider<SyncManager>
) : SyncRepository {

    private val TAG = "SyncRepository"
    private val json = Json { encodeDefaults = true }
    
    // 获取SyncManager实例时使用provider.get()，延迟加载
    private val syncManager: SyncManager
        get() = syncManagerProvider.get()
    
    /**
     * 获取课程DAO
     */
    override fun getCourseDao(): CourseDao {
        return database.courseDao()
    }
    
    /**
     * 获取课程节点DAO
     */
    override fun getCourseNodeDao(): CourseNodeDao {
        return database.courseNodeDao()
    }
    
    /**
     * 获取课表DAO
     */
    override fun getTableDao(): TableDao {
        return database.tableDao()
    }
    
    /**
     * 获取普通日程DAO
     */
    override fun getOrdinaryScheduleDao(): OrdinaryScheduleDao {
        return database.ordinaryScheduleDao()
    }

    override suspend fun saveSyncMessage(message: SyncMessageEntity) {
        syncMessageDao.insert(message)
    }

    override suspend fun saveSyncMessages(messages: List<SyncMessageEntity>) {
        syncMessageDao.insertAll(messages)
    }

    override suspend fun getPendingMessages(): List<SyncMessageEntity> {
        return syncMessageDao.getPendingMessages()
    }

    override suspend fun getPendingMessagesByUserId(userId: Int): List<SyncMessageEntity> {
        return syncMessageDao.getPendingMessagesByUserId(userId)
    }

    override suspend fun getPendingMessagesByType(entityType: String): List<SyncMessageEntity> {
        return syncMessageDao.getPendingMessagesByType(entityType)
    }

    /**
     * 判断是否是可重试的HTTP状态码
     */
    private fun isRetryableHttpCode(code: Int): Boolean {
        // 5xx服务器错误通常可以重试
        // 429 Too Many Requests也可以重试
        // 部分408 Request Timeout可以重试
        return code >= 500 || code == 429 || code == 408
    }
    
    /**
     * 判断是否是可重试的错误消息
     */
    private fun isRetryableError(errorMsg: String): Boolean {
        // 包含这些关键词的错误通常可以重试
        val retryableKeywords = listOf(
            "timeout", "超时",
            "connection", "连接",
            "temporary", "暂时",
            "overloaded", "过载",
            "try again", "重试"
        )
        
        return retryableKeywords.any { errorMsg.contains(it, ignoreCase = true) }
    }
    
    override suspend fun uploadMessages(messages: List<SyncMessageEntity>, entityType: String): List<String> {
        if (messages.isEmpty()) {
            Log.d(TAG, "没有待上传的消息，entityType: $entityType")
            return emptyList()
        }
        
        try {
            val deviceId = deviceIdManager.getOrCreateDeviceId()
            val messageDtos = messages.map { it.toDto() }
            
            // 将DTO对象转换为JSON字符串
            val serializedMessages = messageDtos.map { json.encodeToString(it) }
            
            Log.d(TAG, "准备上传${messages.size}条同步消息，实体类型: $entityType，设备ID: $deviceId，第一条消息ID: ${messages.firstOrNull()?.syncId}")
            Log.d(TAG, "第一条消息序列化后: ${serializedMessages.firstOrNull()?.take(100)}...")
            
            val response = try {
                syncApi.uploadMessages(
                    deviceId = deviceId,
                    entityType = entityType,
                    messages = serializedMessages
                )
            } catch (e: Exception) {
                Log.e(TAG, "调用syncApi.uploadMessages发生异常: ${e::class.java.simpleName}: ${e.message}", e)
                throw e
            }
            
            if (response.isSuccessful && response.body() != null) {
                val result = response.body()!!
                Log.d(TAG, "上传响应: success=${result.success}, messagesReceived=${result.messagesReceived}, errors=${result.errors}")
                
                if (result.success) {
                    Log.d(TAG, "消息上传成功: ${result.messagesReceived}条消息已接收，实体类型: $entityType")
                    // 更新消息状态为已同步
                    val messageIds = messages.map { it.syncId }
                    syncMessageDao.markAsProcessed(messageIds)
                    return messageIds.map { it.toString() } // 返回字符串格式的ID以保持兼容性
                } else {
                    Log.e(TAG, "消息上传失败: ${result.errors?.joinToString()}, 实体类型: $entityType")
                    // 更新消息状态为同步失败
                    val failedMessages = messages.map { 
                        it.withStatus(SyncConstants.SyncStatus.FAILED, 
                                      result.errors?.joinToString() ?: "Unknown error") 
                    }
                    syncMessageDao.updateAll(failedMessages)
                }
            } else {
                // 更新消息状态为同步失败
                val errorCode = response.code()
                val errorBody = response.errorBody()?.string() ?: "Unknown error"
                Log.e(TAG, "上传失败, HTTP状态码: $errorCode, 错误: $errorBody, 实体类型: $entityType")
                
                val failedMessages = messages.map { 
                    it.withStatus(SyncConstants.SyncStatus.FAILED, "HTTP $errorCode: $errorBody") 
                }
                syncMessageDao.updateAll(failedMessages)
            }
        } catch (e: IOException) {
            // 网络错误
            Log.e(TAG, "网络错误，可能是网络连接问题: ${e.message}, 实体类型: $entityType", e)
            val failedMessages = messages.map { 
                it.withStatus(SyncConstants.SyncStatus.FAILED, "网络错误: ${e.message}") 
            }
            syncMessageDao.updateAll(failedMessages)
        } catch (e: HttpException) {
            // HTTP错误
            Log.e(TAG, "HTTP错误: ${e.code()}, 信息: ${e.message()}, 实体类型: $entityType", e)
            val failedMessages = messages.map { 
                it.withStatus(SyncConstants.SyncStatus.FAILED, "HTTP ${e.code()}: ${e.message()}") 
            }
            syncMessageDao.updateAll(failedMessages)
        } catch (e: Exception) {
            // 其他错误
            Log.e(TAG, "上传过程中发生未知错误: ${e::class.java.simpleName}: ${e.message}, 实体类型: $entityType", e)
            val failedMessages = messages.map { 
                it.withStatus(SyncConstants.SyncStatus.FAILED, "${e::class.java.simpleName}: ${e.message}") 
            }
            syncMessageDao.updateAll(failedMessages)
        }
        
        return emptyList()
    }

    override suspend fun downloadAllMessages(): List<SyncMessageDto> {
        return try {
            val deviceId = deviceIdManager.getOrCreateDeviceId()
            val response = syncApi.getAllMessages(deviceId)
            
            if (response.isSuccessful) {
                response.body() ?: emptyList()
            } else {
                Log.e(TAG, "Failed to download all messages: ${response.errorBody()?.string()}")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading all messages", e)
            emptyList()
        }
    }

    override suspend fun downloadMessagesByEntityType(entityType: String): List<SyncMessageDto> {
        return try {
            val deviceId = deviceIdManager.getOrCreateDeviceId()
            val response = syncApi.getMessagesByEntityType(deviceId, entityType)
            
            if (response.isSuccessful) {
                response.body() ?: emptyList()
            } else {
                Log.e(TAG, "Failed to download messages for $entityType: ${response.errorBody()?.string()}")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading messages for $entityType", e)
            emptyList()
        }
    }

    override suspend fun downloadAllMessagesExcludeOrigin(): List<SyncMessageDto> {
        return try {
            val deviceId = deviceIdManager.getOrCreateDeviceId()
            val response = syncApi.getAllMessagesExcludeOrigin(deviceId)
            
            if (response.isSuccessful) {
                response.body() ?: emptyList()
            } else {
                Log.e(TAG, "Failed to download non-origin messages: ${response.errorBody()?.string()}")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading non-origin messages", e)
            emptyList()
        }
    }

    override suspend fun downloadMessagesByEntityTypeExcludeOrigin(entityType: String): List<SyncMessageDto> {
        return try {
            val deviceId = deviceIdManager.getOrCreateDeviceId()
            val response = syncApi.getMessagesByEntityTypeExcludeOrigin(deviceId, entityType)
            
            if (response.isSuccessful) {
                response.body() ?: emptyList()
            } else {
                Log.e(TAG, "Failed to download non-origin messages for $entityType: ${response.errorBody()?.string()}")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading non-origin messages for $entityType", e)
            emptyList()
        }
    }

    override suspend fun markMessagesAsProcessed(ids: List<Int>) {
        syncMessageDao.markAsProcessed(ids)
    }

    /**
     * 将同步消息标记为已同步
     * @param ids 消息ID列表
     */
    override suspend fun markMessagesAsSynced(ids: List<Int>) {
        syncMessageDao.markAsProcessed(ids)
    }

    /**
     * 注册设备与用户的关联
     * @param userId 用户ID
     * @return 注册是否成功
     */
    override suspend fun registerDevice(userId: Int): Boolean {
        return try {
            val deviceId = deviceIdManager.getOrCreateDeviceId()
            
            Log.d(TAG, "正在注册设备: deviceId=$deviceId, userId=$userId")
            
            // 确保用户ID有效
            if (userId <= 0) {
                Log.e(TAG, "无效的用户ID: $userId")
                return false
            }
            
            // 确保设备ID有效
            if (deviceId.isBlank()) {
                Log.e(TAG, "无效的设备ID: $deviceId")
                return false
            }
            
            Log.d(TAG, "发送设备注册请求: deviceId=$deviceId, userId=$userId")
            
            val response = syncApi.registerDevice(
                deviceId = deviceId,
                deviceRegistration = DeviceRegistrationDto(deviceId, userId)
            )
            
            val success = response.isSuccessful && response.body() != null
            
            if (success) {
                // 响应中的lastSyncHlcTimestamp可能为null，首次注册或服务器未维护同步时间戳时
                val lastTimestamp = response.body()?.lastSyncHlcTimestamp ?: 0L
                Log.d(TAG, "设备注册成功: 用户ID=$userId, 设备ID=$deviceId, 最后同步时间戳=$lastTimestamp")
                true
            } else {
                val errorBody = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "设备注册失败: ${response.code()}, $errorBody")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "设备注册过程中发生异常: ${e.message}", e)
            false
        }
    }

    override fun getAllSyncMessages(): Flow<List<SyncMessageEntity>> {
        return syncMessageDao.getAllMessages()
    }

    override suspend fun syncEntityType(entityType: SyncConstants.EntityType): Boolean {
        try {
            Log.d(TAG, "开始同步实体类型: ${entityType.value}")
            
            // 下载该类型的最新消息
            val remoteMessages = downloadMessagesByEntityTypeExcludeOrigin(entityType.value)
            Log.d(TAG, "从服务器下载的消息数量: ${remoteMessages.size}, 实体类型: ${entityType.value}")
            
            if (remoteMessages.isNotEmpty()) {
                // 保存到本地数据库
                val entities = remoteMessages.map { it.toEntity(SyncConstants.SyncStatus.SYNCED) }
                saveSyncMessages(entities)
                Log.d(TAG, "已保存${entities.size}条远程消息到本地，实体类型: ${entityType.value}")
            }
            
            // 上传本地待同步消息
            val pendingMessages = getPendingMessagesByType(entityType.value)
            Log.d(TAG, "待上传的本地消息数量: ${pendingMessages.size}, 实体类型: ${entityType.value}")
            
            if (pendingMessages.isNotEmpty()) {
                val uploadedIds = uploadMessages(pendingMessages, entityType.value)
                Log.d(TAG, "已上传${uploadedIds.size}/${pendingMessages.size}条本地消息，实体类型: ${entityType.value}")
            }
            
            return true
        } catch (e: Exception) {
            Log.e(TAG, "同步实体类型失败: ${entityType.value}", e)
            return false
        }
    }

    override suspend fun syncAll(): Boolean {
        try {
            // 获取待同步消息总数
            val pendingCount = syncMessageDao.getPendingMessages().size
            Log.d(TAG, "开始同步所有实体类型，待同步消息总数: $pendingCount")
            
            // 同步所有类型的实体
            var allSuccess = true
            var syncedCount = 0
            
            for (entityType in SyncConstants.EntityType.values()) {
                val startTime = System.currentTimeMillis()
                val success = syncEntityType(entityType)
                val duration = System.currentTimeMillis() - startTime
                
                if (success) {
                    syncedCount++
                    Log.d(TAG, "实体类型 ${entityType.value} 同步成功，耗时: ${duration}ms")
                } else {
                    allSuccess = false
                    Log.e(TAG, "实体类型 ${entityType.value} 同步失败，耗时: ${duration}ms")
                }
            }
            
            Log.d(TAG, "同步完成，成功: $syncedCount/${SyncConstants.EntityType.values().size}，同步状态: ${if (allSuccess) "成功" else "部分失败"}")
            return allSuccess
        } catch (e: Exception) {
            Log.e(TAG, "同步所有实体类型时发生错误", e)
            return false
        }
    }

    override suspend fun cleanupOldMessages(beforeTime: Long): Int {
        return syncMessageDao.deleteProcessedMessages(beforeTime)
    }

    override suspend fun getTokenFromSession(): String? {
        return try {
            sessionRepository.getUserToken()
        } catch (e: Exception) {
            Log.e(TAG, "获取token失败: ${e.message}")
            null
        }
    }

    override suspend fun getUserIdFromSession(): Long? {
        return try {
            sessionRepository.currentUserIdFlow.first()
        } catch (e: Exception) {
            Log.e(TAG, "获取用户ID失败: ${e.message}")
            null
        }
    }

    /**
     * 执行完整的数据同步流程
     */
    override suspend fun syncData() {
        try {
            Log.d(TAG, "开始执行完整同步流程")
            
            // 1. 获取用户ID
            val userIdFromFlow = getUserIdFromSession()
            Log.d(TAG, "从Session获取到的原始用户ID: $userIdFromFlow")
            
            val userId = userIdFromFlow?.toInt() ?: run {
                Log.e(TAG, "获取用户ID失败，无法执行同步")
                return
            }
            
            Log.d(TAG, "转换后的用户ID: $userId, 是否有效: ${userId > 0}")
            
            // 确保用户ID有效
            if (userId <= 0) {
                Log.e(TAG, "用户ID无效，跳过设备注册")
                return
            }
            
            // 2. 注册设备与用户的关联
            Log.d(TAG, "即将注册设备，用户ID: $userId")
            val deviceRegistered = registerDevice(userId)
            if (!deviceRegistered) {
                Log.e(TAG, "设备注册失败，无法继续同步过程")
                return
            }
            Log.d(TAG, "设备注册成功，用户ID: $userId")
            
            // 3. 获取所有待上传的本地消息
            val pendingMessages = getPendingMessagesByUserId(userId)
            Log.d(TAG, "待上传消息总数: ${pendingMessages.size}")
            
            var uploadedCount = 0
            var entityTypesWithChanges = mutableSetOf<String>()
            
            // 4. 按实体类型分组上传本地消息
            for (entityType in SyncConstants.EntityType.values()) {
                val typeMessages = pendingMessages.filter { it.entityType == entityType.value }
                if (typeMessages.isNotEmpty()) {
                    entityTypesWithChanges.add(entityType.value)
                    Log.d(TAG, "上传 ${entityType.value} 类型的消息: ${typeMessages.size}条")
                    val uploadedIds = uploadMessages(typeMessages, entityType.value)
                    uploadedCount += uploadedIds.size
                    Log.d(TAG, "成功上传 ${uploadedIds.size}/${typeMessages.size} 条 ${entityType.value} 类型的消息")
                }
            }
            
            Log.d(TAG, "上传阶段完成，成功上传 $uploadedCount/${pendingMessages.size} 条消息")
            
            // 5. 下载每种实体类型的新消息
            var downloadedCount = 0
            
            for (entityType in SyncConstants.EntityType.values()) {
                Log.d(TAG, "正在下载 ${entityType.value} 类型的消息...")
                val messages = downloadMessagesByEntityTypeExcludeOrigin(entityType.value)
                downloadedCount += messages.size
                
                Log.d(TAG, "接收到 ${messages.size} 条 ${entityType.value} 类型的消息")
                if (messages.isNotEmpty()) {
                    // 保存到本地数据库并处理
                    for (message in messages) {
                        syncManager.processReceivedMessage(message)
                    }
                }
            }
            
            Log.d(TAG, "下载阶段完成，共下载 $downloadedCount 条消息")
            
            // 6. 清理旧消息（保留最近一周的消息）
            val oneWeekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
            val cleanedCount = cleanupOldMessages(oneWeekAgo)
            Log.d(TAG, "清理了 $cleanedCount 条旧消息")
            
            Log.d(TAG, "数据同步完成 - 上传: $uploadedCount, 下载: $downloadedCount, 清理: $cleanedCount")
        } catch (e: Exception) {
            Log.e(TAG, "数据同步过程中发生错误: ${e.message}", e)
            throw e
        }
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun <T> getEntityByCrdtKey(crdtKey: String): T? {
        return withContext(Dispatchers.IO) {
            try {
                // 尝试在所有DAO中查找实体
                val course = database.courseDao().getCourseByCrdtKey(crdtKey)
                if (course != null) return@withContext course as T
                
                val courseNode = database.courseNodeDao().getCourseNodeByCrdtKey(crdtKey)
                if (courseNode != null) return@withContext courseNode as T
                
                val ordinarySchedule = database.ordinaryScheduleDao().getOrdinaryScheduleByCrdtKey(crdtKey)
                if (ordinarySchedule != null) return@withContext ordinarySchedule as T
                
                val table = database.tableDao().getTableByCrdtKey(crdtKey)
                if (table != null) return@withContext table as T
                
                // 没有找到匹配的实体
                null
            } catch (e: Exception) {
                Log.e(TAG, "根据CRDT键获取实体失败: ${e.message}", e)
                null
            }
        }
    }
    
    override suspend fun <T> saveEntity(entity: T): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                when (entity) {
                    is CourseEntity -> {
                        database.courseDao().insertCourse(entity)
                        true
                    }
                    is CourseNodeEntity -> {
                        database.courseNodeDao().insertCourseNode(entity)
                        true
                    }
                    is OrdinaryScheduleEntity -> {
                        database.ordinaryScheduleDao().insertOrdinarySchedule(entity)
                        true
                    }
                    is TableEntity -> {
                        database.tableDao().insertTable(entity)
                        true
                    }
                    else -> {
                        Log.e(TAG, "不支持的实体类型: ${entity?.javaClass?.simpleName}")
                        false
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存实体失败: ${e.message}", e)
                false
            }
        }
    }
} 