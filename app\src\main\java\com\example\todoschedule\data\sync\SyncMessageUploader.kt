package com.example.todoschedule.data.sync

import android.util.Log
import com.example.todoschedule.data.database.dao.SyncMessageDao
import com.example.todoschedule.data.database.entity.SyncMessageEntity
import com.example.todoschedule.data.remote.api.SyncApi
import com.example.todoschedule.util.NetworkUtils
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 同步消息上传器
 * 
 * 专门负责处理消息上传逻辑，包括重试机制和错误处理，
 * 确保消息格式符合API要求。
 */
@Singleton
class SyncMessageUploader @Inject constructor(
    private val syncApi: SyncApi,
    private val syncMessageDao: SyncMessageDao,
    private val deviceIdManager: DeviceIdManager
) {
    private val TAG = "SyncMessageUploader"
    private val json = Json { encodeDefaults = true }
    
    /**
     * 上传同步消息到服务器
     * 
     * @param messages 待上传的消息列表
     * @param entityType 实体类型
     * @return 上传成功的消息ID列表
     */
    suspend fun uploadMessages(
        messages: List<SyncMessageEntity>, 
        entityType: String
    ): List<String> {
        if (messages.isEmpty()) {
            Log.d(TAG, "没有待上传的消息，entityType: $entityType")
            return emptyList()
        }
        
        try {
            // 使用NetworkUtils添加重试逻辑
            return NetworkUtils.withRetry(
                maxRetries = 3,
                tag = TAG
            ) {
                val deviceId = deviceIdManager.getOrCreateDeviceId()
                val messageDtos = messages.map { it.toDto() }
                
                // 将DTO对象转换为符合API要求的JSON字符串
                val serializedMessages = messageDtos.map { json.encodeToString(it) }
                
                Log.d(TAG, "准备上传${messages.size}条同步消息，实体类型: $entityType，设备ID: $deviceId")
                if (serializedMessages.isNotEmpty()) {
                    Log.d(TAG, "第一条消息序列化后: ${serializedMessages.first().take(100)}...")
                }
                
                val response = syncApi.uploadMessages(
                    deviceId = deviceId,
                    entityType = entityType,
                    messages = serializedMessages // 发送JSON字符串数组
                )
                
                if (response.isSuccessful && response.body() != null) {
                    val result = response.body()!!
                    Log.d(TAG, "上传响应: success=${result.success}, messagesReceived=${result.messagesReceived}, errors=${result.errors}")
                    
                    if (result.success) {
                        Log.d(TAG, "消息上传成功: ${result.messagesReceived}条消息已接收，实体类型: $entityType")
                        // 更新消息状态为已同步
                        val messageIds = messages.map { it.syncId }
                        syncMessageDao.markAsProcessed(messageIds)
                        return@withRetry messageIds.map { it.toString() } // 返回字符串格式的ID以保持兼容性
                    } else {
                        // 服务器接收了请求但报告失败
                        val errorMsg = result.errors?.joinToString() ?: "Unknown server error"
                        Log.e(TAG, "消息上传失败: $errorMsg, 实体类型: $entityType")
                        
                        // 更新消息状态为同步失败
                        val failedMessages = messages.map { 
                            it.withStatus(SyncConstants.SyncStatus.FAILED, errorMsg) 
                        }
                        syncMessageDao.updateAll(failedMessages)
                        
                        // 如果是可重试的错误，让withRetry机制处理
                        if (NetworkUtils.isRetryableError(errorMsg)) {
                            throw RuntimeException("可重试的服务器错误: $errorMsg")
                        }
                        
                        emptyList<String>()
                    }
                } else {
                    // HTTP错误
                    val errorCode = response.code()
                    val errorBody = response.errorBody()?.string() ?: "Unknown error"
                    Log.e(TAG, "上传失败, HTTP状态码: $errorCode, 错误: $errorBody, 实体类型: $entityType")
                    
                    // 更新消息状态为同步失败
                    val failedMessages = messages.map { 
                        it.withStatus(SyncConstants.SyncStatus.FAILED, "HTTP $errorCode: $errorBody") 
                    }
                    syncMessageDao.updateAll(failedMessages)
                    
                    // 如果是可重试的HTTP状态码，让withRetry机制处理
                    if (NetworkUtils.isRetryableHttpCode(errorCode)) {
                        throw RuntimeException("可重试的HTTP错误: $errorCode")
                    }
                    
                    emptyList<String>()
                }
            }
        } catch (e: Exception) {
            // 如果所有重试都失败了，更新消息状态为同步失败
            Log.e(TAG, "所有重试都失败了: ${e.message}, 实体类型: $entityType", e)
            val failedMessages = messages.map { 
                it.withStatus(SyncConstants.SyncStatus.FAILED, "所有重试失败: ${e.message}") 
            }
            syncMessageDao.updateAll(failedMessages)
            return emptyList()
        }
    }
}
