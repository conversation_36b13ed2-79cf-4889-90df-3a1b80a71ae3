// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.course.edit;

import com.example.todoschedule.domain.repository.CourseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EditCourseViewModel_Factory implements Factory<EditCourseViewModel> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  public EditCourseViewModel_Factory(Provider<CourseRepository> courseRepositoryProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
  }

  @Override
  public EditCourseViewModel get() {
    return newInstance(courseRepositoryProvider.get());
  }

  public static EditCourseViewModel_Factory create(
      Provider<CourseRepository> courseRepositoryProvider) {
    return new EditCourseViewModel_Factory(courseRepositoryProvider);
  }

  public static EditCourseViewModel newInstance(CourseRepository courseRepository) {
    return new EditCourseViewModel(courseRepository);
  }
}
