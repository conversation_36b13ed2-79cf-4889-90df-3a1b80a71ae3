// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.ordinaryschedule;

import androidx.lifecycle.SavedStateHandle;
import com.example.todoschedule.domain.use_case.ordinary_schedule.DeleteOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OrdinaryScheduleDetailViewModel_Factory implements Factory<OrdinaryScheduleDetailViewModel> {
  private final Provider<GetOrdinaryScheduleByIdUseCase> getOrdinaryScheduleByIdUseCaseProvider;

  private final Provider<DeleteOrdinaryScheduleUseCase> deleteOrdinaryScheduleUseCaseProvider;

  private final Provider<SavedStateHandle> savedStateHandleProvider;

  public OrdinaryScheduleDetailViewModel_Factory(
      Provider<GetOrdinaryScheduleByIdUseCase> getOrdinaryScheduleByIdUseCaseProvider,
      Provider<DeleteOrdinaryScheduleUseCase> deleteOrdinaryScheduleUseCaseProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    this.getOrdinaryScheduleByIdUseCaseProvider = getOrdinaryScheduleByIdUseCaseProvider;
    this.deleteOrdinaryScheduleUseCaseProvider = deleteOrdinaryScheduleUseCaseProvider;
    this.savedStateHandleProvider = savedStateHandleProvider;
  }

  @Override
  public OrdinaryScheduleDetailViewModel get() {
    return newInstance(getOrdinaryScheduleByIdUseCaseProvider.get(), deleteOrdinaryScheduleUseCaseProvider.get(), savedStateHandleProvider.get());
  }

  public static OrdinaryScheduleDetailViewModel_Factory create(
      Provider<GetOrdinaryScheduleByIdUseCase> getOrdinaryScheduleByIdUseCaseProvider,
      Provider<DeleteOrdinaryScheduleUseCase> deleteOrdinaryScheduleUseCaseProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    return new OrdinaryScheduleDetailViewModel_Factory(getOrdinaryScheduleByIdUseCaseProvider, deleteOrdinaryScheduleUseCaseProvider, savedStateHandleProvider);
  }

  public static OrdinaryScheduleDetailViewModel newInstance(
      GetOrdinaryScheduleByIdUseCase getOrdinaryScheduleByIdUseCase,
      DeleteOrdinaryScheduleUseCase deleteOrdinaryScheduleUseCase,
      SavedStateHandle savedStateHandle) {
    return new OrdinaryScheduleDetailViewModel(getOrdinaryScheduleByIdUseCase, deleteOrdinaryScheduleUseCase, savedStateHandle);
  }
}
