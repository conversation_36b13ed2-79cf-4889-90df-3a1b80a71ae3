  	ArrayList    Boolean    	ByteArray    Common    Int    List    MutableList    Regex    String    arrayOf    invoke    	ArrayList Common  Boolean Common  	ByteArray Common  Course Common  CourseBaseBean Common  Int Common  List Common  MutableList Common  Regex Common  	Semaphore Common  String Common  TimeHM Common  WeekBean Common  arrayOf Common  
getARRAYOf Common  
getArrayOf Common  invoke Common  Int 
Common.TimeHM  String 
Common.TimeHM  TimeHM 
Common.TimeHM  Manifest android  
permission android.Manifest  
READ_CALENDAR android.Manifest.permission  WRITE_CALENDAR android.Manifest.permission  SuppressLint android.annotation  Activity android.app  Application android.app  Service android.app  Bundle android.app.Activity  Inject android.app.Activity  MainActivityViewModel android.app.Activity  SyncRepository android.app.Activity  getValue android.app.Activity  provideDelegate android.app.Activity  
viewModels android.app.Activity  CoroutineScope android.app.Application  DevUtils android.app.Application  Dispatchers android.app.Application  Inject android.app.Application  
SupervisorJob android.app.Application  SyncManager android.app.Application  Binder android.app.Service  Boolean android.app.Service  IBinder android.app.Service  Inject android.app.Service  Int android.app.Service  Intent android.app.Service  Job android.app.Service  SessionRepository android.app.Service  SyncManager android.app.Service  SyncRepository android.app.Service  SyncService android.app.Service  Timer android.app.Service  ContentResolver android.content  
ContentValues android.content  Context android.content  Intent android.content  Binder android.content.Context  Boolean android.content.Context  Bundle android.content.Context  CoroutineScope android.content.Context  DevUtils android.content.Context  Dispatchers android.content.Context  IBinder android.content.Context  Inject android.content.Context  Int android.content.Context  Intent android.content.Context  Job android.content.Context  MainActivityViewModel android.content.Context  SessionRepository android.content.Context  
SupervisorJob android.content.Context  SyncManager android.content.Context  SyncRepository android.content.Context  SyncService android.content.Context  Timer android.content.Context  contentResolver android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getPREFERENCESDataStoreFile android.content.Context  getPreferencesDataStoreFile android.content.Context  getValue android.content.Context  preferencesDataStoreFile android.content.Context  provideDelegate android.content.Context  setContentResolver android.content.Context  
viewModels android.content.Context  Binder android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  DevUtils android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  IBinder android.content.ContextWrapper  Inject android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Job android.content.ContextWrapper  MainActivityViewModel android.content.ContextWrapper  SessionRepository android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  SyncManager android.content.ContextWrapper  SyncRepository android.content.ContextWrapper  SyncService android.content.ContextWrapper  Timer android.content.ContextWrapper  getValue android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
viewModels android.content.ContextWrapper  PackageManager android.content.pm  Bitmap android.graphics  Color android.graphics  ConnectivityManager android.net  NetworkCapabilities android.net  Uri android.net  SslError android.net.http  Binder 
android.os  Build 
android.os  Bundle 
android.os  IBinder 
android.os  Message 
android.os  
Parcelable 
android.os  SyncService android.os.Binder  CalendarContract android.provider  Settings android.provider  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  	ViewGroup android.view  Bundle  android.view.ContextThemeWrapper  Inject  android.view.ContextThemeWrapper  MainActivityViewModel  android.view.ContextThemeWrapper  SyncRepository  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  
CookieManager android.webkit  SslErrorHandler android.webkit  WebChromeClient android.webkit  WebResourceError android.webkit  WebResourceRequest android.webkit  WebSettings android.webkit  
WebStorage android.webkit  WebView android.webkit  
WebViewClient android.webkit  Toast android.widget  ComponentActivity androidx.activity  
viewModels androidx.activity  Bundle #androidx.activity.ComponentActivity  Inject #androidx.activity.ComponentActivity  MainActivityViewModel #androidx.activity.ComponentActivity  SyncRepository #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  getGETValue -androidx.activity.ComponentActivity.Companion  getGetValue -androidx.activity.ComponentActivity.Companion  getPROVIDEDelegate -androidx.activity.ComponentActivity.Companion  getProvideDelegate -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  
viewModels -androidx.activity.ComponentActivity.Companion  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  AnimatedVisibility androidx.compose.animation  expandVertically androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  shrinkVertically androidx.compose.animation  animateFloatAsState androidx.compose.animation.core  tween androidx.compose.animation.core  BorderStroke androidx.compose.foundation  Canvas androidx.compose.foundation  ExperimentalFoundationApi androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  MutableInteractionSource 'androidx.compose.foundation.interaction  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxWithConstraints "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
IntrinsicSize "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  calculateEndPadding "androidx.compose.foundation.layout  calculateStartPadding "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  
imePadding "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  statusBarsPadding "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	GridCells %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  HorizontalPager !androidx.compose.foundation.pager  rememberPagerState !androidx.compose.foundation.pager  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  BasicTextField  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  AlertDialog androidx.compose.material  Button androidx.compose.material  Card androidx.compose.material  CircularProgressIndicator androidx.compose.material  ContentAlpha androidx.compose.material  Divider androidx.compose.material  Icon androidx.compose.material  
IconButton androidx.compose.material  LocalContentAlpha androidx.compose.material  
MaterialTheme androidx.compose.material  Scaffold androidx.compose.material  Text androidx.compose.material  
TextButton androidx.compose.material  	TopAppBar androidx.compose.material  Icons androidx.compose.material.icons  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  
Assignment 3androidx.compose.material.icons.automirrored.filled  Help 3androidx.compose.material.icons.automirrored.filled  
AccessTime &androidx.compose.material.icons.filled  Add &androidx.compose.material.icons.filled  Alarm &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  
ArrowDropDown &androidx.compose.material.icons.filled  ArrowForwardIos &androidx.compose.material.icons.filled  
CalendarMonth &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  
DeleteForever &androidx.compose.material.icons.filled  Download &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  
ExpandLess &androidx.compose.material.icons.filled  
ExpandMore &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  
LocationOn &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  NotificationsNone &androidx.compose.material.icons.filled  Palette &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Phone &androidx.compose.material.icons.filled  PriorityHigh &androidx.compose.material.icons.filled  Public &androidx.compose.material.icons.filled  RadioButtonUnchecked &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Repeat &androidx.compose.material.icons.filled  Schedule &androidx.compose.material.icons.filled  School &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Send &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  Sync &androidx.compose.material.icons.filled  SyncProblem &androidx.compose.material.icons.filled  Today &androidx.compose.material.icons.filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  
WatchLater &androidx.compose.material.icons.filled  RadioButtonUnchecked (androidx.compose.material.icons.outlined  AlertDialog androidx.compose.material3  Button androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  Checkbox androidx.compose.material3  CheckboxDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  
DatePicker androidx.compose.material3  DatePickerDialog androidx.compose.material3  DatePickerState androidx.compose.material3  Divider androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  ElevatedCard androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  ExtendedFloatingActionButton androidx.compose.material3  FabPosition androidx.compose.material3  
FilterChip androidx.compose.material3  FilterChipDefaults androidx.compose.material3  FloatingActionButton androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  
MaterialTheme androidx.compose.material3  ModalBottomSheet androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  Scaffold androidx.compose.material3  SnackbarDuration androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  SwitchDefaults androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  	TextField androidx.compose.material3  TextFieldDefaults androidx.compose.material3  
TimePicker androidx.compose.material3  TimePickerState androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  rememberDatePickerState androidx.compose.material3  rememberModalBottomSheetState androidx.compose.material3  rememberTimePickerState androidx.compose.material3  colorScheme (androidx.compose.material3.MaterialTheme  
Composable androidx.compose.runtime  CompositionLocalProvider androidx.compose.runtime  DisposableEffect androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  MutableState androidx.compose.runtime  
SideEffect androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  snapshotFlow androidx.compose.runtime  getGETValue %androidx.compose.runtime.MutableState  getGetValue %androidx.compose.runtime.MutableState  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  getValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  rememberSaveable !androidx.compose.runtime.saveable  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  clipToBounds androidx.compose.ui.draw  scale androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  Offset androidx.compose.ui.geometry  Color androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  	luminance androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  Layout androidx.compose.ui.layout  	Placeable androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  LocalFocusManager androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  Role androidx.compose.ui.semantics  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextDecoration androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  Constraints androidx.compose.ui.unit  Dp androidx.compose.ui.unit  	IntOffset androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  Inject #androidx.core.app.ComponentActivity  MainActivityViewModel #androidx.core.app.ComponentActivity  SyncRepository #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  
toColorInt androidx.core.graphics  
toDrawable androidx.core.graphics.drawable  SplashScreen androidx.core.splashscreen  	Companion 'androidx.core.splashscreen.SplashScreen  installSplashScreen 1androidx.core.splashscreen.SplashScreen.Companion  WindowCompat androidx.core.view  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  preferencesDataStoreFile androidx.datastore.preferences  PreferenceDataStoreFactory #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  emptyPreferences #androidx.datastore.preferences.core  longPreferencesKey #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  
hiltViewModel  androidx.hilt.navigation.compose  
HiltWorker androidx.hilt.work  AndroidViewModel androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  SavedStateHandle androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  ClearLoginSessionUseCase #androidx.lifecycle.AndroidViewModel  DatabaseOperation #androidx.lifecycle.AndroidViewModel  DevUtils #androidx.lifecycle.AndroidViewModel  Inject #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  SessionRepository #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  asStateFlow #androidx.lifecycle.AndroidViewModel  get #androidx.lifecycle.SavedStateHandle  AddEditOrdinaryScheduleUiState androidx.lifecycle.ViewModel  AddOrdinaryScheduleUseCase androidx.lifecycle.ViewModel  AppConstants androidx.lifecycle.ViewModel  	AppRoutes androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  ApplicationContext androidx.lifecycle.ViewModel  Array androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  CalendarSyncManager androidx.lifecycle.ViewModel  
CalendarUtils androidx.lifecycle.ViewModel  Channel androidx.lifecycle.ViewModel  Char androidx.lifecycle.ViewModel  ClearLoginSessionUseCase androidx.lifecycle.ViewModel  ColorSchemeEnum androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  Course androidx.lifecycle.ViewModel  CourseDetailModel androidx.lifecycle.ViewModel  CourseDetailUiState androidx.lifecycle.ViewModel  
CourseNode androidx.lifecycle.ViewModel  CourseNodeDetailModel androidx.lifecycle.ViewModel  CourseNodeUiState androidx.lifecycle.ViewModel  CourseRepository androidx.lifecycle.ViewModel  CreateEditTableUiState androidx.lifecycle.ViewModel  DatabaseOperation androidx.lifecycle.ViewModel  
DatePeriod androidx.lifecycle.ViewModel  DeleteOrdinaryScheduleUseCase androidx.lifecycle.ViewModel  DevUtils androidx.lifecycle.ViewModel  EditCourseEvent androidx.lifecycle.ViewModel  EditCourseUiState androidx.lifecycle.ViewModel  Flow androidx.lifecycle.ViewModel   GetDefaultTableTimeConfigUseCase androidx.lifecycle.ViewModel  GetLoginUserIdFlowUseCase androidx.lifecycle.ViewModel  GetOrdinaryScheduleByIdUseCase androidx.lifecycle.ViewModel  GetOrdinarySchedulesUseCase androidx.lifecycle.ViewModel  GlobalSettingRepository androidx.lifecycle.ViewModel  HomeCourseItem androidx.lifecycle.ViewModel  HomeTodoItem androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Instant androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  	LocalTime androidx.lifecycle.ViewModel  Locale androidx.lifecycle.ViewModel  Log androidx.lifecycle.ViewModel  LoginUiState androidx.lifecycle.ViewModel  LoginUserUseCase androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  MutableSharedFlow androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  OrdinarySchedule androidx.lifecycle.ViewModel  OrdinaryScheduleDetailEvent androidx.lifecycle.ViewModel  OrdinaryScheduleDetailUiState androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  PermissionManager androidx.lifecycle.ViewModel  PriorityTag androidx.lifecycle.ViewModel  QuickAddScheduleUiState androidx.lifecycle.ViewModel  RegisterUiState androidx.lifecycle.ViewModel  RegisterUserUseCase androidx.lifecycle.ViewModel  SaveCourseState androidx.lifecycle.ViewModel  SaveLoginSessionUseCase androidx.lifecycle.ViewModel  	SaveState androidx.lifecycle.ViewModel  SavedStateHandle androidx.lifecycle.ViewModel  ScheduleStatus androidx.lifecycle.ViewModel  ScheduleUiState androidx.lifecycle.ViewModel  School androidx.lifecycle.ViewModel  SessionRepository androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  SimpleDateFormat androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  SyncManager androidx.lifecycle.ViewModel  SyncRepository androidx.lifecycle.ViewModel  	SyncState androidx.lifecycle.ViewModel  Table androidx.lifecycle.ViewModel  TableRepository androidx.lifecycle.ViewModel  TableTimeConfig androidx.lifecycle.ViewModel  TableTimeConfigRepository androidx.lifecycle.ViewModel  TaskCalendarSyncState androidx.lifecycle.ViewModel  
TaskFilter androidx.lifecycle.ViewModel  TaskItemUiModel androidx.lifecycle.ViewModel  TaskUiState androidx.lifecycle.ViewModel  
ThemeSettings androidx.lifecycle.ViewModel  TimeSlot androidx.lifecycle.ViewModel  TimeZone androidx.lifecycle.ViewModel  TodoUiModel androidx.lifecycle.ViewModel  TodoUiState androidx.lifecycle.ViewModel  Triple androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  UpdateOrdinaryScheduleUseCase androidx.lifecycle.ViewModel  WorkInfo androidx.lifecycle.ViewModel  _rawSchools androidx.lifecycle.ViewModel  also androidx.lifecycle.ViewModel  asSharedFlow androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  atTime androidx.lifecycle.ViewModel  checkNotNull androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  combine androidx.lifecycle.ViewModel  convertCourseNodesToTimeSlots androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  emptyMap androidx.lifecycle.ViewModel  filter androidx.lifecycle.ViewModel  filterAndGroupSchools androidx.lifecycle.ViewModel  
filterNotNull androidx.lifecycle.ViewModel  firstOrNull androidx.lifecycle.ViewModel  flatMap androidx.lifecycle.ViewModel  
flatMapLatest androidx.lifecycle.ViewModel  flow androidx.lifecycle.ViewModel  flowOf androidx.lifecycle.ViewModel   getDefaultTableTimeConfigUseCase androidx.lifecycle.ViewModel  getOrdinaryScheduleByIdUseCase androidx.lifecycle.ViewModel  getValue androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  
isNotEmpty androidx.lifecycle.ViewModel  isoDayNumber androidx.lifecycle.ViewModel  let androidx.lifecycle.ViewModel  listOf androidx.lifecycle.ViewModel  map androidx.lifecycle.ViewModel  minus androidx.lifecycle.ViewModel  mutableStateOf androidx.lifecycle.ViewModel  plus androidx.lifecycle.ViewModel  provideDelegate androidx.lifecycle.ViewModel  
receiveAsFlow androidx.lifecycle.ViewModel  setValue androidx.lifecycle.ViewModel  sortedBy androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  takeIf androidx.lifecycle.ViewModel  to androidx.lifecycle.ViewModel  	toInstant androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  
NavController androidx.navigation  NavHostController androidx.navigation  NavType androidx.navigation  navArgument androidx.navigation  currentDestination !androidx.navigation.NavController  route "androidx.navigation.NavDestination  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  
ColumnInfo 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Embedded 
androidx.room  Entity 
androidx.room  
ForeignKey 
androidx.room  Index 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Relation 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Transaction 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  CASCADE androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  invoke "androidx.room.ForeignKey.Companion  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  	CourseDao androidx.room.RoomDatabase  
CourseNodeDao androidx.room.RoomDatabase  GlobalSettingDao androidx.room.RoomDatabase  OrdinaryScheduleDao androidx.room.RoomDatabase  SyncMessageDao androidx.room.RoomDatabase  TableDao androidx.room.RoomDatabase  TableTimeConfigDao androidx.room.RoomDatabase  TimeSlotDao androidx.room.RoomDatabase  UserDao androidx.room.RoomDatabase  clearAllTables androidx.room.RoomDatabase  Constraints 
androidx.work  CoroutineWorker 
androidx.work  ExistingPeriodicWorkPolicy 
androidx.work  NetworkType 
androidx.work  OneTimeWorkRequestBuilder 
androidx.work  PeriodicWorkRequestBuilder 
androidx.work  WorkInfo 
androidx.work  WorkManager 
androidx.work  WorkerParameters 
androidx.work  Assisted androidx.work.CoroutineWorker  AssistedInject androidx.work.CoroutineWorker  Boolean androidx.work.CoroutineWorker  Context androidx.work.CoroutineWorker  Result androidx.work.CoroutineWorker  SyncManager androidx.work.CoroutineWorker  SyncRepository androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  Assisted androidx.work.ListenableWorker  AssistedInject androidx.work.ListenableWorker  Boolean androidx.work.ListenableWorker  Context androidx.work.ListenableWorker  Result androidx.work.ListenableWorker  SyncManager androidx.work.ListenableWorker  SyncRepository androidx.work.ListenableWorker  WorkerParameters androidx.work.ListenableWorker  State androidx.work.WorkInfo  Course bean  CourseBaseBean bean  CourseDetailBean bean  Float bean  Int bean  List bean  String bean  
TimeDetail bean  	TimeTable bean  WeekBean bean  Float bean.Course  Int bean.Course  String bean.Course  ColorSchemeEnum bean.CourseBaseBean  Float bean.CourseBaseBean  Int bean.CourseBaseBean  String bean.CourseBaseBean  Float bean.CourseDetailBean  Int bean.CourseDetailBean  String bean.CourseDetailBean  day bean.CourseDetailBean  endWeek bean.CourseDetailBean  id bean.CourseDetailBean  room bean.CourseDetailBean  	startNode bean.CourseDetailBean  	startWeek bean.CourseDetailBean  step bean.CourseDetailBean  tableId bean.CourseDetailBean  teacher bean.CourseDetailBean  type bean.CourseDetailBean  Int bean.TimeDetail  String bean.TimeDetail  endTime bean.TimeDetail  node bean.TimeDetail  	startTime bean.TimeDetail  List bean.TimeTable  String bean.TimeTable  
TimeDetail bean.TimeTable  name bean.TimeTable  timeList bean.TimeTable  Int 
bean.WeekBean  String 
bean.WeekBean  String .com.example.mykotlinapplication.utils.injectjs  buildHeadContent .com.example.mykotlinapplication.utils.injectjs  injectHeadRepairScript .com.example.mykotlinapplication.utils.injectjs  Boolean com.example.todoschedule  BuildConfig com.example.todoschedule  CoroutineScope com.example.todoschedule  Dispatchers com.example.todoschedule  MainActivity com.example.todoschedule  MainActivityViewModel com.example.todoschedule  MutableLiveData com.example.todoschedule  
SupervisorJob com.example.todoschedule  TodoScheduleApplication com.example.todoschedule  getValue com.example.todoschedule  provideDelegate com.example.todoschedule  
viewModels com.example.todoschedule  Bundle %com.example.todoschedule.MainActivity  MainActivityViewModel %com.example.todoschedule.MainActivity  getGETValue %com.example.todoschedule.MainActivity  getGetValue %com.example.todoschedule.MainActivity  getPROVIDEDelegate %com.example.todoschedule.MainActivity  getProvideDelegate %com.example.todoschedule.MainActivity  
getVIEWModels %com.example.todoschedule.MainActivity  getValue %com.example.todoschedule.MainActivity  
getViewModels %com.example.todoschedule.MainActivity  provideDelegate %com.example.todoschedule.MainActivity  
viewModels %com.example.todoschedule.MainActivity  Boolean .com.example.todoschedule.MainActivityViewModel  Inject .com.example.todoschedule.MainActivityViewModel  LiveData .com.example.todoschedule.MainActivityViewModel  MutableLiveData .com.example.todoschedule.MainActivityViewModel  SessionRepository .com.example.todoschedule.MainActivityViewModel  _isSessionLoaded .com.example.todoschedule.MainActivityViewModel  CoroutineScope 0com.example.todoschedule.TodoScheduleApplication  DevUtils 0com.example.todoschedule.TodoScheduleApplication  Dispatchers 0com.example.todoschedule.TodoScheduleApplication  Inject 0com.example.todoschedule.TodoScheduleApplication  
SupervisorJob 0com.example.todoschedule.TodoScheduleApplication  SyncManager 0com.example.todoschedule.TodoScheduleApplication  CoroutineScope :com.example.todoschedule.TodoScheduleApplication.Companion  DevUtils :com.example.todoschedule.TodoScheduleApplication.Companion  Dispatchers :com.example.todoschedule.TodoScheduleApplication.Companion  Inject :com.example.todoschedule.TodoScheduleApplication.Companion  
SupervisorJob :com.example.todoschedule.TodoScheduleApplication.Companion  SyncManager :com.example.todoschedule.TodoScheduleApplication.Companion  AppConstants 'com.example.todoschedule.core.constants  	AppRoutes 'com.example.todoschedule.core.constants  ColorSchemeEnum 'com.example.todoschedule.core.constants  	LocalDate 'com.example.todoschedule.core.constants  String 'com.example.todoschedule.core.constants  Api 4com.example.todoschedule.core.constants.AppConstants  	AppRoutes 4com.example.todoschedule.core.constants.AppConstants  ColorSchemeEnum 4com.example.todoschedule.core.constants.AppConstants  DEFAULT_COURSE_COLOR 4com.example.todoschedule.core.constants.AppConstants  Database 4com.example.todoschedule.core.constants.AppConstants  Ids 4com.example.todoschedule.core.constants.AppConstants  	LocalDate 4com.example.todoschedule.core.constants.AppConstants  String 4com.example.todoschedule.core.constants.AppConstants  UserPreferencesName 4com.example.todoschedule.core.constants.AppConstants  invoke 4com.example.todoschedule.core.constants.AppConstants  	Endpoints 8com.example.todoschedule.core.constants.AppConstants.Api  LOGIN Bcom.example.todoschedule.core.constants.AppConstants.Api.Endpoints  REGISTER Bcom.example.todoschedule.core.constants.AppConstants.Api.Endpoints  
DB_VERSION =com.example.todoschedule.core.constants.AppConstants.Database  	LocalDate =com.example.todoschedule.core.constants.AppConstants.Database  invoke =com.example.todoschedule.core.constants.AppConstants.Database  INVALID_TABLE_ID 8com.example.todoschedule.core.constants.AppConstants.Ids  INVALID_USER_ID 8com.example.todoschedule.core.constants.AppConstants.Ids  	AppRoutes ;com.example.todoschedule.core.constants.AppConstants.Routes  toJavaCalendar (com.example.todoschedule.core.extensions  AppConstants #com.example.todoschedule.core.utils  Array #com.example.todoschedule.core.utils  Boolean #com.example.todoschedule.core.utils  DevUtils #com.example.todoschedule.core.utils  Dispatchers #com.example.todoschedule.core.utils  	Exception #com.example.todoschedule.core.utils  Manifest #com.example.todoschedule.core.utils  PermissionManager #com.example.todoschedule.core.utils  String #com.example.todoschedule.core.utils  arrayOf #com.example.todoschedule.core.utils  context #com.example.todoschedule.core.utils  db #com.example.todoschedule.core.utils  preferencesDataStoreFile #com.example.todoschedule.core.utils  withContext #com.example.todoschedule.core.utils  AppConstants ,com.example.todoschedule.core.utils.DevUtils  AppDatabase ,com.example.todoschedule.core.utils.DevUtils  ApplicationContext ,com.example.todoschedule.core.utils.DevUtils  Context ,com.example.todoschedule.core.utils.DevUtils  Dispatchers ,com.example.todoschedule.core.utils.DevUtils  	Exception ,com.example.todoschedule.core.utils.DevUtils  Inject ,com.example.todoschedule.core.utils.DevUtils  context ,com.example.todoschedule.core.utils.DevUtils  db ,com.example.todoschedule.core.utils.DevUtils  getPREFERENCESDataStoreFile ,com.example.todoschedule.core.utils.DevUtils  getPreferencesDataStoreFile ,com.example.todoschedule.core.utils.DevUtils  getWITHContext ,com.example.todoschedule.core.utils.DevUtils  getWithContext ,com.example.todoschedule.core.utils.DevUtils  preferencesDataStoreFile ,com.example.todoschedule.core.utils.DevUtils  withContext ,com.example.todoschedule.core.utils.DevUtils  ApplicationContext 5com.example.todoschedule.core.utils.PermissionManager  Array 5com.example.todoschedule.core.utils.PermissionManager  Boolean 5com.example.todoschedule.core.utils.PermissionManager  Context 5com.example.todoschedule.core.utils.PermissionManager  Inject 5com.example.todoschedule.core.utils.PermissionManager  Manifest 5com.example.todoschedule.core.utils.PermissionManager  String 5com.example.todoschedule.core.utils.PermissionManager  arrayOf 5com.example.todoschedule.core.utils.PermissionManager  ApplicationContext ?com.example.todoschedule.core.utils.PermissionManager.Companion  Array ?com.example.todoschedule.core.utils.PermissionManager.Companion  Boolean ?com.example.todoschedule.core.utils.PermissionManager.Companion  Context ?com.example.todoschedule.core.utils.PermissionManager.Companion  Inject ?com.example.todoschedule.core.utils.PermissionManager.Companion  Manifest ?com.example.todoschedule.core.utils.PermissionManager.Companion  String ?com.example.todoschedule.core.utils.PermissionManager.Companion  arrayOf ?com.example.todoschedule.core.utils.PermissionManager.Companion  
getARRAYOf ?com.example.todoschedule.core.utils.PermissionManager.Companion  
getArrayOf ?com.example.todoschedule.core.utils.PermissionManager.Companion  AppDatabase &com.example.todoschedule.data.database  
Converters &com.example.todoschedule.data.database  CourseEntity &com.example.todoschedule.data.database  CourseNodeEntity &com.example.todoschedule.data.database  GlobalTableSettingEntity &com.example.todoschedule.data.database  OrdinaryScheduleEntity &com.example.todoschedule.data.database  SyncMessageEntity &com.example.todoschedule.data.database  TableEntity &com.example.todoschedule.data.database  TableTimeConfigEntity &com.example.todoschedule.data.database   TableTimeConfigNodeDetaileEntity &com.example.todoschedule.data.database  TimeSlotEntity &com.example.todoschedule.data.database  
UserEntity &com.example.todoschedule.data.database  	CourseDao 2com.example.todoschedule.data.database.AppDatabase  
CourseNodeDao 2com.example.todoschedule.data.database.AppDatabase  GlobalSettingDao 2com.example.todoschedule.data.database.AppDatabase  OrdinaryScheduleDao 2com.example.todoschedule.data.database.AppDatabase  SyncMessageDao 2com.example.todoschedule.data.database.AppDatabase  TableDao 2com.example.todoschedule.data.database.AppDatabase  TableTimeConfigDao 2com.example.todoschedule.data.database.AppDatabase  TimeSlotDao 2com.example.todoschedule.data.database.AppDatabase  UserDao 2com.example.todoschedule.data.database.AppDatabase  clearAllTables 2com.example.todoschedule.data.database.AppDatabase  
Converters 0com.example.todoschedule.data.database.converter  DatabaseConverters 0com.example.todoschedule.data.database.converter  Int 0com.example.todoschedule.data.database.converter  Json 0com.example.todoschedule.data.database.converter  List 0com.example.todoschedule.data.database.converter  Long 0com.example.todoschedule.data.database.converter  ReminderType 0com.example.todoschedule.data.database.converter  ScheduleStatus 0com.example.todoschedule.data.database.converter  ScheduleType 0com.example.todoschedule.data.database.converter  String 0com.example.todoschedule.data.database.converter  com 0com.example.todoschedule.data.database.converter  Instant ;com.example.todoschedule.data.database.converter.Converters  Int ;com.example.todoschedule.data.database.converter.Converters  Json ;com.example.todoschedule.data.database.converter.Converters  List ;com.example.todoschedule.data.database.converter.Converters  	LocalDate ;com.example.todoschedule.data.database.converter.Converters  	LocalTime ;com.example.todoschedule.data.database.converter.Converters  Long ;com.example.todoschedule.data.database.converter.Converters  ReminderType ;com.example.todoschedule.data.database.converter.Converters  ScheduleStatus ;com.example.todoschedule.data.database.converter.Converters  ScheduleType ;com.example.todoschedule.data.database.converter.Converters  String ;com.example.todoschedule.data.database.converter.Converters  
TypeConverter ;com.example.todoschedule.data.database.converter.Converters  com ;com.example.todoschedule.data.database.converter.Converters  invoke ;com.example.todoschedule.data.database.converter.Converters  ORDINARY =com.example.todoschedule.data.database.converter.ScheduleType  	CourseDao *com.example.todoschedule.data.database.dao  
CourseNodeDao *com.example.todoschedule.data.database.dao  GlobalSettingDao *com.example.todoschedule.data.database.dao  Int *com.example.todoschedule.data.database.dao  List *com.example.todoschedule.data.database.dao  Long *com.example.todoschedule.data.database.dao  OnConflictStrategy *com.example.todoschedule.data.database.dao  OrdinaryScheduleDao *com.example.todoschedule.data.database.dao  String *com.example.todoschedule.data.database.dao  SyncMessageDao *com.example.todoschedule.data.database.dao  TableDao *com.example.todoschedule.data.database.dao  TableTimeConfigDao *com.example.todoschedule.data.database.dao  TimeSlotDao *com.example.todoschedule.data.database.dao  UserDao *com.example.todoschedule.data.database.dao  CourseEntity 4com.example.todoschedule.data.database.dao.CourseDao  CourseNodeEntity 4com.example.todoschedule.data.database.dao.CourseDao  CourseWithNodes 4com.example.todoschedule.data.database.dao.CourseDao  Flow 4com.example.todoschedule.data.database.dao.CourseDao  Insert 4com.example.todoschedule.data.database.dao.CourseDao  Int 4com.example.todoschedule.data.database.dao.CourseDao  List 4com.example.todoschedule.data.database.dao.CourseDao  Long 4com.example.todoschedule.data.database.dao.CourseDao  OnConflictStrategy 4com.example.todoschedule.data.database.dao.CourseDao  Query 4com.example.todoschedule.data.database.dao.CourseDao  String 4com.example.todoschedule.data.database.dao.CourseDao  Transaction 4com.example.todoschedule.data.database.dao.CourseDao  Update 4com.example.todoschedule.data.database.dao.CourseDao  CourseNodeEntity 8com.example.todoschedule.data.database.dao.CourseNodeDao  Delete 8com.example.todoschedule.data.database.dao.CourseNodeDao  Flow 8com.example.todoschedule.data.database.dao.CourseNodeDao  Insert 8com.example.todoschedule.data.database.dao.CourseNodeDao  Int 8com.example.todoschedule.data.database.dao.CourseNodeDao  List 8com.example.todoschedule.data.database.dao.CourseNodeDao  Long 8com.example.todoschedule.data.database.dao.CourseNodeDao  OnConflictStrategy 8com.example.todoschedule.data.database.dao.CourseNodeDao  Query 8com.example.todoschedule.data.database.dao.CourseNodeDao  String 8com.example.todoschedule.data.database.dao.CourseNodeDao  Update 8com.example.todoschedule.data.database.dao.CourseNodeDao  Flow ;com.example.todoschedule.data.database.dao.GlobalSettingDao  GlobalTableSettingEntity ;com.example.todoschedule.data.database.dao.GlobalSettingDao  Insert ;com.example.todoschedule.data.database.dao.GlobalSettingDao  Int ;com.example.todoschedule.data.database.dao.GlobalSettingDao  Long ;com.example.todoschedule.data.database.dao.GlobalSettingDao  OnConflictStrategy ;com.example.todoschedule.data.database.dao.GlobalSettingDao  Query ;com.example.todoschedule.data.database.dao.GlobalSettingDao  Update ;com.example.todoschedule.data.database.dao.GlobalSettingDao  Delete >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Flow >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Insert >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Int >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  List >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Long >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  OnConflictStrategy >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  OrdinaryScheduleEntity >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  OrdinaryScheduleWithTimeSlots >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Query >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  String >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Transaction >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Update >com.example.todoschedule.data.database.dao.OrdinaryScheduleDao  Delete 9com.example.todoschedule.data.database.dao.SyncMessageDao  Flow 9com.example.todoschedule.data.database.dao.SyncMessageDao  Insert 9com.example.todoschedule.data.database.dao.SyncMessageDao  Int 9com.example.todoschedule.data.database.dao.SyncMessageDao  List 9com.example.todoschedule.data.database.dao.SyncMessageDao  Long 9com.example.todoschedule.data.database.dao.SyncMessageDao  OnConflictStrategy 9com.example.todoschedule.data.database.dao.SyncMessageDao  Query 9com.example.todoschedule.data.database.dao.SyncMessageDao  String 9com.example.todoschedule.data.database.dao.SyncMessageDao  SyncMessageEntity 9com.example.todoschedule.data.database.dao.SyncMessageDao  Update 9com.example.todoschedule.data.database.dao.SyncMessageDao  Flow 3com.example.todoschedule.data.database.dao.TableDao  Insert 3com.example.todoschedule.data.database.dao.TableDao  Int 3com.example.todoschedule.data.database.dao.TableDao  List 3com.example.todoschedule.data.database.dao.TableDao  Long 3com.example.todoschedule.data.database.dao.TableDao  OnConflictStrategy 3com.example.todoschedule.data.database.dao.TableDao  Query 3com.example.todoschedule.data.database.dao.TableDao  String 3com.example.todoschedule.data.database.dao.TableDao  TableEntity 3com.example.todoschedule.data.database.dao.TableDao  Update 3com.example.todoschedule.data.database.dao.TableDao  Delete =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Flow =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Insert =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Int =com.example.todoschedule.data.database.dao.TableTimeConfigDao  List =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Long =com.example.todoschedule.data.database.dao.TableTimeConfigDao  OnConflictStrategy =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Query =com.example.todoschedule.data.database.dao.TableTimeConfigDao  TableTimeConfigEntity =com.example.todoschedule.data.database.dao.TableTimeConfigDao   TableTimeConfigNodeDetaileEntity =com.example.todoschedule.data.database.dao.TableTimeConfigDao  TableTimeConfigWithNodes =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Transaction =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Update =com.example.todoschedule.data.database.dao.TableTimeConfigDao  Delete 6com.example.todoschedule.data.database.dao.TimeSlotDao  Flow 6com.example.todoschedule.data.database.dao.TimeSlotDao  Insert 6com.example.todoschedule.data.database.dao.TimeSlotDao  Int 6com.example.todoschedule.data.database.dao.TimeSlotDao  List 6com.example.todoschedule.data.database.dao.TimeSlotDao  Long 6com.example.todoschedule.data.database.dao.TimeSlotDao  OnConflictStrategy 6com.example.todoschedule.data.database.dao.TimeSlotDao  Query 6com.example.todoschedule.data.database.dao.TimeSlotDao  ScheduleType 6com.example.todoschedule.data.database.dao.TimeSlotDao  TimeSlotEntity 6com.example.todoschedule.data.database.dao.TimeSlotDao  Update 6com.example.todoschedule.data.database.dao.TimeSlotDao  Flow 2com.example.todoschedule.data.database.dao.UserDao  Insert 2com.example.todoschedule.data.database.dao.UserDao  Int 2com.example.todoschedule.data.database.dao.UserDao  List 2com.example.todoschedule.data.database.dao.UserDao  Long 2com.example.todoschedule.data.database.dao.UserDao  OnConflictStrategy 2com.example.todoschedule.data.database.dao.UserDao  Query 2com.example.todoschedule.data.database.dao.UserDao  String 2com.example.todoschedule.data.database.dao.UserDao  Update 2com.example.todoschedule.data.database.dao.UserDao  
UserEntity 2com.example.todoschedule.data.database.dao.UserDao  Boolean -com.example.todoschedule.data.database.entity  CourseEntity -com.example.todoschedule.data.database.entity  CourseNodeEntity -com.example.todoschedule.data.database.entity  Float -com.example.todoschedule.data.database.entity  GlobalTableSettingEntity -com.example.todoschedule.data.database.entity  Int -com.example.todoschedule.data.database.entity  Long -com.example.todoschedule.data.database.entity  OrdinaryScheduleEntity -com.example.todoschedule.data.database.entity  String -com.example.todoschedule.data.database.entity  SyncMessageEntity -com.example.todoschedule.data.database.entity  Syncable -com.example.todoschedule.data.database.entity  TableEntity -com.example.todoschedule.data.database.entity  TableTimeConfigEntity -com.example.todoschedule.data.database.entity   TableTimeConfigNodeDetaileEntity -com.example.todoschedule.data.database.entity  TimeSlotEntity -com.example.todoschedule.data.database.entity  
UserEntity -com.example.todoschedule.data.database.entity  com -com.example.todoschedule.data.database.entity  toEntity -com.example.todoschedule.data.database.entity  
ColumnInfo :com.example.todoschedule.data.database.entity.CourseEntity  Float :com.example.todoschedule.data.database.entity.CourseEntity  Int :com.example.todoschedule.data.database.entity.CourseEntity  Long :com.example.todoschedule.data.database.entity.CourseEntity  
PrimaryKey :com.example.todoschedule.data.database.entity.CourseEntity  String :com.example.todoschedule.data.database.entity.CourseEntity  crdtKey :com.example.todoschedule.data.database.entity.CourseEntity  
ColumnInfo >com.example.todoschedule.data.database.entity.CourseNodeEntity  Int >com.example.todoschedule.data.database.entity.CourseNodeEntity  Long >com.example.todoschedule.data.database.entity.CourseNodeEntity  
PrimaryKey >com.example.todoschedule.data.database.entity.CourseNodeEntity  String >com.example.todoschedule.data.database.entity.CourseNodeEntity  crdtKey >com.example.todoschedule.data.database.entity.CourseNodeEntity  Boolean Fcom.example.todoschedule.data.database.entity.GlobalTableSettingEntity  Int Fcom.example.todoschedule.data.database.entity.GlobalTableSettingEntity  
PrimaryKey Fcom.example.todoschedule.data.database.entity.GlobalTableSettingEntity  String Fcom.example.todoschedule.data.database.entity.GlobalTableSettingEntity  Boolean Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  
ColumnInfo Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  Int Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  Long Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  
PrimaryKey Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  ScheduleStatus Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  String Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  crdtKey Dcom.example.todoschedule.data.database.entity.OrdinaryScheduleEntity  Boolean ?com.example.todoschedule.data.database.entity.SyncMessageEntity  
ColumnInfo ?com.example.todoschedule.data.database.entity.SyncMessageEntity  HybridLogicalClock ?com.example.todoschedule.data.database.entity.SyncMessageEntity  Int ?com.example.todoschedule.data.database.entity.SyncMessageEntity  Long ?com.example.todoschedule.data.database.entity.SyncMessageEntity  
PrimaryKey ?com.example.todoschedule.data.database.entity.SyncMessageEntity  String ?com.example.todoschedule.data.database.entity.SyncMessageEntity  
SyncConstants ?com.example.todoschedule.data.database.entity.SyncMessageEntity  SyncMessageDto ?com.example.todoschedule.data.database.entity.SyncMessageEntity  SyncMessageEntity ?com.example.todoschedule.data.database.entity.SyncMessageEntity  com ?com.example.todoschedule.data.database.entity.SyncMessageEntity  String 6com.example.todoschedule.data.database.entity.Syncable  
ColumnInfo 9com.example.todoschedule.data.database.entity.TableEntity  Int 9com.example.todoschedule.data.database.entity.TableEntity  	LocalDate 9com.example.todoschedule.data.database.entity.TableEntity  Long 9com.example.todoschedule.data.database.entity.TableEntity  
PrimaryKey 9com.example.todoschedule.data.database.entity.TableEntity  String 9com.example.todoschedule.data.database.entity.TableEntity  crdtKey 9com.example.todoschedule.data.database.entity.TableEntity  Boolean Ccom.example.todoschedule.data.database.entity.TableTimeConfigEntity  
ColumnInfo Ccom.example.todoschedule.data.database.entity.TableTimeConfigEntity  Int Ccom.example.todoschedule.data.database.entity.TableTimeConfigEntity  
PrimaryKey Ccom.example.todoschedule.data.database.entity.TableTimeConfigEntity  String Ccom.example.todoschedule.data.database.entity.TableTimeConfigEntity  
ColumnInfo Ncom.example.todoschedule.data.database.entity.TableTimeConfigNodeDetaileEntity  Int Ncom.example.todoschedule.data.database.entity.TableTimeConfigNodeDetaileEntity  	LocalTime Ncom.example.todoschedule.data.database.entity.TableTimeConfigNodeDetaileEntity  
PrimaryKey Ncom.example.todoschedule.data.database.entity.TableTimeConfigNodeDetaileEntity  String Ncom.example.todoschedule.data.database.entity.TableTimeConfigNodeDetaileEntity  Boolean <com.example.todoschedule.data.database.entity.TimeSlotEntity  
ColumnInfo <com.example.todoschedule.data.database.entity.TimeSlotEntity  Int <com.example.todoschedule.data.database.entity.TimeSlotEntity  Long <com.example.todoschedule.data.database.entity.TimeSlotEntity  
PrimaryKey <com.example.todoschedule.data.database.entity.TimeSlotEntity  ReminderType <com.example.todoschedule.data.database.entity.TimeSlotEntity  ScheduleType <com.example.todoschedule.data.database.entity.TimeSlotEntity  String <com.example.todoschedule.data.database.entity.TimeSlotEntity  Instant 8com.example.todoschedule.data.database.entity.UserEntity  Int 8com.example.todoschedule.data.database.entity.UserEntity  
PrimaryKey 8com.example.todoschedule.data.database.entity.UserEntity  String 8com.example.todoschedule.data.database.entity.UserEntity  Int $com.example.todoschedule.data.mapper  List $com.example.todoschedule.data.mapper  filterAndToDomainModel $com.example.todoschedule.data.mapper  toCourse $com.example.todoschedule.data.mapper  toCourseEntity $com.example.todoschedule.data.mapper  toCourseNode $com.example.todoschedule.data.mapper  toCourseNodeEntity $com.example.todoschedule.data.mapper  
toDomainModel $com.example.todoschedule.data.mapper  toEntity $com.example.todoschedule.data.mapper  toGlobalTableSetting $com.example.todoschedule.data.mapper  toGlobalTableSettingEntity $com.example.todoschedule.data.mapper  toUser $com.example.todoschedule.data.mapper  toUserEntity $com.example.todoschedule.data.mapper  CourseWithNodes #com.example.todoschedule.data.model  List #com.example.todoschedule.data.model  OrdinaryScheduleWithTimeSlots #com.example.todoschedule.data.model  TableTimeConfigWithNodes #com.example.todoschedule.data.model  CourseEntity 3com.example.todoschedule.data.model.CourseWithNodes  CourseNodeEntity 3com.example.todoschedule.data.model.CourseWithNodes  Embedded 3com.example.todoschedule.data.model.CourseWithNodes  List 3com.example.todoschedule.data.model.CourseWithNodes  Relation 3com.example.todoschedule.data.model.CourseWithNodes  Embedded Acom.example.todoschedule.data.model.OrdinaryScheduleWithTimeSlots  List Acom.example.todoschedule.data.model.OrdinaryScheduleWithTimeSlots  OrdinaryScheduleEntity Acom.example.todoschedule.data.model.OrdinaryScheduleWithTimeSlots  Relation Acom.example.todoschedule.data.model.OrdinaryScheduleWithTimeSlots  TimeSlotEntity Acom.example.todoschedule.data.model.OrdinaryScheduleWithTimeSlots  Embedded <com.example.todoschedule.data.model.TableTimeConfigWithNodes  List <com.example.todoschedule.data.model.TableTimeConfigWithNodes  Relation <com.example.todoschedule.data.model.TableTimeConfigWithNodes  TableTimeConfigEntity <com.example.todoschedule.data.model.TableTimeConfigWithNodes   TableTimeConfigNodeDetaileEntity <com.example.todoschedule.data.model.TableTimeConfigWithNodes  Int $com.example.todoschedule.data.remote  List $com.example.todoschedule.data.remote  TodoApiService $com.example.todoschedule.data.remote  Unit $com.example.todoschedule.data.remote  ApiResponse 3com.example.todoschedule.data.remote.TodoApiService  Body 3com.example.todoschedule.data.remote.TodoApiService  	CourseDto 3com.example.todoschedule.data.remote.TodoApiService  DELETE 3com.example.todoschedule.data.remote.TodoApiService  GET 3com.example.todoschedule.data.remote.TodoApiService  Int 3com.example.todoschedule.data.remote.TodoApiService  List 3com.example.todoschedule.data.remote.TodoApiService  POST 3com.example.todoschedule.data.remote.TodoApiService  PUT 3com.example.todoschedule.data.remote.TodoApiService  Path 3com.example.todoschedule.data.remote.TodoApiService  Query 3com.example.todoschedule.data.remote.TodoApiService  TableDto 3com.example.todoschedule.data.remote.TodoApiService  Unit 3com.example.todoschedule.data.remote.TodoApiService  AppConstants (com.example.todoschedule.data.remote.api  Boolean (com.example.todoschedule.data.remote.api  List (com.example.todoschedule.data.remote.api  String (com.example.todoschedule.data.remote.api  SyncApi (com.example.todoschedule.data.remote.api  UserApiService (com.example.todoschedule.data.remote.api  Body 0com.example.todoschedule.data.remote.api.SyncApi  Boolean 0com.example.todoschedule.data.remote.api.SyncApi  DeviceRegistrationDto 0com.example.todoschedule.data.remote.api.SyncApi  GET 0com.example.todoschedule.data.remote.api.SyncApi  Header 0com.example.todoschedule.data.remote.api.SyncApi  List 0com.example.todoschedule.data.remote.api.SyncApi  POST 0com.example.todoschedule.data.remote.api.SyncApi  Path 0com.example.todoschedule.data.remote.api.SyncApi  Response 0com.example.todoschedule.data.remote.api.SyncApi  String 0com.example.todoschedule.data.remote.api.SyncApi  SyncMessageDto 0com.example.todoschedule.data.remote.api.SyncApi  SyncMessagesDto 0com.example.todoschedule.data.remote.api.SyncApi  UploadSyncResponseDto 0com.example.todoschedule.data.remote.api.SyncApi  ApiResponse 7com.example.todoschedule.data.remote.api.UserApiService  AppConstants 7com.example.todoschedule.data.remote.api.UserApiService  Body 7com.example.todoschedule.data.remote.api.UserApiService  POST 7com.example.todoschedule.data.remote.api.UserApiService  UserDto 7com.example.todoschedule.data.remote.api.UserApiService  UserLoginRequest 7com.example.todoschedule.data.remote.api.UserApiService  UserRegisterRequest 7com.example.todoschedule.data.remote.api.UserApiService  ApiResponse (com.example.todoschedule.data.remote.dto  Int (com.example.todoschedule.data.remote.dto  String (com.example.todoschedule.data.remote.dto  UserDto (com.example.todoschedule.data.remote.dto  UserLoginRequest (com.example.todoschedule.data.remote.dto  UserRegisterRequest (com.example.todoschedule.data.remote.dto  Int 4com.example.todoschedule.data.remote.dto.ApiResponse  String 4com.example.todoschedule.data.remote.dto.ApiResponse  Int 0com.example.todoschedule.data.remote.dto.UserDto  String 0com.example.todoschedule.data.remote.dto.UserDto  String 9com.example.todoschedule.data.remote.dto.UserLoginRequest  String <com.example.todoschedule.data.remote.dto.UserRegisterRequest  ApiResponse *com.example.todoschedule.data.remote.model  Boolean *com.example.todoschedule.data.remote.model  	CourseDto *com.example.todoschedule.data.remote.model  
CourseNodeDto *com.example.todoschedule.data.remote.model  Float *com.example.todoschedule.data.remote.model  Int *com.example.todoschedule.data.remote.model  List *com.example.todoschedule.data.remote.model  String *com.example.todoschedule.data.remote.model  TableDto *com.example.todoschedule.data.remote.model  Boolean 6com.example.todoschedule.data.remote.model.ApiResponse  Int 6com.example.todoschedule.data.remote.model.ApiResponse  String 6com.example.todoschedule.data.remote.model.ApiResponse  
CourseNodeDto 4com.example.todoschedule.data.remote.model.CourseDto  Float 4com.example.todoschedule.data.remote.model.CourseDto  Int 4com.example.todoschedule.data.remote.model.CourseDto  List 4com.example.todoschedule.data.remote.model.CourseDto  String 4com.example.todoschedule.data.remote.model.CourseDto  Int 8com.example.todoschedule.data.remote.model.CourseNodeDto  String 8com.example.todoschedule.data.remote.model.CourseNodeDto  Int 3com.example.todoschedule.data.remote.model.TableDto  String 3com.example.todoschedule.data.remote.model.TableDto  Boolean (com.example.todoschedule.data.repository  CoroutineScope (com.example.todoschedule.data.repository  CourseRepositoryImpl (com.example.todoschedule.data.repository  Dispatchers (com.example.todoschedule.data.repository  ExperimentalCoroutinesApi (com.example.todoschedule.data.repository  GlobalSettingRepositoryImpl (com.example.todoschedule.data.repository  Int (com.example.todoschedule.data.repository  List (com.example.todoschedule.data.repository  Long (com.example.todoschedule.data.repository  OptIn (com.example.todoschedule.data.repository  OrdinaryScheduleRepositoryImpl (com.example.todoschedule.data.repository  PreferencesKeys (com.example.todoschedule.data.repository  RemoteUserRepositoryImpl (com.example.todoschedule.data.repository  Result (com.example.todoschedule.data.repository  ScheduleType (com.example.todoschedule.data.repository  SessionRepositoryImpl (com.example.todoschedule.data.repository  SharingStarted (com.example.todoschedule.data.repository  String (com.example.todoschedule.data.repository  
SupervisorJob (com.example.todoschedule.data.repository  SyncRepository (com.example.todoschedule.data.repository  SyncRepositoryImpl (com.example.todoschedule.data.repository  TableRepositoryImpl (com.example.todoschedule.data.repository  TableTimeConfigRepositoryImpl (com.example.todoschedule.data.repository  
ThemeSettings (com.example.todoschedule.data.repository  UserRepositoryImpl (com.example.todoschedule.data.repository  booleanPreferencesKey (com.example.todoschedule.data.repository  catch (com.example.todoschedule.data.repository  emptyPreferences (com.example.todoschedule.data.repository  longPreferencesKey (com.example.todoschedule.data.repository  map (com.example.todoschedule.data.repository  stateIn (com.example.todoschedule.data.repository  stringPreferencesKey (com.example.todoschedule.data.repository  Course =com.example.todoschedule.data.repository.CourseRepositoryImpl  	CourseDao =com.example.todoschedule.data.repository.CourseRepositoryImpl  
CourseNode =com.example.todoschedule.data.repository.CourseRepositoryImpl  ExperimentalCoroutinesApi =com.example.todoschedule.data.repository.CourseRepositoryImpl  Flow =com.example.todoschedule.data.repository.CourseRepositoryImpl  GlobalSettingRepository =com.example.todoschedule.data.repository.CourseRepositoryImpl  Inject =com.example.todoschedule.data.repository.CourseRepositoryImpl  Int =com.example.todoschedule.data.repository.CourseRepositoryImpl  List =com.example.todoschedule.data.repository.CourseRepositoryImpl  Long =com.example.todoschedule.data.repository.CourseRepositoryImpl  OptIn =com.example.todoschedule.data.repository.CourseRepositoryImpl  SessionRepository =com.example.todoschedule.data.repository.CourseRepositoryImpl  SyncManager =com.example.todoschedule.data.repository.CourseRepositoryImpl  Flow Dcom.example.todoschedule.data.repository.GlobalSettingRepositoryImpl  GlobalSettingDao Dcom.example.todoschedule.data.repository.GlobalSettingRepositoryImpl  GlobalTableSetting Dcom.example.todoschedule.data.repository.GlobalSettingRepositoryImpl  Inject Dcom.example.todoschedule.data.repository.GlobalSettingRepositoryImpl  Int Dcom.example.todoschedule.data.repository.GlobalSettingRepositoryImpl  List Dcom.example.todoschedule.data.repository.GlobalSettingRepositoryImpl  Long Dcom.example.todoschedule.data.repository.GlobalSettingRepositoryImpl  Flow Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  Inject Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  Int Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  List Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  Long Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  OrdinarySchedule Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  OrdinaryScheduleDao Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  ScheduleType Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  TimeSlotDao Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  Transaction Gcom.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl  Inject Acom.example.todoschedule.data.repository.RemoteUserRepositoryImpl  Result Acom.example.todoschedule.data.repository.RemoteUserRepositoryImpl  SessionRepository Acom.example.todoschedule.data.repository.RemoteUserRepositoryImpl  String Acom.example.todoschedule.data.repository.RemoteUserRepositoryImpl  User Acom.example.todoschedule.data.repository.RemoteUserRepositoryImpl  UserApiService Acom.example.todoschedule.data.repository.RemoteUserRepositoryImpl  Boolean >com.example.todoschedule.data.repository.SessionRepositoryImpl  CoroutineScope >com.example.todoschedule.data.repository.SessionRepositoryImpl  	DataStore >com.example.todoschedule.data.repository.SessionRepositoryImpl  Dispatchers >com.example.todoschedule.data.repository.SessionRepositoryImpl  Flow >com.example.todoschedule.data.repository.SessionRepositoryImpl  IOException >com.example.todoschedule.data.repository.SessionRepositoryImpl  Inject >com.example.todoschedule.data.repository.SessionRepositoryImpl  Long >com.example.todoschedule.data.repository.SessionRepositoryImpl  Preferences >com.example.todoschedule.data.repository.SessionRepositoryImpl  PreferencesKeys >com.example.todoschedule.data.repository.SessionRepositoryImpl  SharingStarted >com.example.todoschedule.data.repository.SessionRepositoryImpl  	StateFlow >com.example.todoschedule.data.repository.SessionRepositoryImpl  String >com.example.todoschedule.data.repository.SessionRepositoryImpl  
SupervisorJob >com.example.todoschedule.data.repository.SessionRepositoryImpl  
ThemeSettings >com.example.todoschedule.data.repository.SessionRepositoryImpl  booleanPreferencesKey >com.example.todoschedule.data.repository.SessionRepositoryImpl  catch >com.example.todoschedule.data.repository.SessionRepositoryImpl  	dataStore >com.example.todoschedule.data.repository.SessionRepositoryImpl  emptyPreferences >com.example.todoschedule.data.repository.SessionRepositoryImpl  getCATCH >com.example.todoschedule.data.repository.SessionRepositoryImpl  getCatch >com.example.todoschedule.data.repository.SessionRepositoryImpl  getEMPTYPreferences >com.example.todoschedule.data.repository.SessionRepositoryImpl  getEmptyPreferences >com.example.todoschedule.data.repository.SessionRepositoryImpl  getMAP >com.example.todoschedule.data.repository.SessionRepositoryImpl  getMap >com.example.todoschedule.data.repository.SessionRepositoryImpl  
getSTATEIn >com.example.todoschedule.data.repository.SessionRepositoryImpl  
getStateIn >com.example.todoschedule.data.repository.SessionRepositoryImpl  longPreferencesKey >com.example.todoschedule.data.repository.SessionRepositoryImpl  map >com.example.todoschedule.data.repository.SessionRepositoryImpl  scope >com.example.todoschedule.data.repository.SessionRepositoryImpl  stateIn >com.example.todoschedule.data.repository.SessionRepositoryImpl  stringPreferencesKey >com.example.todoschedule.data.repository.SessionRepositoryImpl  
IS_DARK_THEME Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  USER_ID Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  USE_MATERIAL_YOU Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  booleanPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  getBOOLEANPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  getBooleanPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  getLONGPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  getLongPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  getSTRINGPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  getStringPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  longPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  stringPreferencesKey Ncom.example.todoschedule.data.repository.SessionRepositoryImpl.PreferencesKeys  Boolean 7com.example.todoschedule.data.repository.SyncRepository  	CourseDao 7com.example.todoschedule.data.repository.SyncRepository  
CourseNodeDao 7com.example.todoschedule.data.repository.SyncRepository  Flow 7com.example.todoschedule.data.repository.SyncRepository  Int 7com.example.todoschedule.data.repository.SyncRepository  List 7com.example.todoschedule.data.repository.SyncRepository  Long 7com.example.todoschedule.data.repository.SyncRepository  OrdinaryScheduleDao 7com.example.todoschedule.data.repository.SyncRepository  String 7com.example.todoschedule.data.repository.SyncRepository  
SyncConstants 7com.example.todoschedule.data.repository.SyncRepository  SyncMessageDto 7com.example.todoschedule.data.repository.SyncRepository  SyncMessageEntity 7com.example.todoschedule.data.repository.SyncRepository  TableDao 7com.example.todoschedule.data.repository.SyncRepository  AppDatabase ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Boolean ;com.example.todoschedule.data.repository.SyncRepositoryImpl  	CourseDao ;com.example.todoschedule.data.repository.SyncRepositoryImpl  
CourseNodeDao ;com.example.todoschedule.data.repository.SyncRepositoryImpl  DeviceIdManager ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Flow ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Inject ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Int ;com.example.todoschedule.data.repository.SyncRepositoryImpl  List ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Long ;com.example.todoschedule.data.repository.SyncRepositoryImpl  OrdinaryScheduleDao ;com.example.todoschedule.data.repository.SyncRepositoryImpl  String ;com.example.todoschedule.data.repository.SyncRepositoryImpl  SyncApi ;com.example.todoschedule.data.repository.SyncRepositoryImpl  
SyncConstants ;com.example.todoschedule.data.repository.SyncRepositoryImpl  SyncMessageDao ;com.example.todoschedule.data.repository.SyncRepositoryImpl  SyncMessageDto ;com.example.todoschedule.data.repository.SyncRepositoryImpl  SyncMessageEntity ;com.example.todoschedule.data.repository.SyncRepositoryImpl  TableDao ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Flow <com.example.todoschedule.data.repository.TableRepositoryImpl  Inject <com.example.todoschedule.data.repository.TableRepositoryImpl  Int <com.example.todoschedule.data.repository.TableRepositoryImpl  List <com.example.todoschedule.data.repository.TableRepositoryImpl  Long <com.example.todoschedule.data.repository.TableRepositoryImpl  Table <com.example.todoschedule.data.repository.TableRepositoryImpl  TableDao <com.example.todoschedule.data.repository.TableRepositoryImpl  TableEntity <com.example.todoschedule.data.repository.TableRepositoryImpl  Flow Fcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl  Inject Fcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl  Int Fcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl  List Fcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl  TableTimeConfig Fcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl  TableTimeConfigDao Fcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl   TableTimeConfigNodeDetaileEntity Fcom.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl  Flow ;com.example.todoschedule.data.repository.UserRepositoryImpl  Inject ;com.example.todoschedule.data.repository.UserRepositoryImpl  Int ;com.example.todoschedule.data.repository.UserRepositoryImpl  List ;com.example.todoschedule.data.repository.UserRepositoryImpl  Long ;com.example.todoschedule.data.repository.UserRepositoryImpl  Result ;com.example.todoschedule.data.repository.UserRepositoryImpl  SessionRepository ;com.example.todoschedule.data.repository.UserRepositoryImpl  String ;com.example.todoschedule.data.repository.UserRepositoryImpl  User ;com.example.todoschedule.data.repository.UserRepositoryImpl  UserDao ;com.example.todoschedule.data.repository.UserRepositoryImpl  Any "com.example.todoschedule.data.sync  Boolean "com.example.todoschedule.data.sync  CrdtKeyResolver "com.example.todoschedule.data.sync  DeviceIdManager "com.example.todoschedule.data.sync  Int "com.example.todoschedule.data.sync  Json "com.example.todoschedule.data.sync  List "com.example.todoschedule.data.sync  MutableStateFlow "com.example.todoschedule.data.sync  Mutex "com.example.todoschedule.data.sync  String "com.example.todoschedule.data.sync  SyncApi "com.example.todoschedule.data.sync  SyncApiImpl "com.example.todoschedule.data.sync  
SyncConstants "com.example.todoschedule.data.sync  SyncManager "com.example.todoschedule.data.sync  
SyncResult "com.example.todoschedule.data.sync  SyncService "com.example.todoschedule.data.sync  	SyncState "com.example.todoschedule.data.sync  
SyncWorker "com.example.todoschedule.data.sync  preferencesDataStore "com.example.todoschedule.data.sync  provideDelegate "com.example.todoschedule.data.sync  stringPreferencesKey "com.example.todoschedule.data.sync  AppDatabase 2com.example.todoschedule.data.sync.CrdtKeyResolver  CourseEntity 2com.example.todoschedule.data.sync.CrdtKeyResolver  CourseNodeEntity 2com.example.todoschedule.data.sync.CrdtKeyResolver  Inject 2com.example.todoschedule.data.sync.CrdtKeyResolver  Int 2com.example.todoschedule.data.sync.CrdtKeyResolver  OrdinaryScheduleEntity 2com.example.todoschedule.data.sync.CrdtKeyResolver  String 2com.example.todoschedule.data.sync.CrdtKeyResolver  SyncRepository 2com.example.todoschedule.data.sync.CrdtKeyResolver  TableEntity 2com.example.todoschedule.data.sync.CrdtKeyResolver  AppDatabase <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  CourseEntity <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  CourseNodeEntity <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  Inject <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  Int <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  OrdinaryScheduleEntity <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  String <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  SyncRepository <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  TableEntity <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  Boolean 2com.example.todoschedule.data.sync.DeviceIdManager  Context 2com.example.todoschedule.data.sync.DeviceIdManager  	DataStore 2com.example.todoschedule.data.sync.DeviceIdManager  Inject 2com.example.todoschedule.data.sync.DeviceIdManager  Mutex 2com.example.todoschedule.data.sync.DeviceIdManager  Preferences 2com.example.todoschedule.data.sync.DeviceIdManager  String 2com.example.todoschedule.data.sync.DeviceIdManager  preferencesDataStore 2com.example.todoschedule.data.sync.DeviceIdManager  provideDelegate 2com.example.todoschedule.data.sync.DeviceIdManager  stringPreferencesKey 2com.example.todoschedule.data.sync.DeviceIdManager  Boolean <com.example.todoschedule.data.sync.DeviceIdManager.Companion  Context <com.example.todoschedule.data.sync.DeviceIdManager.Companion  DEVICE_ID_PREFERENCES <com.example.todoschedule.data.sync.DeviceIdManager.Companion  	DataStore <com.example.todoschedule.data.sync.DeviceIdManager.Companion  Inject <com.example.todoschedule.data.sync.DeviceIdManager.Companion  Mutex <com.example.todoschedule.data.sync.DeviceIdManager.Companion  Preferences <com.example.todoschedule.data.sync.DeviceIdManager.Companion  String <com.example.todoschedule.data.sync.DeviceIdManager.Companion  getPREFERENCESDataStore <com.example.todoschedule.data.sync.DeviceIdManager.Companion  getPROVIDEDelegate <com.example.todoschedule.data.sync.DeviceIdManager.Companion  getPreferencesDataStore <com.example.todoschedule.data.sync.DeviceIdManager.Companion  getProvideDelegate <com.example.todoschedule.data.sync.DeviceIdManager.Companion  getSTRINGPreferencesKey <com.example.todoschedule.data.sync.DeviceIdManager.Companion  getStringPreferencesKey <com.example.todoschedule.data.sync.DeviceIdManager.Companion  preferencesDataStore <com.example.todoschedule.data.sync.DeviceIdManager.Companion  provideDelegate <com.example.todoschedule.data.sync.DeviceIdManager.Companion  stringPreferencesKey <com.example.todoschedule.data.sync.DeviceIdManager.Companion  Int *com.example.todoschedule.data.sync.SyncApi  List *com.example.todoschedule.data.sync.SyncApi  SyncMessageDto *com.example.todoschedule.data.sync.SyncApi  
SyncResult *com.example.todoschedule.data.sync.SyncApi  DeviceIdManager .com.example.todoschedule.data.sync.SyncApiImpl  Inject .com.example.todoschedule.data.sync.SyncApiImpl  Int .com.example.todoschedule.data.sync.SyncApiImpl  List .com.example.todoschedule.data.sync.SyncApiImpl  
RemoteSyncApi .com.example.todoschedule.data.sync.SyncApiImpl  SyncMessageDto .com.example.todoschedule.data.sync.SyncApiImpl  
SyncResult .com.example.todoschedule.data.sync.SyncApiImpl  
EntityType 0com.example.todoschedule.data.sync.SyncConstants  Int 0com.example.todoschedule.data.sync.SyncConstants  String 0com.example.todoschedule.data.sync.SyncConstants  
SyncStatus 0com.example.todoschedule.data.sync.SyncConstants  SYNC_BASE_PATH 4com.example.todoschedule.data.sync.SyncConstants.Api  
EntityType ;com.example.todoschedule.data.sync.SyncConstants.EntityType  String ;com.example.todoschedule.data.sync.SyncConstants.EntityType  Int ;com.example.todoschedule.data.sync.SyncConstants.SyncStatus  
SyncStatus ;com.example.todoschedule.data.sync.SyncConstants.SyncStatus  Any .com.example.todoschedule.data.sync.SyncManager  Boolean .com.example.todoschedule.data.sync.SyncManager  CoroutineScope .com.example.todoschedule.data.sync.SyncManager  CrdtKeyResolver .com.example.todoschedule.data.sync.SyncManager  DeviceIdManager .com.example.todoschedule.data.sync.SyncManager  Flow .com.example.todoschedule.data.sync.SyncManager  HybridLogicalClock .com.example.todoschedule.data.sync.SyncManager  Inject .com.example.todoschedule.data.sync.SyncManager  Int .com.example.todoschedule.data.sync.SyncManager  Json .com.example.todoschedule.data.sync.SyncManager  List .com.example.todoschedule.data.sync.SyncManager  MutableStateFlow .com.example.todoschedule.data.sync.SyncManager  	StateFlow .com.example.todoschedule.data.sync.SyncManager  String .com.example.todoschedule.data.sync.SyncManager  
SyncConstants .com.example.todoschedule.data.sync.SyncManager  SyncMessageDto .com.example.todoschedule.data.sync.SyncManager  SyncMessageEntity .com.example.todoschedule.data.sync.SyncManager  SyncRepository .com.example.todoschedule.data.sync.SyncManager  	SyncState .com.example.todoschedule.data.sync.SyncManager  SynkAdapterRegistry .com.example.todoschedule.data.sync.SyncManager  
_syncState .com.example.todoschedule.data.sync.SyncManager  invoke .com.example.todoschedule.data.sync.SyncManager  Any 8com.example.todoschedule.data.sync.SyncManager.Companion  Boolean 8com.example.todoschedule.data.sync.SyncManager.Companion  CoroutineScope 8com.example.todoschedule.data.sync.SyncManager.Companion  CrdtKeyResolver 8com.example.todoschedule.data.sync.SyncManager.Companion  DeviceIdManager 8com.example.todoschedule.data.sync.SyncManager.Companion  Flow 8com.example.todoschedule.data.sync.SyncManager.Companion  HybridLogicalClock 8com.example.todoschedule.data.sync.SyncManager.Companion  Inject 8com.example.todoschedule.data.sync.SyncManager.Companion  Int 8com.example.todoschedule.data.sync.SyncManager.Companion  Json 8com.example.todoschedule.data.sync.SyncManager.Companion  List 8com.example.todoschedule.data.sync.SyncManager.Companion  MutableStateFlow 8com.example.todoschedule.data.sync.SyncManager.Companion  	StateFlow 8com.example.todoschedule.data.sync.SyncManager.Companion  String 8com.example.todoschedule.data.sync.SyncManager.Companion  
SyncConstants 8com.example.todoschedule.data.sync.SyncManager.Companion  SyncMessageDto 8com.example.todoschedule.data.sync.SyncManager.Companion  SyncMessageEntity 8com.example.todoschedule.data.sync.SyncManager.Companion  SyncRepository 8com.example.todoschedule.data.sync.SyncManager.Companion  	SyncState 8com.example.todoschedule.data.sync.SyncManager.Companion  SynkAdapterRegistry 8com.example.todoschedule.data.sync.SyncManager.Companion  invoke 8com.example.todoschedule.data.sync.SyncManager.Companion  IDLE 8com.example.todoschedule.data.sync.SyncManager.SyncState  Boolean -com.example.todoschedule.data.sync.SyncResult  Int -com.example.todoschedule.data.sync.SyncResult  String -com.example.todoschedule.data.sync.SyncResult  Binder .com.example.todoschedule.data.sync.SyncService  Boolean .com.example.todoschedule.data.sync.SyncService  IBinder .com.example.todoschedule.data.sync.SyncService  Inject .com.example.todoschedule.data.sync.SyncService  Int .com.example.todoschedule.data.sync.SyncService  Intent .com.example.todoschedule.data.sync.SyncService  Job .com.example.todoschedule.data.sync.SyncService  SessionRepository .com.example.todoschedule.data.sync.SyncService  
SyncBinder .com.example.todoschedule.data.sync.SyncService  SyncManager .com.example.todoschedule.data.sync.SyncService  SyncRepository .com.example.todoschedule.data.sync.SyncService  SyncService .com.example.todoschedule.data.sync.SyncService  Timer .com.example.todoschedule.data.sync.SyncService  Binder 8com.example.todoschedule.data.sync.SyncService.Companion  Boolean 8com.example.todoschedule.data.sync.SyncService.Companion  IBinder 8com.example.todoschedule.data.sync.SyncService.Companion  Inject 8com.example.todoschedule.data.sync.SyncService.Companion  Int 8com.example.todoschedule.data.sync.SyncService.Companion  Intent 8com.example.todoschedule.data.sync.SyncService.Companion  Job 8com.example.todoschedule.data.sync.SyncService.Companion  SessionRepository 8com.example.todoschedule.data.sync.SyncService.Companion  SyncManager 8com.example.todoschedule.data.sync.SyncService.Companion  SyncRepository 8com.example.todoschedule.data.sync.SyncService.Companion  SyncService 8com.example.todoschedule.data.sync.SyncService.Companion  Timer 8com.example.todoschedule.data.sync.SyncService.Companion  SyncService 9com.example.todoschedule.data.sync.SyncService.SyncBinder  Assisted -com.example.todoschedule.data.sync.SyncWorker  AssistedInject -com.example.todoschedule.data.sync.SyncWorker  Boolean -com.example.todoschedule.data.sync.SyncWorker  Context -com.example.todoschedule.data.sync.SyncWorker  Result -com.example.todoschedule.data.sync.SyncWorker  SyncManager -com.example.todoschedule.data.sync.SyncWorker  SyncRepository -com.example.todoschedule.data.sync.SyncWorker  WorkerParameters -com.example.todoschedule.data.sync.SyncWorker  Assisted 7com.example.todoschedule.data.sync.SyncWorker.Companion  AssistedInject 7com.example.todoschedule.data.sync.SyncWorker.Companion  Boolean 7com.example.todoschedule.data.sync.SyncWorker.Companion  Context 7com.example.todoschedule.data.sync.SyncWorker.Companion  Result 7com.example.todoschedule.data.sync.SyncWorker.Companion  SyncManager 7com.example.todoschedule.data.sync.SyncWorker.Companion  SyncRepository 7com.example.todoschedule.data.sync.SyncWorker.Companion  WorkerParameters 7com.example.todoschedule.data.sync.SyncWorker.Companion  AbstractSynkAdapter *com.example.todoschedule.data.sync.adapter  Any *com.example.todoschedule.data.sync.adapter  Boolean *com.example.todoschedule.data.sync.adapter  
CourseAdapter *com.example.todoschedule.data.sync.adapter  CourseNodeAdapter *com.example.todoschedule.data.sync.adapter  Int *com.example.todoschedule.data.sync.adapter  Long *com.example.todoschedule.data.sync.adapter  Map *com.example.todoschedule.data.sync.adapter  OrdinaryScheduleAdapter *com.example.todoschedule.data.sync.adapter  String *com.example.todoschedule.data.sync.adapter  SynkAdapter *com.example.todoschedule.data.sync.adapter  SynkAdapterRegistry *com.example.todoschedule.data.sync.adapter  TableAdapter *com.example.todoschedule.data.sync.adapter  Any >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  Boolean >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  CourseEntity >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  CourseNodeEntity >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  Inject >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  Int >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  Long >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  Map >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  OrdinaryScheduleEntity >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  String >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  TableEntity >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter  Any 8com.example.todoschedule.data.sync.adapter.CourseAdapter  CourseEntity 8com.example.todoschedule.data.sync.adapter.CourseAdapter  Inject 8com.example.todoschedule.data.sync.adapter.CourseAdapter  Map 8com.example.todoschedule.data.sync.adapter.CourseAdapter  String 8com.example.todoschedule.data.sync.adapter.CourseAdapter  Any <com.example.todoschedule.data.sync.adapter.CourseNodeAdapter  CourseNodeEntity <com.example.todoschedule.data.sync.adapter.CourseNodeAdapter  Inject <com.example.todoschedule.data.sync.adapter.CourseNodeAdapter  Map <com.example.todoschedule.data.sync.adapter.CourseNodeAdapter  String <com.example.todoschedule.data.sync.adapter.CourseNodeAdapter  Any Bcom.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter  Inject Bcom.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter  Map Bcom.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter  OrdinaryScheduleEntity Bcom.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter  String Bcom.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter  Any 6com.example.todoschedule.data.sync.adapter.SynkAdapter  Map 6com.example.todoschedule.data.sync.adapter.SynkAdapter  String 6com.example.todoschedule.data.sync.adapter.SynkAdapter  
CourseAdapter >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  CourseNodeAdapter >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  Inject >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  OrdinaryScheduleAdapter >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  String >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  
SyncConstants >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  SynkAdapter >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  TableAdapter >com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry  Any 7com.example.todoschedule.data.sync.adapter.TableAdapter  Inject 7com.example.todoschedule.data.sync.adapter.TableAdapter  Map 7com.example.todoschedule.data.sync.adapter.TableAdapter  String 7com.example.todoschedule.data.sync.adapter.TableAdapter  TableEntity 7com.example.todoschedule.data.sync.adapter.TableAdapter  Boolean &com.example.todoschedule.data.sync.dto  DeviceRegistrationDto &com.example.todoschedule.data.sync.dto  Int &com.example.todoschedule.data.sync.dto  List &com.example.todoschedule.data.sync.dto  Long &com.example.todoschedule.data.sync.dto  String &com.example.todoschedule.data.sync.dto  SyncMessageDto &com.example.todoschedule.data.sync.dto  SyncMessagesDto &com.example.todoschedule.data.sync.dto  TimestampDto &com.example.todoschedule.data.sync.dto  UploadSyncResponseDto &com.example.todoschedule.data.sync.dto  com &com.example.todoschedule.data.sync.dto  Int <com.example.todoschedule.data.sync.dto.DeviceRegistrationDto  String <com.example.todoschedule.data.sync.dto.DeviceRegistrationDto  Int Fcom.example.todoschedule.data.sync.dto.DeviceRegistrationDto.Companion  String Fcom.example.todoschedule.data.sync.dto.DeviceRegistrationDto.Companion  Int 5com.example.todoschedule.data.sync.dto.SyncMessageDto  String 5com.example.todoschedule.data.sync.dto.SyncMessageDto  TimestampDto 5com.example.todoschedule.data.sync.dto.SyncMessageDto  Int ?com.example.todoschedule.data.sync.dto.SyncMessageDto.Companion  String ?com.example.todoschedule.data.sync.dto.SyncMessageDto.Companion  TimestampDto ?com.example.todoschedule.data.sync.dto.SyncMessageDto.Companion  List 6com.example.todoschedule.data.sync.dto.SyncMessagesDto  SyncMessageDto 6com.example.todoschedule.data.sync.dto.SyncMessagesDto  List @com.example.todoschedule.data.sync.dto.SyncMessagesDto.Companion  SyncMessageDto @com.example.todoschedule.data.sync.dto.SyncMessagesDto.Companion  Long 3com.example.todoschedule.data.sync.dto.TimestampDto  String 3com.example.todoschedule.data.sync.dto.TimestampDto  TimestampDto 3com.example.todoschedule.data.sync.dto.TimestampDto  com 3com.example.todoschedule.data.sync.dto.TimestampDto  Long =com.example.todoschedule.data.sync.dto.TimestampDto.Companion  String =com.example.todoschedule.data.sync.dto.TimestampDto.Companion  TimestampDto =com.example.todoschedule.data.sync.dto.TimestampDto.Companion  com =com.example.todoschedule.data.sync.dto.TimestampDto.Companion  Boolean <com.example.todoschedule.data.sync.dto.UploadSyncResponseDto  Int <com.example.todoschedule.data.sync.dto.UploadSyncResponseDto  List <com.example.todoschedule.data.sync.dto.UploadSyncResponseDto  String <com.example.todoschedule.data.sync.dto.UploadSyncResponseDto  Boolean Fcom.example.todoschedule.data.sync.dto.UploadSyncResponseDto.Companion  Int Fcom.example.todoschedule.data.sync.dto.UploadSyncResponseDto.Companion  List Fcom.example.todoschedule.data.sync.dto.UploadSyncResponseDto.Companion  String Fcom.example.todoschedule.data.sync.dto.UploadSyncResponseDto.Companion  Boolean )com.example.todoschedule.data.sync.entity  Int )com.example.todoschedule.data.sync.entity  Long )com.example.todoschedule.data.sync.entity  String )com.example.todoschedule.data.sync.entity  
SyncEntity )com.example.todoschedule.data.sync.entity  SyncMessageEntity )com.example.todoschedule.data.sync.entity  Boolean 4com.example.todoschedule.data.sync.entity.SyncEntity  Instant 4com.example.todoschedule.data.sync.entity.SyncEntity  Int 4com.example.todoschedule.data.sync.entity.SyncEntity  String 4com.example.todoschedule.data.sync.entity.SyncEntity  HybridLogicalClock ;com.example.todoschedule.data.sync.entity.SyncMessageEntity  Int ;com.example.todoschedule.data.sync.entity.SyncMessageEntity  Long ;com.example.todoschedule.data.sync.entity.SyncMessageEntity  
PrimaryKey ;com.example.todoschedule.data.sync.entity.SyncMessageEntity  String ;com.example.todoschedule.data.sync.entity.SyncMessageEntity  SyncMessageDto ;com.example.todoschedule.data.sync.entity.SyncMessageEntity  	Timestamp ;com.example.todoschedule.data.sync.entity.SyncMessageEntity  	AppModule com.example.todoschedule.di  DataStoreModule com.example.todoschedule.di  DatabaseModule com.example.todoschedule.di  
NetworkModule com.example.todoschedule.di  RepositoryModule com.example.todoschedule.di  SingletonComponent com.example.todoschedule.di  
SyncModule com.example.todoschedule.di  Provides %com.example.todoschedule.di.AppModule  	Singleton %com.example.todoschedule.di.AppModule  TodoApiService %com.example.todoschedule.di.AppModule  ApplicationContext +com.example.todoschedule.di.DataStoreModule  Context +com.example.todoschedule.di.DataStoreModule  	DataStore +com.example.todoschedule.di.DataStoreModule  Preferences +com.example.todoschedule.di.DataStoreModule  Provides +com.example.todoschedule.di.DataStoreModule  	Singleton +com.example.todoschedule.di.DataStoreModule  AppDatabase *com.example.todoschedule.di.DatabaseModule  ApplicationContext *com.example.todoschedule.di.DatabaseModule  Context *com.example.todoschedule.di.DatabaseModule  	CourseDao *com.example.todoschedule.di.DatabaseModule  GlobalSettingDao *com.example.todoschedule.di.DatabaseModule  OrdinaryScheduleDao *com.example.todoschedule.di.DatabaseModule  Provides *com.example.todoschedule.di.DatabaseModule  	Singleton *com.example.todoschedule.di.DatabaseModule  TableDao *com.example.todoschedule.di.DatabaseModule  TableTimeConfigDao *com.example.todoschedule.di.DatabaseModule  TimeSlotDao *com.example.todoschedule.di.DatabaseModule  UserDao *com.example.todoschedule.di.DatabaseModule  HttpLoggingInterceptor )com.example.todoschedule.di.NetworkModule  Interceptor )com.example.todoschedule.di.NetworkModule  Moshi )com.example.todoschedule.di.NetworkModule  OkHttpClient )com.example.todoschedule.di.NetworkModule  Provides )com.example.todoschedule.di.NetworkModule  Retrofit )com.example.todoschedule.di.NetworkModule  SessionRepository )com.example.todoschedule.di.NetworkModule  	Singleton )com.example.todoschedule.di.NetworkModule  SyncApi )com.example.todoschedule.di.NetworkModule  UserApiService )com.example.todoschedule.di.NetworkModule  Binds ,com.example.todoschedule.di.RepositoryModule  CourseRepository ,com.example.todoschedule.di.RepositoryModule  CourseRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  GlobalSettingRepository ,com.example.todoschedule.di.RepositoryModule  GlobalSettingRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  OrdinaryScheduleRepository ,com.example.todoschedule.di.RepositoryModule  OrdinaryScheduleRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  RemoteUserRepository ,com.example.todoschedule.di.RepositoryModule  RemoteUserRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  SessionRepository ,com.example.todoschedule.di.RepositoryModule  SessionRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  	Singleton ,com.example.todoschedule.di.RepositoryModule  TableRepository ,com.example.todoschedule.di.RepositoryModule  TableRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  TableTimeConfigRepository ,com.example.todoschedule.di.RepositoryModule  TableTimeConfigRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  UserRepository ,com.example.todoschedule.di.RepositoryModule  UserRepositoryImpl ,com.example.todoschedule.di.RepositoryModule  AppDatabase &com.example.todoschedule.di.SyncModule  ApplicationContext &com.example.todoschedule.di.SyncModule  Context &com.example.todoschedule.di.SyncModule  
CourseAdapter &com.example.todoschedule.di.SyncModule  CourseNodeAdapter &com.example.todoschedule.di.SyncModule  CrdtKeyResolver &com.example.todoschedule.di.SyncModule  DeviceIdManager &com.example.todoschedule.di.SyncModule  OrdinaryScheduleAdapter &com.example.todoschedule.di.SyncModule  Provides &com.example.todoschedule.di.SyncModule  
RemoteSyncApi &com.example.todoschedule.di.SyncModule  	Singleton &com.example.todoschedule.di.SyncModule  SyncApi &com.example.todoschedule.di.SyncModule  SyncManager &com.example.todoschedule.di.SyncModule  SyncRepository &com.example.todoschedule.di.SyncModule  SynkAdapterRegistry &com.example.todoschedule.di.SyncModule  TableAdapter &com.example.todoschedule.di.SyncModule  WorkManager &com.example.todoschedule.di.SyncModule  Boolean %com.example.todoschedule.domain.model  Course %com.example.todoschedule.domain.model  
CourseNode %com.example.todoschedule.domain.model  Float %com.example.todoschedule.domain.model  GlobalTableSetting %com.example.todoschedule.domain.model  Int %com.example.todoschedule.domain.model  List %com.example.todoschedule.domain.model  Long %com.example.todoschedule.domain.model  OrdinarySchedule %com.example.todoschedule.domain.model  String %com.example.todoschedule.domain.model  Table %com.example.todoschedule.domain.model  TableTimeConfig %com.example.todoschedule.domain.model  TableTimeConfigNode %com.example.todoschedule.domain.model  
ThemeSettings %com.example.todoschedule.domain.model  TimeSlot %com.example.todoschedule.domain.model  User %com.example.todoschedule.domain.model  ColorSchemeEnum ,com.example.todoschedule.domain.model.Course  
CourseNode ,com.example.todoschedule.domain.model.Course  Float ,com.example.todoschedule.domain.model.Course  Int ,com.example.todoschedule.domain.model.Course  List ,com.example.todoschedule.domain.model.Course  String ,com.example.todoschedule.domain.model.Course  copy ,com.example.todoschedule.domain.model.Course  nodes ,com.example.todoschedule.domain.model.Course  Boolean 0com.example.todoschedule.domain.model.CourseNode  Int 0com.example.todoschedule.domain.model.CourseNode  String 0com.example.todoschedule.domain.model.CourseNode  isInWeek 0com.example.todoschedule.domain.model.CourseNode  Boolean 8com.example.todoschedule.domain.model.GlobalTableSetting  Int 8com.example.todoschedule.domain.model.GlobalTableSetting  List 8com.example.todoschedule.domain.model.GlobalTableSetting  Boolean 6com.example.todoschedule.domain.model.OrdinarySchedule  ColorSchemeEnum 6com.example.todoschedule.domain.model.OrdinarySchedule  Int 6com.example.todoschedule.domain.model.OrdinarySchedule  List 6com.example.todoschedule.domain.model.OrdinarySchedule  ScheduleStatus 6com.example.todoschedule.domain.model.OrdinarySchedule  String 6com.example.todoschedule.domain.model.OrdinarySchedule  TimeSlot 6com.example.todoschedule.domain.model.OrdinarySchedule  color 6com.example.todoschedule.domain.model.OrdinarySchedule  equals 6com.example.todoschedule.domain.model.OrdinarySchedule  location 6com.example.todoschedule.domain.model.OrdinarySchedule  	timeSlots 6com.example.todoschedule.domain.model.OrdinarySchedule  title 6com.example.todoschedule.domain.model.OrdinarySchedule  Boolean +com.example.todoschedule.domain.model.Table  Int +com.example.todoschedule.domain.model.Table  	LocalDate +com.example.todoschedule.domain.model.Table  String +com.example.todoschedule.domain.model.Table  equals +com.example.todoschedule.domain.model.Table  id +com.example.todoschedule.domain.model.Table  	startDate +com.example.todoschedule.domain.model.Table  userId +com.example.todoschedule.domain.model.Table  Boolean 5com.example.todoschedule.domain.model.TableTimeConfig  Int 5com.example.todoschedule.domain.model.TableTimeConfig  List 5com.example.todoschedule.domain.model.TableTimeConfig  String 5com.example.todoschedule.domain.model.TableTimeConfig  TableTimeConfigNode 5com.example.todoschedule.domain.model.TableTimeConfig  equals 5com.example.todoschedule.domain.model.TableTimeConfig  id 5com.example.todoschedule.domain.model.TableTimeConfig  Int 9com.example.todoschedule.domain.model.TableTimeConfigNode  	LocalTime 9com.example.todoschedule.domain.model.TableTimeConfigNode  String 9com.example.todoschedule.domain.model.TableTimeConfigNode  Boolean 3com.example.todoschedule.domain.model.ThemeSettings  Boolean .com.example.todoschedule.domain.model.TimeSlot  ColorSchemeEnum .com.example.todoschedule.domain.model.TimeSlot  Int .com.example.todoschedule.domain.model.TimeSlot  Long .com.example.todoschedule.domain.model.TimeSlot  ReminderType .com.example.todoschedule.domain.model.TimeSlot  ScheduleType .com.example.todoschedule.domain.model.TimeSlot  String .com.example.todoschedule.domain.model.TimeSlot  copy .com.example.todoschedule.domain.model.TimeSlot  endTime .com.example.todoschedule.domain.model.TimeSlot  head .com.example.todoschedule.domain.model.TimeSlot  	startTime .com.example.todoschedule.domain.model.TimeSlot  Instant *com.example.todoschedule.domain.model.User  Int *com.example.todoschedule.domain.model.User  String *com.example.todoschedule.domain.model.User  Boolean *com.example.todoschedule.domain.repository  CourseRepository *com.example.todoschedule.domain.repository  GlobalSettingRepository *com.example.todoschedule.domain.repository  Int *com.example.todoschedule.domain.repository  List *com.example.todoschedule.domain.repository  Long *com.example.todoschedule.domain.repository  OrdinaryScheduleRepository *com.example.todoschedule.domain.repository  RemoteUserRepository *com.example.todoschedule.domain.repository  Result *com.example.todoschedule.domain.repository  SessionRepository *com.example.todoschedule.domain.repository  String *com.example.todoschedule.domain.repository  TableRepository *com.example.todoschedule.domain.repository  TableTimeConfigRepository *com.example.todoschedule.domain.repository  UserRepository *com.example.todoschedule.domain.repository  com *com.example.todoschedule.domain.repository  Course ;com.example.todoschedule.domain.repository.CourseRepository  
CourseNode ;com.example.todoschedule.domain.repository.CourseRepository  Flow ;com.example.todoschedule.domain.repository.CourseRepository  Int ;com.example.todoschedule.domain.repository.CourseRepository  List ;com.example.todoschedule.domain.repository.CourseRepository  Long ;com.example.todoschedule.domain.repository.CourseRepository  getCoursesByTableId ;com.example.todoschedule.domain.repository.CourseRepository  Flow Bcom.example.todoschedule.domain.repository.GlobalSettingRepository  GlobalTableSetting Bcom.example.todoschedule.domain.repository.GlobalSettingRepository  Int Bcom.example.todoschedule.domain.repository.GlobalSettingRepository  List Bcom.example.todoschedule.domain.repository.GlobalSettingRepository  Long Bcom.example.todoschedule.domain.repository.GlobalSettingRepository  getDefaultTableIds Bcom.example.todoschedule.domain.repository.GlobalSettingRepository  Flow Ecom.example.todoschedule.domain.repository.OrdinaryScheduleRepository  Int Ecom.example.todoschedule.domain.repository.OrdinaryScheduleRepository  List Ecom.example.todoschedule.domain.repository.OrdinaryScheduleRepository  Long Ecom.example.todoschedule.domain.repository.OrdinaryScheduleRepository  com Ecom.example.todoschedule.domain.repository.OrdinaryScheduleRepository  Result ?com.example.todoschedule.domain.repository.RemoteUserRepository  String ?com.example.todoschedule.domain.repository.RemoteUserRepository  User ?com.example.todoschedule.domain.repository.RemoteUserRepository  Boolean <com.example.todoschedule.domain.repository.SessionRepository  Flow <com.example.todoschedule.domain.repository.SessionRepository  Long <com.example.todoschedule.domain.repository.SessionRepository  	StateFlow <com.example.todoschedule.domain.repository.SessionRepository  String <com.example.todoschedule.domain.repository.SessionRepository  
ThemeSettings <com.example.todoschedule.domain.repository.SessionRepository  currentUserIdFlow <com.example.todoschedule.domain.repository.SessionRepository  themeSettingsFlow <com.example.todoschedule.domain.repository.SessionRepository  Flow :com.example.todoschedule.domain.repository.TableRepository  Int :com.example.todoschedule.domain.repository.TableRepository  List :com.example.todoschedule.domain.repository.TableRepository  Long :com.example.todoschedule.domain.repository.TableRepository  Table :com.example.todoschedule.domain.repository.TableRepository  getTableById :com.example.todoschedule.domain.repository.TableRepository  Flow Dcom.example.todoschedule.domain.repository.TableTimeConfigRepository  Int Dcom.example.todoschedule.domain.repository.TableTimeConfigRepository  TableTimeConfig Dcom.example.todoschedule.domain.repository.TableTimeConfigRepository  Flow 9com.example.todoschedule.domain.repository.UserRepository  Int 9com.example.todoschedule.domain.repository.UserRepository  List 9com.example.todoschedule.domain.repository.UserRepository  Long 9com.example.todoschedule.domain.repository.UserRepository  Result 9com.example.todoschedule.domain.repository.UserRepository  String 9com.example.todoschedule.domain.repository.UserRepository  User 9com.example.todoschedule.domain.repository.UserRepository  Boolean -com.example.todoschedule.domain.use_case.auth  ClearLoginSessionUseCase -com.example.todoschedule.domain.use_case.auth  GetLoginUserIdFlowUseCase -com.example.todoschedule.domain.use_case.auth  HashPasswordUseCase -com.example.todoschedule.domain.use_case.auth  LoginUserUseCase -com.example.todoschedule.domain.use_case.auth  Long -com.example.todoschedule.domain.use_case.auth  RegisterUserUseCase -com.example.todoschedule.domain.use_case.auth  Result -com.example.todoschedule.domain.use_case.auth  SaveLoginSessionUseCase -com.example.todoschedule.domain.use_case.auth  String -com.example.todoschedule.domain.use_case.auth  VerifyPasswordUseCase -com.example.todoschedule.domain.use_case.auth  Inject Fcom.example.todoschedule.domain.use_case.auth.ClearLoginSessionUseCase  SessionRepository Fcom.example.todoschedule.domain.use_case.auth.ClearLoginSessionUseCase  Inject Gcom.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase  Long Gcom.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase  SessionRepository Gcom.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase  	StateFlow Gcom.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase  invoke Gcom.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase  Inject Acom.example.todoschedule.domain.use_case.auth.HashPasswordUseCase  String Acom.example.todoschedule.domain.use_case.auth.HashPasswordUseCase  Boolean >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  Inject >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  RemoteUserRepository >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  Result >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  String >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  User >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  UserRepository >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  VerifyPasswordUseCase >com.example.todoschedule.domain.use_case.auth.LoginUserUseCase  Boolean Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  HashPasswordUseCase Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  Inject Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  Long Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  RemoteUserRepository Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  Result Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  String Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  UserRepository Acom.example.todoschedule.domain.use_case.auth.RegisterUserUseCase  Inject Ecom.example.todoschedule.domain.use_case.auth.SaveLoginSessionUseCase  Long Ecom.example.todoschedule.domain.use_case.auth.SaveLoginSessionUseCase  SessionRepository Ecom.example.todoschedule.domain.use_case.auth.SaveLoginSessionUseCase  String Ecom.example.todoschedule.domain.use_case.auth.SaveLoginSessionUseCase  UserRepository Ecom.example.todoschedule.domain.use_case.auth.SaveLoginSessionUseCase  Boolean Ccom.example.todoschedule.domain.use_case.auth.VerifyPasswordUseCase  Inject Ccom.example.todoschedule.domain.use_case.auth.VerifyPasswordUseCase  String Ccom.example.todoschedule.domain.use_case.auth.VerifyPasswordUseCase  AddOrdinaryScheduleUseCase :com.example.todoschedule.domain.use_case.ordinary_schedule  DeleteOrdinaryScheduleUseCase :com.example.todoschedule.domain.use_case.ordinary_schedule  GetOrdinaryScheduleByIdUseCase :com.example.todoschedule.domain.use_case.ordinary_schedule  GetOrdinarySchedulesUseCase :com.example.todoschedule.domain.use_case.ordinary_schedule  Int :com.example.todoschedule.domain.use_case.ordinary_schedule  List :com.example.todoschedule.domain.use_case.ordinary_schedule  Long :com.example.todoschedule.domain.use_case.ordinary_schedule  UpdateOrdinaryScheduleUseCase :com.example.todoschedule.domain.use_case.ordinary_schedule  Inject Ucom.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase  Long Ucom.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase  OrdinarySchedule Ucom.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase  OrdinaryScheduleRepository Ucom.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase  Inject Xcom.example.todoschedule.domain.use_case.ordinary_schedule.DeleteOrdinaryScheduleUseCase  OrdinarySchedule Xcom.example.todoschedule.domain.use_case.ordinary_schedule.DeleteOrdinaryScheduleUseCase  OrdinaryScheduleRepository Xcom.example.todoschedule.domain.use_case.ordinary_schedule.DeleteOrdinaryScheduleUseCase  Flow Ycom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase  Inject Ycom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase  Int Ycom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase  OrdinarySchedule Ycom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase  OrdinaryScheduleRepository Ycom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase  invoke Ycom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase  Flow Vcom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase  Inject Vcom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase  Int Vcom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase  List Vcom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase  OrdinarySchedule Vcom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase  OrdinaryScheduleRepository Vcom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase  invoke Vcom.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase  Inject Xcom.example.todoschedule.domain.use_case.ordinary_schedule.UpdateOrdinaryScheduleUseCase  OrdinarySchedule Xcom.example.todoschedule.domain.use_case.ordinary_schedule.UpdateOrdinaryScheduleUseCase  OrdinaryScheduleRepository Xcom.example.todoschedule.domain.use_case.ordinary_schedule.UpdateOrdinaryScheduleUseCase   GetDefaultTableTimeConfigUseCase :com.example.todoschedule.domain.use_case.table_time_config  Int :com.example.todoschedule.domain.use_case.table_time_config  Flow [com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase  Inject [com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase  Int [com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase  TableTimeConfig [com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase  TableTimeConfigRepository [com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase  invoke [com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase  Boolean %com.example.todoschedule.domain.utils  CalendarSyncManager %com.example.todoschedule.domain.utils  
CalendarUtils %com.example.todoschedule.domain.utils  Int %com.example.todoschedule.domain.utils  List %com.example.todoschedule.domain.utils  Long %com.example.todoschedule.domain.utils  Map %com.example.todoschedule.domain.utils  String %com.example.todoschedule.domain.utils  ApplicationContext 9com.example.todoschedule.domain.utils.CalendarSyncManager  Boolean 9com.example.todoschedule.domain.utils.CalendarSyncManager  ContentResolver 9com.example.todoschedule.domain.utils.CalendarSyncManager  Context 9com.example.todoschedule.domain.utils.CalendarSyncManager  Inject 9com.example.todoschedule.domain.utils.CalendarSyncManager  Int 9com.example.todoschedule.domain.utils.CalendarSyncManager  List 9com.example.todoschedule.domain.utils.CalendarSyncManager  Long 9com.example.todoschedule.domain.utils.CalendarSyncManager  Map 9com.example.todoschedule.domain.utils.CalendarSyncManager  String 9com.example.todoschedule.domain.utils.CalendarSyncManager  TaskItemUiModel 9com.example.todoschedule.domain.utils.CalendarSyncManager  context 9com.example.todoschedule.domain.utils.CalendarSyncManager  Int 3com.example.todoschedule.domain.utils.CalendarUtils  List 3com.example.todoschedule.domain.utils.CalendarUtils  	LocalDate 3com.example.todoschedule.domain.utils.CalendarUtils  String 3com.example.todoschedule.domain.utils.CalendarUtils  getWeekDates 3com.example.todoschedule.domain.utils.CalendarUtils  NavGraph #com.example.todoschedule.navigation  MainActivity com.example.todoschedule.ui  Bundle (com.example.todoschedule.ui.MainActivity  Inject (com.example.todoschedule.ui.MainActivity  SyncRepository (com.example.todoschedule.ui.MainActivity  Bundle 2com.example.todoschedule.ui.MainActivity.Companion  Inject 2com.example.todoschedule.ui.MainActivity.Companion  SyncRepository 2com.example.todoschedule.ui.MainActivity.Companion  Boolean  com.example.todoschedule.ui.auth  LoginScreen  com.example.todoschedule.ui.auth  LoginScreenPreview  com.example.todoschedule.ui.auth  LoginUiState  com.example.todoschedule.ui.auth  LoginViewModel  com.example.todoschedule.ui.auth  MutableStateFlow  com.example.todoschedule.ui.auth  RegisterScreen  com.example.todoschedule.ui.auth  RegisterScreenPreview  com.example.todoschedule.ui.auth  RegisterUiState  com.example.todoschedule.ui.auth  RegisterViewModel  com.example.todoschedule.ui.auth  String  com.example.todoschedule.ui.auth  Unit  com.example.todoschedule.ui.auth  asStateFlow  com.example.todoschedule.ui.auth  Boolean -com.example.todoschedule.ui.auth.LoginUiState  String -com.example.todoschedule.ui.auth.LoginUiState  Inject /com.example.todoschedule.ui.auth.LoginViewModel  LoginUiState /com.example.todoschedule.ui.auth.LoginViewModel  LoginUserUseCase /com.example.todoschedule.ui.auth.LoginViewModel  MutableStateFlow /com.example.todoschedule.ui.auth.LoginViewModel  SaveLoginSessionUseCase /com.example.todoschedule.ui.auth.LoginViewModel  	StateFlow /com.example.todoschedule.ui.auth.LoginViewModel  String /com.example.todoschedule.ui.auth.LoginViewModel  SyncManager /com.example.todoschedule.ui.auth.LoginViewModel  _uiState /com.example.todoschedule.ui.auth.LoginViewModel  asStateFlow /com.example.todoschedule.ui.auth.LoginViewModel  getASStateFlow /com.example.todoschedule.ui.auth.LoginViewModel  getAsStateFlow /com.example.todoschedule.ui.auth.LoginViewModel  Boolean 0com.example.todoschedule.ui.auth.RegisterUiState  String 0com.example.todoschedule.ui.auth.RegisterUiState  Inject 2com.example.todoschedule.ui.auth.RegisterViewModel  MutableStateFlow 2com.example.todoschedule.ui.auth.RegisterViewModel  RegisterUiState 2com.example.todoschedule.ui.auth.RegisterViewModel  RegisterUserUseCase 2com.example.todoschedule.ui.auth.RegisterViewModel  	StateFlow 2com.example.todoschedule.ui.auth.RegisterViewModel  String 2com.example.todoschedule.ui.auth.RegisterViewModel  _uiState 2com.example.todoschedule.ui.auth.RegisterViewModel  asStateFlow 2com.example.todoschedule.ui.auth.RegisterViewModel  getASStateFlow 2com.example.todoschedule.ui.auth.RegisterViewModel  getAsStateFlow 2com.example.todoschedule.ui.auth.RegisterViewModel  Boolean &com.example.todoschedule.ui.components  CalendarPermissionTextProvider &com.example.todoschedule.ui.components  PermissionDialog &com.example.todoschedule.ui.components  PermissionTextProvider &com.example.todoschedule.ui.components  String &com.example.todoschedule.ui.components  Unit &com.example.todoschedule.ui.components  androidx &com.example.todoschedule.ui.components  String Ecom.example.todoschedule.ui.components.CalendarPermissionTextProvider  String =com.example.todoschedule.ui.components.PermissionTextProvider  AddCourseScreen &com.example.todoschedule.ui.course.add  AddCourseViewModel &com.example.todoschedule.ui.course.add  AppConstants &com.example.todoschedule.ui.course.add  Boolean &com.example.todoschedule.ui.course.add  CourseNodeDialog &com.example.todoschedule.ui.course.add  CourseNodeItem &com.example.todoschedule.ui.course.add  CourseNodeUiState &com.example.todoschedule.ui.course.add  ExperimentalMaterial3Api &com.example.todoschedule.ui.course.add  Int &com.example.todoschedule.ui.course.add  List &com.example.todoschedule.ui.course.add  MutableStateFlow &com.example.todoschedule.ui.course.add  OptIn &com.example.todoschedule.ui.course.add  	SaveState &com.example.todoschedule.ui.course.add  String &com.example.todoschedule.ui.course.add  Unit &com.example.todoschedule.ui.course.add  asStateFlow &com.example.todoschedule.ui.course.add  	emptyList &com.example.todoschedule.ui.course.add  toDomain &com.example.todoschedule.ui.course.add  AppConstants 9com.example.todoschedule.ui.course.add.AddCourseViewModel  Boolean 9com.example.todoschedule.ui.course.add.AddCourseViewModel  ColorSchemeEnum 9com.example.todoschedule.ui.course.add.AddCourseViewModel  CourseNodeUiState 9com.example.todoschedule.ui.course.add.AddCourseViewModel  CourseRepository 9com.example.todoschedule.ui.course.add.AddCourseViewModel  GetLoginUserIdFlowUseCase 9com.example.todoschedule.ui.course.add.AddCourseViewModel  Inject 9com.example.todoschedule.ui.course.add.AddCourseViewModel  Int 9com.example.todoschedule.ui.course.add.AddCourseViewModel  List 9com.example.todoschedule.ui.course.add.AddCourseViewModel  MutableStateFlow 9com.example.todoschedule.ui.course.add.AddCourseViewModel  	SaveState 9com.example.todoschedule.ui.course.add.AddCourseViewModel  	StateFlow 9com.example.todoschedule.ui.course.add.AddCourseViewModel  String 9com.example.todoschedule.ui.course.add.AddCourseViewModel  TableRepository 9com.example.todoschedule.ui.course.add.AddCourseViewModel  _color 9com.example.todoschedule.ui.course.add.AddCourseViewModel  _courseCode 9com.example.todoschedule.ui.course.add.AddCourseViewModel  _courseName 9com.example.todoschedule.ui.course.add.AddCourseViewModel  _courseNodes 9com.example.todoschedule.ui.course.add.AddCourseViewModel  _credit 9com.example.todoschedule.ui.course.add.AddCourseViewModel  _room 9com.example.todoschedule.ui.course.add.AddCourseViewModel  
_saveState 9com.example.todoschedule.ui.course.add.AddCourseViewModel  _teacher 9com.example.todoschedule.ui.course.add.AddCourseViewModel  asStateFlow 9com.example.todoschedule.ui.course.add.AddCourseViewModel  	emptyList 9com.example.todoschedule.ui.course.add.AddCourseViewModel  getASStateFlow 9com.example.todoschedule.ui.course.add.AddCourseViewModel  getAsStateFlow 9com.example.todoschedule.ui.course.add.AddCourseViewModel  getEMPTYList 9com.example.todoschedule.ui.course.add.AddCourseViewModel  getEmptyList 9com.example.todoschedule.ui.course.add.AddCourseViewModel  Int 8com.example.todoschedule.ui.course.add.CourseNodeUiState  String 8com.example.todoschedule.ui.course.add.CourseNodeUiState  Idle 0com.example.todoschedule.ui.course.add.SaveState  Int 0com.example.todoschedule.ui.course.add.SaveState  	SaveState 0com.example.todoschedule.ui.course.add.SaveState  String 0com.example.todoschedule.ui.course.add.SaveState  String 6com.example.todoschedule.ui.course.add.SaveState.Error  Int 8com.example.todoschedule.ui.course.add.SaveState.Success  Boolean )com.example.todoschedule.ui.course.detail  CourseDetail )com.example.todoschedule.ui.course.detail  CourseDetailModel )com.example.todoschedule.ui.course.detail  CourseDetailScreen )com.example.todoschedule.ui.course.detail  CourseDetailUiState )com.example.todoschedule.ui.course.detail  CourseDetailViewModel )com.example.todoschedule.ui.course.detail  CourseNodeDetailModel )com.example.todoschedule.ui.course.detail  ExperimentalMaterial3Api )com.example.todoschedule.ui.course.detail  Float )com.example.todoschedule.ui.course.detail  Int )com.example.todoschedule.ui.course.detail  List )com.example.todoschedule.ui.course.detail  MutableStateFlow )com.example.todoschedule.ui.course.detail  NodeCard )com.example.todoschedule.ui.course.detail  OptIn )com.example.todoschedule.ui.course.detail  String )com.example.todoschedule.ui.course.detail  Unit )com.example.todoschedule.ui.course.detail  isColorDark )com.example.todoschedule.ui.course.detail  ColorSchemeEnum ;com.example.todoschedule.ui.course.detail.CourseDetailModel  CourseNodeDetailModel ;com.example.todoschedule.ui.course.detail.CourseDetailModel  Float ;com.example.todoschedule.ui.course.detail.CourseDetailModel  Int ;com.example.todoschedule.ui.course.detail.CourseDetailModel  List ;com.example.todoschedule.ui.course.detail.CourseDetailModel  String ;com.example.todoschedule.ui.course.detail.CourseDetailModel  CourseDetailModel =com.example.todoschedule.ui.course.detail.CourseDetailUiState  CourseDetailUiState =com.example.todoschedule.ui.course.detail.CourseDetailUiState  Loading =com.example.todoschedule.ui.course.detail.CourseDetailUiState  String =com.example.todoschedule.ui.course.detail.CourseDetailUiState  String Ccom.example.todoschedule.ui.course.detail.CourseDetailUiState.Error  CourseDetailModel Ecom.example.todoschedule.ui.course.detail.CourseDetailUiState.Success  Course ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  CourseDetailModel ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  CourseDetailUiState ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  
CourseNode ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  CourseNodeDetailModel ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  CourseRepository ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  Inject ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  Int ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  MutableStateFlow ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  	StateFlow ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  _uiState ?com.example.todoschedule.ui.course.detail.CourseDetailViewModel  Int ?com.example.todoschedule.ui.course.detail.CourseNodeDetailModel  String ?com.example.todoschedule.ui.course.detail.CourseNodeDetailModel  AppConstants 'com.example.todoschedule.ui.course.edit  Boolean 'com.example.todoschedule.ui.course.edit  Channel 'com.example.todoschedule.ui.course.edit  CourseNodeDialog 'com.example.todoschedule.ui.course.edit  CourseNodeItem 'com.example.todoschedule.ui.course.edit  EditCourseEvent 'com.example.todoschedule.ui.course.edit  EditCourseScreen 'com.example.todoschedule.ui.course.edit  EditCourseUiState 'com.example.todoschedule.ui.course.edit  EditCourseViewModel 'com.example.todoschedule.ui.course.edit  ExperimentalMaterial3Api 'com.example.todoschedule.ui.course.edit  Int 'com.example.todoschedule.ui.course.edit  List 'com.example.todoschedule.ui.course.edit  MutableStateFlow 'com.example.todoschedule.ui.course.edit  OptIn 'com.example.todoschedule.ui.course.edit  String 'com.example.todoschedule.ui.course.edit  Unit 'com.example.todoschedule.ui.course.edit  asStateFlow 'com.example.todoschedule.ui.course.edit  	emptyList 'com.example.todoschedule.ui.course.edit  
receiveAsFlow 'com.example.todoschedule.ui.course.edit  EditCourseEvent 7com.example.todoschedule.ui.course.edit.EditCourseEvent  String 7com.example.todoschedule.ui.course.edit.EditCourseEvent  String Acom.example.todoschedule.ui.course.edit.EditCourseEvent.ShowError  Loading 9com.example.todoschedule.ui.course.edit.EditCourseUiState  AppConstants ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  Boolean ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  Channel ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  ColorSchemeEnum ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  
CourseNode ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  CourseNodeUiState ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  CourseRepository ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  EditCourseEvent ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  EditCourseUiState ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  Inject ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  Int ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  List ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  MutableStateFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  	StateFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  String ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _color ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _courseCode ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _courseName ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _courseNodes ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _credit ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _events ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _room ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _teacher ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  _uiState ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  asStateFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  	emptyList ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  getASStateFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  getAsStateFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  getEMPTYList ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  getEmptyList ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  getRECEIVEAsFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  getReceiveAsFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  invoke ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  
receiveAsFlow ;com.example.todoschedule.ui.course.edit.EditCourseViewModel  Boolean 'com.example.todoschedule.ui.course.load  Char 'com.example.todoschedule.ui.course.load  ExperimentalFoundationApi 'com.example.todoschedule.ui.course.load  Int 'com.example.todoschedule.ui.course.load  List 'com.example.todoschedule.ui.course.load  Map 'com.example.todoschedule.ui.course.load  MutableStateFlow 'com.example.todoschedule.ui.course.load  OptIn 'com.example.todoschedule.ui.course.load  SaveCourseState 'com.example.todoschedule.ui.course.load  School 'com.example.todoschedule.ui.course.load  SchoolListItemV2 'com.example.todoschedule.ui.course.load  SchoolSelectorScreen 'com.example.todoschedule.ui.course.load  SchoolViewModel 'com.example.todoschedule.ui.course.load  	SearchBar 'com.example.todoschedule.ui.course.load  
SectionHeader 'com.example.todoschedule.ui.course.load  SharingStarted 'com.example.todoschedule.ui.course.load  	StateFlow 'com.example.todoschedule.ui.course.load  String 'com.example.todoschedule.ui.course.load  Unit 'com.example.todoschedule.ui.course.load  
WebViewScreen 'com.example.todoschedule.ui.course.load  WebViewScreenViewModel 'com.example.todoschedule.ui.course.load  _rawSchools 'com.example.todoschedule.ui.course.load  asStateFlow 'com.example.todoschedule.ui.course.load  combine 'com.example.todoschedule.ui.course.load  emptyMap 'com.example.todoschedule.ui.course.load  flow 'com.example.todoschedule.ui.course.load  getPinyinInitial 'com.example.todoschedule.ui.course.load  listOf 'com.example.todoschedule.ui.course.load  rememberWebView 'com.example.todoschedule.ui.course.load  saveParserResult 'com.example.todoschedule.ui.course.load  stateIn 'com.example.todoschedule.ui.course.load  viewModelScope 'com.example.todoschedule.ui.course.load  Idle 7com.example.todoschedule.ui.course.load.SaveCourseState  Int 7com.example.todoschedule.ui.course.load.SaveCourseState  SaveCourseState 7com.example.todoschedule.ui.course.load.SaveCourseState  String 7com.example.todoschedule.ui.course.load.SaveCourseState  String =com.example.todoschedule.ui.course.load.SaveCourseState.Error  Int ?com.example.todoschedule.ui.course.load.SaveCourseState.Success  Char .com.example.todoschedule.ui.course.load.School  String .com.example.todoschedule.ui.course.load.School  Char 7com.example.todoschedule.ui.course.load.SchoolViewModel  List 7com.example.todoschedule.ui.course.load.SchoolViewModel  Map 7com.example.todoschedule.ui.course.load.SchoolViewModel  MutableStateFlow 7com.example.todoschedule.ui.course.load.SchoolViewModel  School 7com.example.todoschedule.ui.course.load.SchoolViewModel  SharingStarted 7com.example.todoschedule.ui.course.load.SchoolViewModel  	StateFlow 7com.example.todoschedule.ui.course.load.SchoolViewModel  String 7com.example.todoschedule.ui.course.load.SchoolViewModel  _rawSchools 7com.example.todoschedule.ui.course.load.SchoolViewModel  _searchQuery 7com.example.todoschedule.ui.course.load.SchoolViewModel  combine 7com.example.todoschedule.ui.course.load.SchoolViewModel  emptyMap 7com.example.todoschedule.ui.course.load.SchoolViewModel  filterAndGroupSchools 7com.example.todoschedule.ui.course.load.SchoolViewModel  flow 7com.example.todoschedule.ui.course.load.SchoolViewModel  
getCOMBINE 7com.example.todoschedule.ui.course.load.SchoolViewModel  
getCombine 7com.example.todoschedule.ui.course.load.SchoolViewModel  getEMPTYMap 7com.example.todoschedule.ui.course.load.SchoolViewModel  getEmptyMap 7com.example.todoschedule.ui.course.load.SchoolViewModel  getFLOW 7com.example.todoschedule.ui.course.load.SchoolViewModel  getFlow 7com.example.todoschedule.ui.course.load.SchoolViewModel  	getLISTOf 7com.example.todoschedule.ui.course.load.SchoolViewModel  	getListOf 7com.example.todoschedule.ui.course.load.SchoolViewModel  
getSTATEIn 7com.example.todoschedule.ui.course.load.SchoolViewModel  
getStateIn 7com.example.todoschedule.ui.course.load.SchoolViewModel  getVIEWModelScope 7com.example.todoschedule.ui.course.load.SchoolViewModel  getViewModelScope 7com.example.todoschedule.ui.course.load.SchoolViewModel  listOf 7com.example.todoschedule.ui.course.load.SchoolViewModel  stateIn 7com.example.todoschedule.ui.course.load.SchoolViewModel  viewModelScope 7com.example.todoschedule.ui.course.load.SchoolViewModel  Course >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  CourseRepository >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  GetLoginUserIdFlowUseCase >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  GlobalSettingRepository >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  Inject >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  Int >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  List >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  MutableStateFlow >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  SaveCourseState >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  	StateFlow >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  TableRepository >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  
_saveState >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  asStateFlow >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  getASStateFlow >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  getAsStateFlow >com.example.todoschedule.ui.course.load.WebViewScreenViewModel  Boolean  com.example.todoschedule.ui.home  FeatureCard  com.example.todoschedule.ui.home  HomeCourseItem  com.example.todoschedule.ui.home  
HomeScreen  com.example.todoschedule.ui.home  HomeTodoItem  com.example.todoschedule.ui.home  
HomeViewModel  com.example.todoschedule.ui.home  Int  com.example.todoschedule.ui.home  List  com.example.todoschedule.ui.home  MutableStateFlow  com.example.todoschedule.ui.home  String  com.example.todoschedule.ui.home  TodoListItem  com.example.todoschedule.ui.home  Unit  com.example.todoschedule.ui.home  asStateFlow  com.example.todoschedule.ui.home  	emptyList  com.example.todoschedule.ui.home  getValue  com.example.todoschedule.ui.home  mutableStateOf  com.example.todoschedule.ui.home  provideDelegate  com.example.todoschedule.ui.home  setValue  com.example.todoschedule.ui.home  Int /com.example.todoschedule.ui.home.HomeCourseItem  	LocalTime /com.example.todoschedule.ui.home.HomeCourseItem  String /com.example.todoschedule.ui.home.HomeCourseItem  Boolean -com.example.todoschedule.ui.home.HomeTodoItem  Int -com.example.todoschedule.ui.home.HomeTodoItem  	LocalTime -com.example.todoschedule.ui.home.HomeTodoItem  String -com.example.todoschedule.ui.home.HomeTodoItem  HomeCourseItem .com.example.todoschedule.ui.home.HomeViewModel  HomeTodoItem .com.example.todoschedule.ui.home.HomeViewModel  Inject .com.example.todoschedule.ui.home.HomeViewModel  List .com.example.todoschedule.ui.home.HomeViewModel  MutableStateFlow .com.example.todoschedule.ui.home.HomeViewModel  	StateFlow .com.example.todoschedule.ui.home.HomeViewModel  
_todayCourses .com.example.todoschedule.ui.home.HomeViewModel  asStateFlow .com.example.todoschedule.ui.home.HomeViewModel  	emptyList .com.example.todoschedule.ui.home.HomeViewModel  getASStateFlow .com.example.todoschedule.ui.home.HomeViewModel  getAsStateFlow .com.example.todoschedule.ui.home.HomeViewModel  getEMPTYList .com.example.todoschedule.ui.home.HomeViewModel  getEmptyList .com.example.todoschedule.ui.home.HomeViewModel  getGETValue .com.example.todoschedule.ui.home.HomeViewModel  getGetValue .com.example.todoschedule.ui.home.HomeViewModel  getMUTABLEStateOf .com.example.todoschedule.ui.home.HomeViewModel  getMutableStateOf .com.example.todoschedule.ui.home.HomeViewModel  getPROVIDEDelegate .com.example.todoschedule.ui.home.HomeViewModel  getProvideDelegate .com.example.todoschedule.ui.home.HomeViewModel  getSETValue .com.example.todoschedule.ui.home.HomeViewModel  getSetValue .com.example.todoschedule.ui.home.HomeViewModel  getValue .com.example.todoschedule.ui.home.HomeViewModel  mutableStateOf .com.example.todoschedule.ui.home.HomeViewModel  provideDelegate .com.example.todoschedule.ui.home.HomeViewModel  setValue .com.example.todoschedule.ui.home.HomeViewModel  AppBottomNavigationBar &com.example.todoschedule.ui.navigation  AppConstants &com.example.todoschedule.ui.navigation  
AppNavigation &com.example.todoschedule.ui.navigation  	AppRoutes &com.example.todoschedule.ui.navigation  Boolean &com.example.todoschedule.ui.navigation  
BottomNavItem &com.example.todoschedule.ui.navigation  Int &com.example.todoschedule.ui.navigation  Long &com.example.todoschedule.ui.navigation  NavigationState &com.example.todoschedule.ui.navigation  SessionViewModel &com.example.todoschedule.ui.navigation  SharingStarted &com.example.todoschedule.ui.navigation  String &com.example.todoschedule.ui.navigation  Unit &com.example.todoschedule.ui.navigation  stateIn &com.example.todoschedule.ui.navigation  viewModelScope &com.example.todoschedule.ui.navigation  AddEditOrdinarySchedule 0com.example.todoschedule.ui.navigation.AppRoutes  	AppRoutes 0com.example.todoschedule.ui.navigation.AppRoutes  CreateEditTable 0com.example.todoschedule.ui.navigation.AppRoutes  Home 0com.example.todoschedule.ui.navigation.AppRoutes  Int 0com.example.todoschedule.ui.navigation.AppRoutes  OrdinaryScheduleDetail 0com.example.todoschedule.ui.navigation.AppRoutes  String 0com.example.todoschedule.ui.navigation.AppRoutes  Int :com.example.todoschedule.ui.navigation.AppRoutes.AddCourse  ARG_SCHEDULE_ID Hcom.example.todoschedule.ui.navigation.AppRoutes.AddEditOrdinarySchedule  Int Hcom.example.todoschedule.ui.navigation.AppRoutes.AddEditOrdinarySchedule  String Hcom.example.todoschedule.ui.navigation.AppRoutes.AddEditOrdinarySchedule  Int =com.example.todoschedule.ui.navigation.AppRoutes.CourseDetail  ARG_TABLE_ID @com.example.todoschedule.ui.navigation.AppRoutes.CreateEditTable  Int @com.example.todoschedule.ui.navigation.AppRoutes.CreateEditTable  String @com.example.todoschedule.ui.navigation.AppRoutes.CreateEditTable  Int ;com.example.todoschedule.ui.navigation.AppRoutes.EditCourse  ARG_SCHEDULE_ID Gcom.example.todoschedule.ui.navigation.AppRoutes.OrdinaryScheduleDetail  Int Gcom.example.todoschedule.ui.navigation.AppRoutes.OrdinaryScheduleDetail  Int ?com.example.todoschedule.ui.navigation.AppRoutes.SchoolSelector  Int >com.example.todoschedule.ui.navigation.AppRoutes.SchoolWebView  String >com.example.todoschedule.ui.navigation.AppRoutes.SchoolWebView  String Acom.example.todoschedule.ui.navigation.AppRoutes.TaskCalendarSync  ImageVector 4com.example.todoschedule.ui.navigation.BottomNavItem  String 4com.example.todoschedule.ui.navigation.BottomNavItem  Unit 4com.example.todoschedule.ui.navigation.BottomNavItem  Boolean 6com.example.todoschedule.ui.navigation.NavigationState  Int 6com.example.todoschedule.ui.navigation.NavigationState  
NavController 6com.example.todoschedule.ui.navigation.NavigationState  String 6com.example.todoschedule.ui.navigation.NavigationState  
navController 6com.example.todoschedule.ui.navigation.NavigationState  AppConstants 7com.example.todoschedule.ui.navigation.SessionViewModel  GetLoginUserIdFlowUseCase 7com.example.todoschedule.ui.navigation.SessionViewModel  Inject 7com.example.todoschedule.ui.navigation.SessionViewModel  Long 7com.example.todoschedule.ui.navigation.SessionViewModel  SessionRepository 7com.example.todoschedule.ui.navigation.SessionViewModel  SharingStarted 7com.example.todoschedule.ui.navigation.SessionViewModel  	StateFlow 7com.example.todoschedule.ui.navigation.SessionViewModel  
ThemeSettings 7com.example.todoschedule.ui.navigation.SessionViewModel  
getSTATEIn 7com.example.todoschedule.ui.navigation.SessionViewModel  
getStateIn 7com.example.todoschedule.ui.navigation.SessionViewModel  getVIEWModelScope 7com.example.todoschedule.ui.navigation.SessionViewModel  getViewModelScope 7com.example.todoschedule.ui.navigation.SessionViewModel  stateIn 7com.example.todoschedule.ui.navigation.SessionViewModel  viewModelScope 7com.example.todoschedule.ui.navigation.SessionViewModel  AddEditOrdinaryScheduleScreen ,com.example.todoschedule.ui.ordinaryschedule  AddEditOrdinaryScheduleUiState ,com.example.todoschedule.ui.ordinaryschedule   AddEditOrdinaryScheduleViewModel ,com.example.todoschedule.ui.ordinaryschedule  	AppRoutes ,com.example.todoschedule.ui.ordinaryschedule  Boolean ,com.example.todoschedule.ui.ordinaryschedule  ColorPickerDialog ,com.example.todoschedule.ui.ordinaryschedule  DateTimeDisplayChip ,com.example.todoschedule.ui.ordinaryschedule  DeleteConfirmationDialog ,com.example.todoschedule.ui.ordinaryschedule  DetailsCard ,com.example.todoschedule.ui.ordinaryschedule  ErrorContent ,com.example.todoschedule.ui.ordinaryschedule  ExperimentalMaterial3Api ,com.example.todoschedule.ui.ordinaryschedule  Float ,com.example.todoschedule.ui.ordinaryschedule  HeadingCard ,com.example.todoschedule.ui.ordinaryschedule  Int ,com.example.todoschedule.ui.ordinaryschedule  List ,com.example.todoschedule.ui.ordinaryschedule  MutableSharedFlow ,com.example.todoschedule.ui.ordinaryschedule  MutableStateFlow ,com.example.todoschedule.ui.ordinaryschedule  OptIn ,com.example.todoschedule.ui.ordinaryschedule  OrdinaryScheduleDetailEvent ,com.example.todoschedule.ui.ordinaryschedule  OrdinaryScheduleDetailScreen ,com.example.todoschedule.ui.ordinaryschedule  OrdinaryScheduleDetailUiState ,com.example.todoschedule.ui.ordinaryschedule  OrdinaryScheduleDetailViewModel ,com.example.todoschedule.ui.ordinaryschedule  ProgressCard ,com.example.todoschedule.ui.ordinaryschedule  ScheduleDetailsContent ,com.example.todoschedule.ui.ordinaryschedule  SectionCard ,com.example.todoschedule.ui.ordinaryschedule  SharingStarted ,com.example.todoschedule.ui.ordinaryschedule  StatusDropdown ,com.example.todoschedule.ui.ordinaryschedule  String ,com.example.todoschedule.ui.ordinaryschedule  TimePickerDialog ,com.example.todoschedule.ui.ordinaryschedule  TimeSlotItem ,com.example.todoschedule.ui.ordinaryschedule  TimeSlotsSectionCard ,com.example.todoschedule.ui.ordinaryschedule  Unit ,com.example.todoschedule.ui.ordinaryschedule  androidx ,com.example.todoschedule.ui.ordinaryschedule  asSharedFlow ,com.example.todoschedule.ui.ordinaryschedule  asStateFlow ,com.example.todoschedule.ui.ordinaryschedule  checkNotNull ,com.example.todoschedule.ui.ordinaryschedule  com ,com.example.todoschedule.ui.ordinaryschedule  
formatDate ,com.example.todoschedule.ui.ordinaryschedule  
formatTime ,com.example.todoschedule.ui.ordinaryschedule  getOnStatusColor ,com.example.todoschedule.ui.ordinaryschedule  getStatusColor ,com.example.todoschedule.ui.ordinaryschedule  let ,com.example.todoschedule.ui.ordinaryschedule  map ,com.example.todoschedule.ui.ordinaryschedule  
parseColor ,com.example.todoschedule.ui.ordinaryschedule  stateIn ,com.example.todoschedule.ui.ordinaryschedule  viewModelScope ,com.example.todoschedule.ui.ordinaryschedule  Boolean Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  ColorSchemeEnum Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  Int Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  	LocalDate Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  	LocalTime Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  ScheduleStatus Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  String Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  
scheduleId Kcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleUiState  AddEditOrdinaryScheduleUiState Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  AddOrdinaryScheduleUseCase Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  	AppRoutes Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  ColorSchemeEnum Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  GetOrdinaryScheduleByIdUseCase Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  Inject Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  Int Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  	LocalDate Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  	LocalTime Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  MutableStateFlow Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  SavedStateHandle Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  ScheduleStatus Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  SessionRepository Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  	StateFlow Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  String Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  UpdateOrdinaryScheduleUseCase Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  _uiState Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  asStateFlow Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  getASStateFlow Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  getAsStateFlow Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  getLET Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  getLet Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  let Mcom.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel  OrdinaryScheduleDetailEvent Hcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEvent  String Hcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEvent  String Rcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEvent.ShowError  Error Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState  Loading Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState  OrdinarySchedule Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState  OrdinaryScheduleDetailUiState Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState  String Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState  Success Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState  String Pcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState.Error  OrdinarySchedule Rcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiState.Success  	AppRoutes Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  DeleteOrdinaryScheduleUseCase Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  GetOrdinaryScheduleByIdUseCase Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  Inject Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  Int Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  MutableSharedFlow Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  OrdinaryScheduleDetailEvent Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  OrdinaryScheduleDetailUiState Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  SavedStateHandle Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  SharingStarted Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  	StateFlow Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  
_eventFlow Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  asSharedFlow Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  checkNotNull Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getASSharedFlow Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getAsSharedFlow Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getCHECKNotNull Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getCheckNotNull Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getMAP Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getMap Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getOrdinaryScheduleByIdUseCase Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  
getSTATEIn Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  
getStateIn Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getVIEWModelScope Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  getViewModelScope Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  invoke Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  map Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  
scheduleId Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  stateIn Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  viewModelScope Lcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel  
ProfileScreen #com.example.todoschedule.ui.profile  SyncSettingsButton #com.example.todoschedule.ui.profile  Unit #com.example.todoschedule.ui.profile  Any $com.example.todoschedule.ui.schedule  AppConstants $com.example.todoschedule.ui.schedule  Boolean $com.example.todoschedule.ui.schedule  
CalendarUtils $com.example.todoschedule.ui.schedule  
DatePeriod $com.example.todoschedule.ui.schedule  DateTimeFormatter $com.example.todoschedule.ui.schedule  EmptyScheduleScreen $com.example.todoschedule.ui.schedule  ErrorScreen $com.example.todoschedule.ui.schedule  ExperimentalCoroutinesApi $com.example.todoschedule.ui.schedule  ExperimentalFoundationApi $com.example.todoschedule.ui.schedule  ExperimentalMaterial3Api $com.example.todoschedule.ui.schedule  Float $com.example.todoschedule.ui.schedule  
GRID_END_HOUR $com.example.todoschedule.ui.schedule  GRID_START_HOUR $com.example.todoschedule.ui.schedule  HOUR_HEIGHT $com.example.todoschedule.ui.schedule  Int $com.example.todoschedule.ui.schedule  List $com.example.todoschedule.ui.schedule  
LoadingScreen $com.example.todoschedule.ui.schedule  Log $com.example.todoschedule.ui.schedule  Long $com.example.todoschedule.ui.schedule  MutableStateFlow $com.example.todoschedule.ui.schedule  NoTableSelectedScreen $com.example.todoschedule.ui.schedule  OptIn $com.example.todoschedule.ui.schedule  QuickAddScheduleSheetContent $com.example.todoschedule.ui.schedule  #QuickAddScheduleSheetContentPreview $com.example.todoschedule.ui.schedule  QuickAddScheduleUiState $com.example.todoschedule.ui.schedule  QuickAddScheduleViewModel $com.example.todoschedule.ui.schedule  ScheduleContent $com.example.todoschedule.ui.schedule  ScheduleGridWithTimeSlots $com.example.todoschedule.ui.schedule  ScheduleScreen $com.example.todoschedule.ui.schedule  ScheduleUiState $com.example.todoschedule.ui.schedule  ScheduleViewModel $com.example.todoschedule.ui.schedule  SharingStarted $com.example.todoschedule.ui.schedule  String $com.example.todoschedule.ui.schedule  TIME_AXIS_WIDTH $com.example.todoschedule.ui.schedule  TimeAxis $com.example.todoschedule.ui.schedule  
TimeAxisLabel $com.example.todoschedule.ui.schedule  TimeSlotItem $com.example.todoschedule.ui.schedule  TimeZone $com.example.todoschedule.ui.schedule  Triple $com.example.todoschedule.ui.schedule  Unit $com.example.todoschedule.ui.schedule  WeekGrid $com.example.todoschedule.ui.schedule  
WeekHeader $com.example.todoschedule.ui.schedule  WeekSchedulePage $com.example.todoschedule.ui.schedule  also $com.example.todoschedule.ui.schedule  asStateFlow $com.example.todoschedule.ui.schedule  atTime $com.example.todoschedule.ui.schedule  calculateLuminance $com.example.todoschedule.ui.schedule  com $com.example.todoschedule.ui.schedule  combine $com.example.todoschedule.ui.schedule  	emptyList $com.example.todoschedule.ui.schedule  filter $com.example.todoschedule.ui.schedule  
filterNotNull $com.example.todoschedule.ui.schedule  firstOrNull $com.example.todoschedule.ui.schedule  flatMap $com.example.todoschedule.ui.schedule  
flatMapLatest $com.example.todoschedule.ui.schedule  flowOf $com.example.todoschedule.ui.schedule  format $com.example.todoschedule.ui.schedule  generateAdaptiveCourseColor $com.example.todoschedule.ui.schedule  getChineseWeekName $com.example.todoschedule.ui.schedule  isDark $com.example.todoschedule.ui.schedule  isInWeek $com.example.todoschedule.ui.schedule  
isNotEmpty $com.example.todoschedule.ui.schedule  map $com.example.todoschedule.ui.schedule  minus $com.example.todoschedule.ui.schedule  plus $com.example.todoschedule.ui.schedule  sortedBy $com.example.todoschedule.ui.schedule  stateIn $com.example.todoschedule.ui.schedule  
timeFormatter $com.example.todoschedule.ui.schedule  to $com.example.todoschedule.ui.schedule  	toInstant $com.example.todoschedule.ui.schedule  viewModelScope $com.example.todoschedule.ui.schedule  Boolean <com.example.todoschedule.ui.schedule.QuickAddScheduleUiState  	LocalDate <com.example.todoschedule.ui.schedule.QuickAddScheduleUiState  	LocalTime <com.example.todoschedule.ui.schedule.QuickAddScheduleUiState  String <com.example.todoschedule.ui.schedule.QuickAddScheduleUiState  AddOrdinaryScheduleUseCase >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  Boolean >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  Inject >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  	LocalDate >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  	LocalTime >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  MutableStateFlow >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  QuickAddScheduleUiState >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  SessionRepository >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  	StateFlow >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  String >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  Unit >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  _uiState >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  asStateFlow >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  getASStateFlow >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  getAsStateFlow >com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel  AddOrdinaryScheduleUseCase 6com.example.todoschedule.ui.schedule.ScheduleViewModel  AppConstants 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
CalendarUtils 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Course 6com.example.todoschedule.ui.schedule.ScheduleViewModel  CourseRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
DatePeriod 6com.example.todoschedule.ui.schedule.ScheduleViewModel  DeleteOrdinaryScheduleUseCase 6com.example.todoschedule.ui.schedule.ScheduleViewModel   GetDefaultTableTimeConfigUseCase 6com.example.todoschedule.ui.schedule.ScheduleViewModel  GetOrdinarySchedulesUseCase 6com.example.todoschedule.ui.schedule.ScheduleViewModel  GlobalSettingRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Inject 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Int 6com.example.todoschedule.ui.schedule.ScheduleViewModel  List 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	LocalDate 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Log 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Long 6com.example.todoschedule.ui.schedule.ScheduleViewModel  MutableStateFlow 6com.example.todoschedule.ui.schedule.ScheduleViewModel  OrdinarySchedule 6com.example.todoschedule.ui.schedule.ScheduleViewModel  ScheduleUiState 6com.example.todoschedule.ui.schedule.ScheduleViewModel  SessionRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  SharingStarted 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	StateFlow 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Table 6com.example.todoschedule.ui.schedule.ScheduleViewModel  TableRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  TableTimeConfig 6com.example.todoschedule.ui.schedule.ScheduleViewModel  TableTimeConfigRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  TimeSlot 6com.example.todoschedule.ui.schedule.ScheduleViewModel  TimeZone 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Triple 6com.example.todoschedule.ui.schedule.ScheduleViewModel  UpdateOrdinaryScheduleUseCase 6com.example.todoschedule.ui.schedule.ScheduleViewModel  _currentWeek 6com.example.todoschedule.ui.schedule.ScheduleViewModel  _defaultTableIdState 6com.example.todoschedule.ui.schedule.ScheduleViewModel  also 6com.example.todoschedule.ui.schedule.ScheduleViewModel  atTime 6com.example.todoschedule.ui.schedule.ScheduleViewModel  combine 6com.example.todoschedule.ui.schedule.ScheduleViewModel  convertCourseNodesToTimeSlots 6com.example.todoschedule.ui.schedule.ScheduleViewModel  courseRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  currentTableState 6com.example.todoschedule.ui.schedule.ScheduleViewModel  currentTableTimeConfig 6com.example.todoschedule.ui.schedule.ScheduleViewModel  currentUserIdState 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	emptyList 6com.example.todoschedule.ui.schedule.ScheduleViewModel  filter 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
filterNotNull 6com.example.todoschedule.ui.schedule.ScheduleViewModel  firstOrNull 6com.example.todoschedule.ui.schedule.ScheduleViewModel  flatMap 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
flatMapLatest 6com.example.todoschedule.ui.schedule.ScheduleViewModel  flowOf 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getALSO 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	getATTime 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getAlso 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	getAtTime 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getCOMBINE 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getCombine 6com.example.todoschedule.ui.schedule.ScheduleViewModel   getDefaultTableTimeConfigUseCase 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getEMPTYList 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getEmptyList 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	getFILTER 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getFILTERNotNull 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getFIRSTOrNull 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getFLATMap 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getFLATMapLatest 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	getFLOWOf 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	getFilter 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getFilterNotNull 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getFirstOrNull 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getFlatMap 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getFlatMapLatest 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	getFlowOf 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getISNotEmpty 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getIsNotEmpty 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getMAP 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getMINUS 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getMap 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getMinus 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getPLUS 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getPlus 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getSORTEDBy 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getSTATEIn 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getSortedBy 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
getStateIn 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getTO 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getTOInstant 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getTo 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getToInstant 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getVIEWModelScope 6com.example.todoschedule.ui.schedule.ScheduleViewModel  getViewModelScope 6com.example.todoschedule.ui.schedule.ScheduleViewModel  globalSettingRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  invoke 6com.example.todoschedule.ui.schedule.ScheduleViewModel  
isNotEmpty 6com.example.todoschedule.ui.schedule.ScheduleViewModel  isoDayNumber 6com.example.todoschedule.ui.schedule.ScheduleViewModel  map 6com.example.todoschedule.ui.schedule.ScheduleViewModel  minus 6com.example.todoschedule.ui.schedule.ScheduleViewModel  ordinarySchedules 6com.example.todoschedule.ui.schedule.ScheduleViewModel  plus 6com.example.todoschedule.ui.schedule.ScheduleViewModel  sortedBy 6com.example.todoschedule.ui.schedule.ScheduleViewModel  stateIn 6com.example.todoschedule.ui.schedule.ScheduleViewModel  tableRepository 6com.example.todoschedule.ui.schedule.ScheduleViewModel  to 6com.example.todoschedule.ui.schedule.ScheduleViewModel  	toInstant 6com.example.todoschedule.ui.schedule.ScheduleViewModel  userTableDataFlow 6com.example.todoschedule.ui.schedule.ScheduleViewModel  viewModelScope 6com.example.todoschedule.ui.schedule.ScheduleViewModel  weekCourses 6com.example.todoschedule.ui.schedule.ScheduleViewModel  Boolean *com.example.todoschedule.ui.schedule.model  
CourseUiModel *com.example.todoschedule.ui.schedule.model  Int *com.example.todoschedule.ui.schedule.model  ScheduleUiState *com.example.todoschedule.ui.schedule.model  String *com.example.todoschedule.ui.schedule.model  TimeDetailUiModel *com.example.todoschedule.ui.schedule.model  Boolean 8com.example.todoschedule.ui.schedule.model.CourseUiModel  Int 8com.example.todoschedule.ui.schedule.model.CourseUiModel  String 8com.example.todoschedule.ui.schedule.model.CourseUiModel  Loading :com.example.todoschedule.ui.schedule.model.ScheduleUiState  NoTableSelected :com.example.todoschedule.ui.schedule.model.ScheduleUiState  ScheduleUiState :com.example.todoschedule.ui.schedule.model.ScheduleUiState  String :com.example.todoschedule.ui.schedule.model.ScheduleUiState  Success :com.example.todoschedule.ui.schedule.model.ScheduleUiState  String @com.example.todoschedule.ui.schedule.model.ScheduleUiState.Error  Int <com.example.todoschedule.ui.schedule.model.TimeDetailUiModel  	LocalTime <com.example.todoschedule.ui.schedule.model.TimeDetailUiModel  String <com.example.todoschedule.ui.schedule.model.TimeDetailUiModel  
SettingScreen +com.example.todoschedule.ui.screens.setting  String +com.example.todoschedule.ui.screens.setting  SyncErrorDialog +com.example.todoschedule.ui.screens.setting  Unit +com.example.todoschedule.ui.screens.setting  Boolean $com.example.todoschedule.ui.settings  ClearDatabaseConfirmDialog $com.example.todoschedule.ui.settings  DatabaseOperation $com.example.todoschedule.ui.settings  DatabaseOperationHandler $com.example.todoschedule.ui.settings  DeveloperOptions $com.example.todoschedule.ui.settings  ExperimentalMaterial3Api $com.example.todoschedule.ui.settings  Int $com.example.todoschedule.ui.settings  
LoadingDialog $com.example.todoschedule.ui.settings  MutableStateFlow $com.example.todoschedule.ui.settings  OptIn $com.example.todoschedule.ui.settings  SettingsCategory $com.example.todoschedule.ui.settings  SettingsClickableItem $com.example.todoschedule.ui.settings  SettingsScreen $com.example.todoschedule.ui.settings  SettingsSwitchItem $com.example.todoschedule.ui.settings  SettingsViewModel $com.example.todoschedule.ui.settings  String $com.example.todoschedule.ui.settings  Unit $com.example.todoschedule.ui.settings  asStateFlow $com.example.todoschedule.ui.settings  DatabaseOperation 6com.example.todoschedule.ui.settings.DatabaseOperation  Idle 6com.example.todoschedule.ui.settings.DatabaseOperation  String 6com.example.todoschedule.ui.settings.DatabaseOperation  String <com.example.todoschedule.ui.settings.DatabaseOperation.Error  String >com.example.todoschedule.ui.settings.DatabaseOperation.Success  Application 6com.example.todoschedule.ui.settings.SettingsViewModel  Boolean 6com.example.todoschedule.ui.settings.SettingsViewModel  ClearLoginSessionUseCase 6com.example.todoschedule.ui.settings.SettingsViewModel  DatabaseOperation 6com.example.todoschedule.ui.settings.SettingsViewModel  DevUtils 6com.example.todoschedule.ui.settings.SettingsViewModel  Inject 6com.example.todoschedule.ui.settings.SettingsViewModel  Int 6com.example.todoschedule.ui.settings.SettingsViewModel  MutableStateFlow 6com.example.todoschedule.ui.settings.SettingsViewModel  SessionRepository 6com.example.todoschedule.ui.settings.SettingsViewModel  	StateFlow 6com.example.todoschedule.ui.settings.SettingsViewModel  
_darkTheme 6com.example.todoschedule.ui.settings.SettingsViewModel  _databaseOperation 6com.example.todoschedule.ui.settings.SettingsViewModel  _firstDayOfWeek 6com.example.todoschedule.ui.settings.SettingsViewModel  _materialYou 6com.example.todoschedule.ui.settings.SettingsViewModel  asStateFlow 6com.example.todoschedule.ui.settings.SettingsViewModel  getASStateFlow 6com.example.todoschedule.ui.settings.SettingsViewModel  getAsStateFlow 6com.example.todoschedule.ui.settings.SettingsViewModel  StudyScreen !com.example.todoschedule.ui.study  Int  com.example.todoschedule.ui.sync  Long  com.example.todoschedule.ui.sync  MutableStateFlow  com.example.todoschedule.ui.sync  String  com.example.todoschedule.ui.sync  SyncErrorDialog  com.example.todoschedule.ui.sync  	SyncState  com.example.todoschedule.ui.sync  SyncStatusIndicator  com.example.todoschedule.ui.sync  
SyncViewModel  com.example.todoschedule.ui.sync  Unit  com.example.todoschedule.ui.sync  asStateFlow  com.example.todoschedule.ui.sync  ApplicationContext .com.example.todoschedule.ui.sync.SyncViewModel  Context .com.example.todoschedule.ui.sync.SyncViewModel  Inject .com.example.todoschedule.ui.sync.SyncViewModel  Int .com.example.todoschedule.ui.sync.SyncViewModel  Long .com.example.todoschedule.ui.sync.SyncViewModel  MutableStateFlow .com.example.todoschedule.ui.sync.SyncViewModel  	StateFlow .com.example.todoschedule.ui.sync.SyncViewModel  String .com.example.todoschedule.ui.sync.SyncViewModel  SyncManager .com.example.todoschedule.ui.sync.SyncViewModel  SyncRepository .com.example.todoschedule.ui.sync.SyncViewModel  	SyncState .com.example.todoschedule.ui.sync.SyncViewModel  WorkInfo .com.example.todoschedule.ui.sync.SyncViewModel  
_errorMessage .com.example.todoschedule.ui.sync.SyncViewModel  
_lastSyncTime .com.example.todoschedule.ui.sync.SyncViewModel  _lastSyncTimeText .com.example.todoschedule.ui.sync.SyncViewModel  _pendingMessageCount .com.example.todoschedule.ui.sync.SyncViewModel  
_syncState .com.example.todoschedule.ui.sync.SyncViewModel  _syncStatusText .com.example.todoschedule.ui.sync.SyncViewModel  asStateFlow .com.example.todoschedule.ui.sync.SyncViewModel  getASStateFlow .com.example.todoschedule.ui.sync.SyncViewModel  getAsStateFlow .com.example.todoschedule.ui.sync.SyncViewModel  ApplicationContext 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  Context 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  Inject 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  Int 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  Long 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  MutableStateFlow 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  	StateFlow 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  String 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  SyncManager 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  SyncRepository 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  	SyncState 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  WorkInfo 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  asStateFlow 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  getASStateFlow 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  getAsStateFlow 8com.example.todoschedule.ui.sync.SyncViewModel.Companion  IDLE 8com.example.todoschedule.ui.sync.SyncViewModel.SyncState  	AppRoutes !com.example.todoschedule.ui.table  Boolean !com.example.todoschedule.ui.table  CreateEditTableScreen !com.example.todoschedule.ui.table  CreateEditTableUiState !com.example.todoschedule.ui.table  CreateEditTableViewModel !com.example.todoschedule.ui.table  ExperimentalMaterial3Api !com.example.todoschedule.ui.table  Int !com.example.todoschedule.ui.table  MutableStateFlow !com.example.todoschedule.ui.table  OptIn !com.example.todoschedule.ui.table  String !com.example.todoschedule.ui.table  Unit !com.example.todoschedule.ui.table  asStateFlow !com.example.todoschedule.ui.table  takeIf !com.example.todoschedule.ui.table  Boolean 8com.example.todoschedule.ui.table.CreateEditTableUiState  Int 8com.example.todoschedule.ui.table.CreateEditTableUiState  	LocalDate 8com.example.todoschedule.ui.table.CreateEditTableUiState  String 8com.example.todoschedule.ui.table.CreateEditTableUiState  tableId 8com.example.todoschedule.ui.table.CreateEditTableUiState  	AppRoutes :com.example.todoschedule.ui.table.CreateEditTableViewModel  CreateEditTableUiState :com.example.todoschedule.ui.table.CreateEditTableViewModel  GlobalSettingRepository :com.example.todoschedule.ui.table.CreateEditTableViewModel  Inject :com.example.todoschedule.ui.table.CreateEditTableViewModel  Int :com.example.todoschedule.ui.table.CreateEditTableViewModel  	LocalDate :com.example.todoschedule.ui.table.CreateEditTableViewModel  MutableStateFlow :com.example.todoschedule.ui.table.CreateEditTableViewModel  SavedStateHandle :com.example.todoschedule.ui.table.CreateEditTableViewModel  SessionRepository :com.example.todoschedule.ui.table.CreateEditTableViewModel  	StateFlow :com.example.todoschedule.ui.table.CreateEditTableViewModel  String :com.example.todoschedule.ui.table.CreateEditTableViewModel  TableRepository :com.example.todoschedule.ui.table.CreateEditTableViewModel  _uiState :com.example.todoschedule.ui.table.CreateEditTableViewModel  asStateFlow :com.example.todoschedule.ui.table.CreateEditTableViewModel  getASStateFlow :com.example.todoschedule.ui.table.CreateEditTableViewModel  getAsStateFlow :com.example.todoschedule.ui.table.CreateEditTableViewModel  	getTAKEIf :com.example.todoschedule.ui.table.CreateEditTableViewModel  	getTakeIf :com.example.todoschedule.ui.table.CreateEditTableViewModel  takeIf :com.example.todoschedule.ui.table.CreateEditTableViewModel  Array  com.example.todoschedule.ui.task  Boolean  com.example.todoschedule.ui.task  
Composable  com.example.todoschedule.ui.task  ExperimentalCoroutinesApi  com.example.todoschedule.ui.task  ExperimentalMaterial3Api  com.example.todoschedule.ui.task  Int  com.example.todoschedule.ui.task  List  com.example.todoschedule.ui.task  Locale  com.example.todoschedule.ui.task  Long  com.example.todoschedule.ui.task  MutableStateFlow  com.example.todoschedule.ui.task  OptIn  com.example.todoschedule.ui.task  Pair  com.example.todoschedule.ui.task  PriorityTag  com.example.todoschedule.ui.task  Set  com.example.todoschedule.ui.task  SimpleDateFormat  com.example.todoschedule.ui.task  String  com.example.todoschedule.ui.task  TaskCalendarSyncScreen  com.example.todoschedule.ui.task  TaskCalendarSyncState  com.example.todoschedule.ui.task  TaskCalendarViewModel  com.example.todoschedule.ui.task  
TaskFilter  com.example.todoschedule.ui.task  TaskFilterTabs  com.example.todoschedule.ui.task  	TaskGroup  com.example.todoschedule.ui.task  TaskGroupHeader  com.example.todoschedule.ui.task  TaskItem  com.example.todoschedule.ui.task  TaskItemType  com.example.todoschedule.ui.task  TaskItemUiModel  com.example.todoschedule.ui.task  TaskList  com.example.todoschedule.ui.task  TaskReminderScreen  com.example.todoschedule.ui.task  
TaskScreen  com.example.todoschedule.ui.task  TaskSyncItem  com.example.todoschedule.ui.task  
TaskTopAppBar  com.example.todoschedule.ui.task  TaskUiModel  com.example.todoschedule.ui.task  TaskUiState  com.example.todoschedule.ui.task  
TaskViewModel  com.example.todoschedule.ui.task  Unit  com.example.todoschedule.ui.task  asStateFlow  com.example.todoschedule.ui.task  com  com.example.todoschedule.ui.task  	emptyList  com.example.todoschedule.ui.task  
flatMapLatest  com.example.todoschedule.ui.task  flowOf  com.example.todoschedule.ui.task  let  com.example.todoschedule.ui.task  Color ,com.example.todoschedule.ui.task.PriorityTag  String ,com.example.todoschedule.ui.task.PriorityTag  Boolean 6com.example.todoschedule.ui.task.TaskCalendarSyncState  List 6com.example.todoschedule.ui.task.TaskCalendarSyncState  Long 6com.example.todoschedule.ui.task.TaskCalendarSyncState  Set 6com.example.todoschedule.ui.task.TaskCalendarSyncState  String 6com.example.todoschedule.ui.task.TaskCalendarSyncState  TaskItemUiModel 6com.example.todoschedule.ui.task.TaskCalendarSyncState  Array 6com.example.todoschedule.ui.task.TaskCalendarViewModel  Boolean 6com.example.todoschedule.ui.task.TaskCalendarViewModel  CalendarSyncManager 6com.example.todoschedule.ui.task.TaskCalendarViewModel  
CourseNode 6com.example.todoschedule.ui.task.TaskCalendarViewModel  CourseRepository 6com.example.todoschedule.ui.task.TaskCalendarViewModel   GetDefaultTableTimeConfigUseCase 6com.example.todoschedule.ui.task.TaskCalendarViewModel  GetOrdinarySchedulesUseCase 6com.example.todoschedule.ui.task.TaskCalendarViewModel  GlobalSettingRepository 6com.example.todoschedule.ui.task.TaskCalendarViewModel  Inject 6com.example.todoschedule.ui.task.TaskCalendarViewModel  List 6com.example.todoschedule.ui.task.TaskCalendarViewModel  Locale 6com.example.todoschedule.ui.task.TaskCalendarViewModel  Long 6com.example.todoschedule.ui.task.TaskCalendarViewModel  MutableStateFlow 6com.example.todoschedule.ui.task.TaskCalendarViewModel  Pair 6com.example.todoschedule.ui.task.TaskCalendarViewModel  PermissionManager 6com.example.todoschedule.ui.task.TaskCalendarViewModel  SessionRepository 6com.example.todoschedule.ui.task.TaskCalendarViewModel  SimpleDateFormat 6com.example.todoschedule.ui.task.TaskCalendarViewModel  	StateFlow 6com.example.todoschedule.ui.task.TaskCalendarViewModel  String 6com.example.todoschedule.ui.task.TaskCalendarViewModel  TableRepository 6com.example.todoschedule.ui.task.TaskCalendarViewModel  TaskCalendarSyncState 6com.example.todoschedule.ui.task.TaskCalendarViewModel  TaskItemUiModel 6com.example.todoschedule.ui.task.TaskCalendarViewModel  _availableCalendars 6com.example.todoschedule.ui.task.TaskCalendarViewModel  _hasCalendarPermissions 6com.example.todoschedule.ui.task.TaskCalendarViewModel  _uiState 6com.example.todoschedule.ui.task.TaskCalendarViewModel  asStateFlow 6com.example.todoschedule.ui.task.TaskCalendarViewModel  	emptyList 6com.example.todoschedule.ui.task.TaskCalendarViewModel  getASStateFlow 6com.example.todoschedule.ui.task.TaskCalendarViewModel  getAsStateFlow 6com.example.todoschedule.ui.task.TaskCalendarViewModel  getEMPTYList 6com.example.todoschedule.ui.task.TaskCalendarViewModel  getEmptyList 6com.example.todoschedule.ui.task.TaskCalendarViewModel  String *com.example.todoschedule.ui.task.TaskGroup  	TaskGroup *com.example.todoschedule.ui.task.TaskGroup  COURSE -com.example.todoschedule.ui.task.TaskItemType  ORDINARY_SCHEDULE -com.example.todoschedule.ui.task.TaskItemType  Boolean 0com.example.todoschedule.ui.task.TaskItemUiModel  ColorSchemeEnum 0com.example.todoschedule.ui.task.TaskItemUiModel  Int 0com.example.todoschedule.ui.task.TaskItemUiModel  
LocalDateTime 0com.example.todoschedule.ui.task.TaskItemUiModel  OrdinaryTask 0com.example.todoschedule.ui.task.TaskItemUiModel  PriorityTag 0com.example.todoschedule.ui.task.TaskItemUiModel  ScheduleStatus 0com.example.todoschedule.ui.task.TaskItemUiModel  String 0com.example.todoschedule.ui.task.TaskItemUiModel  TaskItemType 0com.example.todoschedule.ui.task.TaskItemUiModel  TaskItemUiModel 0com.example.todoschedule.ui.task.TaskItemUiModel  Boolean ;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask  ColorSchemeEnum ;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask  Int ;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask  
LocalDateTime ;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask  PriorityTag ;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask  String ;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask  TaskItemType ;com.example.todoschedule.ui.task.TaskItemUiModel.CourseTask  Boolean =com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask  Int =com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask  
LocalDateTime =com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask  PriorityTag =com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask  ScheduleStatus =com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask  String =com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask  TaskItemType =com.example.todoschedule.ui.task.TaskItemUiModel.OrdinaryTask  Boolean ,com.example.todoschedule.ui.task.TaskUiModel  Calendar ,com.example.todoschedule.ui.task.TaskUiModel  Long ,com.example.todoschedule.ui.task.TaskUiModel  String ,com.example.todoschedule.ui.task.TaskUiModel  Boolean ,com.example.todoschedule.ui.task.TaskUiState  List ,com.example.todoschedule.ui.task.TaskUiState  String ,com.example.todoschedule.ui.task.TaskUiState  
TaskFilter ,com.example.todoschedule.ui.task.TaskUiState  TaskItemUiModel ,com.example.todoschedule.ui.task.TaskUiState  Boolean .com.example.todoschedule.ui.task.TaskViewModel  CourseRepository .com.example.todoschedule.ui.task.TaskViewModel  Flow .com.example.todoschedule.ui.task.TaskViewModel   GetDefaultTableTimeConfigUseCase .com.example.todoschedule.ui.task.TaskViewModel  GetOrdinarySchedulesUseCase .com.example.todoschedule.ui.task.TaskViewModel  GlobalSettingRepository .com.example.todoschedule.ui.task.TaskViewModel  Inject .com.example.todoschedule.ui.task.TaskViewModel  Instant .com.example.todoschedule.ui.task.TaskViewModel  Int .com.example.todoschedule.ui.task.TaskViewModel  MutableStateFlow .com.example.todoschedule.ui.task.TaskViewModel  OrdinarySchedule .com.example.todoschedule.ui.task.TaskViewModel  PriorityTag .com.example.todoschedule.ui.task.TaskViewModel  SessionRepository .com.example.todoschedule.ui.task.TaskViewModel  	StateFlow .com.example.todoschedule.ui.task.TaskViewModel  String .com.example.todoschedule.ui.task.TaskViewModel  TableRepository .com.example.todoschedule.ui.task.TaskViewModel  
TaskFilter .com.example.todoschedule.ui.task.TaskViewModel  TaskItemUiModel .com.example.todoschedule.ui.task.TaskViewModel  TaskUiState .com.example.todoschedule.ui.task.TaskViewModel  UpdateOrdinaryScheduleUseCase .com.example.todoschedule.ui.task.TaskViewModel  _uiState .com.example.todoschedule.ui.task.TaskViewModel  asStateFlow .com.example.todoschedule.ui.task.TaskViewModel  com .com.example.todoschedule.ui.task.TaskViewModel  currentUserIdFlow .com.example.todoschedule.ui.task.TaskViewModel  defaultTableIdsFlow .com.example.todoschedule.ui.task.TaskViewModel  	emptyList .com.example.todoschedule.ui.task.TaskViewModel  
flatMapLatest .com.example.todoschedule.ui.task.TaskViewModel  flowOf .com.example.todoschedule.ui.task.TaskViewModel  getASStateFlow .com.example.todoschedule.ui.task.TaskViewModel  getAsStateFlow .com.example.todoschedule.ui.task.TaskViewModel   getDefaultTableTimeConfigUseCase .com.example.todoschedule.ui.task.TaskViewModel  getEMPTYList .com.example.todoschedule.ui.task.TaskViewModel  getEmptyList .com.example.todoschedule.ui.task.TaskViewModel  getFLATMapLatest .com.example.todoschedule.ui.task.TaskViewModel  	getFLOWOf .com.example.todoschedule.ui.task.TaskViewModel  getFlatMapLatest .com.example.todoschedule.ui.task.TaskViewModel  	getFlowOf .com.example.todoschedule.ui.task.TaskViewModel  getLET .com.example.todoschedule.ui.task.TaskViewModel  getLet .com.example.todoschedule.ui.task.TaskViewModel  globalSettingRepository .com.example.todoschedule.ui.task.TaskViewModel  invoke .com.example.todoschedule.ui.task.TaskViewModel  let .com.example.todoschedule.ui.task.TaskViewModel  sessionRepository .com.example.todoschedule.ui.task.TaskViewModel  Boolean !com.example.todoschedule.ui.theme  ColorSchemeEnum !com.example.todoschedule.ui.theme  List !com.example.todoschedule.ui.theme  String !com.example.todoschedule.ui.theme  TodoScheduleTheme !com.example.todoschedule.ui.theme  
Typography !com.example.todoschedule.ui.theme  Unit !com.example.todoschedule.ui.theme  backgroundDark !com.example.todoschedule.ui.theme  backgroundDarkHighContrast !com.example.todoschedule.ui.theme  backgroundDarkMediumContrast !com.example.todoschedule.ui.theme  backgroundLight !com.example.todoschedule.ui.theme  backgroundLightHighContrast !com.example.todoschedule.ui.theme  backgroundLightMediumContrast !com.example.todoschedule.ui.theme  
darkScheme !com.example.todoschedule.ui.theme  errorContainerDark !com.example.todoschedule.ui.theme  errorContainerDarkHighContrast !com.example.todoschedule.ui.theme   errorContainerDarkMediumContrast !com.example.todoschedule.ui.theme  errorContainerLight !com.example.todoschedule.ui.theme  errorContainerLightHighContrast !com.example.todoschedule.ui.theme  !errorContainerLightMediumContrast !com.example.todoschedule.ui.theme  	errorDark !com.example.todoschedule.ui.theme  errorDarkHighContrast !com.example.todoschedule.ui.theme  errorDarkMediumContrast !com.example.todoschedule.ui.theme  
errorLight !com.example.todoschedule.ui.theme  errorLightHighContrast !com.example.todoschedule.ui.theme  errorLightMediumContrast !com.example.todoschedule.ui.theme  getColorListFromColorScheme !com.example.todoschedule.ui.theme  highContrastDarkColorScheme !com.example.todoschedule.ui.theme  highContrastLightColorScheme !com.example.todoschedule.ui.theme  inverseOnSurfaceDark !com.example.todoschedule.ui.theme   inverseOnSurfaceDarkHighContrast !com.example.todoschedule.ui.theme  "inverseOnSurfaceDarkMediumContrast !com.example.todoschedule.ui.theme  inverseOnSurfaceLight !com.example.todoschedule.ui.theme  !inverseOnSurfaceLightHighContrast !com.example.todoschedule.ui.theme  #inverseOnSurfaceLightMediumContrast !com.example.todoschedule.ui.theme  inversePrimaryDark !com.example.todoschedule.ui.theme  inversePrimaryDarkHighContrast !com.example.todoschedule.ui.theme   inversePrimaryDarkMediumContrast !com.example.todoschedule.ui.theme  inversePrimaryLight !com.example.todoschedule.ui.theme  inversePrimaryLightHighContrast !com.example.todoschedule.ui.theme  !inversePrimaryLightMediumContrast !com.example.todoschedule.ui.theme  inverseSurfaceDark !com.example.todoschedule.ui.theme  inverseSurfaceDarkHighContrast !com.example.todoschedule.ui.theme   inverseSurfaceDarkMediumContrast !com.example.todoschedule.ui.theme  inverseSurfaceLight !com.example.todoschedule.ui.theme  inverseSurfaceLightHighContrast !com.example.todoschedule.ui.theme  !inverseSurfaceLightMediumContrast !com.example.todoschedule.ui.theme  lightScheme !com.example.todoschedule.ui.theme  mediumContrastDarkColorScheme !com.example.todoschedule.ui.theme  mediumContrastLightColorScheme !com.example.todoschedule.ui.theme  onBackgroundDark !com.example.todoschedule.ui.theme  onBackgroundDarkHighContrast !com.example.todoschedule.ui.theme  onBackgroundDarkMediumContrast !com.example.todoschedule.ui.theme  onBackgroundLight !com.example.todoschedule.ui.theme  onBackgroundLightHighContrast !com.example.todoschedule.ui.theme  onBackgroundLightMediumContrast !com.example.todoschedule.ui.theme  onErrorContainerDark !com.example.todoschedule.ui.theme   onErrorContainerDarkHighContrast !com.example.todoschedule.ui.theme  "onErrorContainerDarkMediumContrast !com.example.todoschedule.ui.theme  onErrorContainerLight !com.example.todoschedule.ui.theme  !onErrorContainerLightHighContrast !com.example.todoschedule.ui.theme  #onErrorContainerLightMediumContrast !com.example.todoschedule.ui.theme  onErrorDark !com.example.todoschedule.ui.theme  onErrorDarkHighContrast !com.example.todoschedule.ui.theme  onErrorDarkMediumContrast !com.example.todoschedule.ui.theme  onErrorLight !com.example.todoschedule.ui.theme  onErrorLightHighContrast !com.example.todoschedule.ui.theme  onErrorLightMediumContrast !com.example.todoschedule.ui.theme  onPrimaryContainerDark !com.example.todoschedule.ui.theme  "onPrimaryContainerDarkHighContrast !com.example.todoschedule.ui.theme  $onPrimaryContainerDarkMediumContrast !com.example.todoschedule.ui.theme  onPrimaryContainerLight !com.example.todoschedule.ui.theme  #onPrimaryContainerLightHighContrast !com.example.todoschedule.ui.theme  %onPrimaryContainerLightMediumContrast !com.example.todoschedule.ui.theme  
onPrimaryDark !com.example.todoschedule.ui.theme  onPrimaryDarkHighContrast !com.example.todoschedule.ui.theme  onPrimaryDarkMediumContrast !com.example.todoschedule.ui.theme  onPrimaryLight !com.example.todoschedule.ui.theme  onPrimaryLightHighContrast !com.example.todoschedule.ui.theme  onPrimaryLightMediumContrast !com.example.todoschedule.ui.theme  onSecondaryContainerDark !com.example.todoschedule.ui.theme  $onSecondaryContainerDarkHighContrast !com.example.todoschedule.ui.theme  &onSecondaryContainerDarkMediumContrast !com.example.todoschedule.ui.theme  onSecondaryContainerLight !com.example.todoschedule.ui.theme  %onSecondaryContainerLightHighContrast !com.example.todoschedule.ui.theme  'onSecondaryContainerLightMediumContrast !com.example.todoschedule.ui.theme  onSecondaryDark !com.example.todoschedule.ui.theme  onSecondaryDarkHighContrast !com.example.todoschedule.ui.theme  onSecondaryDarkMediumContrast !com.example.todoschedule.ui.theme  onSecondaryLight !com.example.todoschedule.ui.theme  onSecondaryLightHighContrast !com.example.todoschedule.ui.theme  onSecondaryLightMediumContrast !com.example.todoschedule.ui.theme  
onSurfaceDark !com.example.todoschedule.ui.theme  onSurfaceDarkHighContrast !com.example.todoschedule.ui.theme  onSurfaceDarkMediumContrast !com.example.todoschedule.ui.theme  onSurfaceLight !com.example.todoschedule.ui.theme  onSurfaceLightHighContrast !com.example.todoschedule.ui.theme  onSurfaceLightMediumContrast !com.example.todoschedule.ui.theme  onSurfaceVariantDark !com.example.todoschedule.ui.theme   onSurfaceVariantDarkHighContrast !com.example.todoschedule.ui.theme  "onSurfaceVariantDarkMediumContrast !com.example.todoschedule.ui.theme  onSurfaceVariantLight !com.example.todoschedule.ui.theme  !onSurfaceVariantLightHighContrast !com.example.todoschedule.ui.theme  #onSurfaceVariantLightMediumContrast !com.example.todoschedule.ui.theme  onTertiaryContainerDark !com.example.todoschedule.ui.theme  #onTertiaryContainerDarkHighContrast !com.example.todoschedule.ui.theme  %onTertiaryContainerDarkMediumContrast !com.example.todoschedule.ui.theme  onTertiaryContainerLight !com.example.todoschedule.ui.theme  $onTertiaryContainerLightHighContrast !com.example.todoschedule.ui.theme  &onTertiaryContainerLightMediumContrast !com.example.todoschedule.ui.theme  onTertiaryDark !com.example.todoschedule.ui.theme  onTertiaryDarkHighContrast !com.example.todoschedule.ui.theme  onTertiaryDarkMediumContrast !com.example.todoschedule.ui.theme  onTertiaryLight !com.example.todoschedule.ui.theme  onTertiaryLightHighContrast !com.example.todoschedule.ui.theme  onTertiaryLightMediumContrast !com.example.todoschedule.ui.theme  outlineDark !com.example.todoschedule.ui.theme  outlineDarkHighContrast !com.example.todoschedule.ui.theme  outlineDarkMediumContrast !com.example.todoschedule.ui.theme  outlineLight !com.example.todoschedule.ui.theme  outlineLightHighContrast !com.example.todoschedule.ui.theme  outlineLightMediumContrast !com.example.todoschedule.ui.theme  outlineVariantDark !com.example.todoschedule.ui.theme  outlineVariantDarkHighContrast !com.example.todoschedule.ui.theme   outlineVariantDarkMediumContrast !com.example.todoschedule.ui.theme  outlineVariantLight !com.example.todoschedule.ui.theme  outlineVariantLightHighContrast !com.example.todoschedule.ui.theme  !outlineVariantLightMediumContrast !com.example.todoschedule.ui.theme  primaryContainerDark !com.example.todoschedule.ui.theme   primaryContainerDarkHighContrast !com.example.todoschedule.ui.theme  "primaryContainerDarkMediumContrast !com.example.todoschedule.ui.theme  primaryContainerLight !com.example.todoschedule.ui.theme  !primaryContainerLightHighContrast !com.example.todoschedule.ui.theme  #primaryContainerLightMediumContrast !com.example.todoschedule.ui.theme  primaryDark !com.example.todoschedule.ui.theme  primaryDarkHighContrast !com.example.todoschedule.ui.theme  primaryDarkMediumContrast !com.example.todoschedule.ui.theme  primaryLight !com.example.todoschedule.ui.theme  primaryLightHighContrast !com.example.todoschedule.ui.theme  primaryLightMediumContrast !com.example.todoschedule.ui.theme  	scrimDark !com.example.todoschedule.ui.theme  scrimDarkHighContrast !com.example.todoschedule.ui.theme  scrimDarkMediumContrast !com.example.todoschedule.ui.theme  
scrimLight !com.example.todoschedule.ui.theme  scrimLightHighContrast !com.example.todoschedule.ui.theme  scrimLightMediumContrast !com.example.todoschedule.ui.theme  secondaryContainerDark !com.example.todoschedule.ui.theme  "secondaryContainerDarkHighContrast !com.example.todoschedule.ui.theme  $secondaryContainerDarkMediumContrast !com.example.todoschedule.ui.theme  secondaryContainerLight !com.example.todoschedule.ui.theme  #secondaryContainerLightHighContrast !com.example.todoschedule.ui.theme  %secondaryContainerLightMediumContrast !com.example.todoschedule.ui.theme  
secondaryDark !com.example.todoschedule.ui.theme  secondaryDarkHighContrast !com.example.todoschedule.ui.theme  secondaryDarkMediumContrast !com.example.todoschedule.ui.theme  secondaryLight !com.example.todoschedule.ui.theme  secondaryLightHighContrast !com.example.todoschedule.ui.theme  secondaryLightMediumContrast !com.example.todoschedule.ui.theme  surfaceBrightDark !com.example.todoschedule.ui.theme  surfaceBrightDarkHighContrast !com.example.todoschedule.ui.theme  surfaceBrightDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceBrightLight !com.example.todoschedule.ui.theme  surfaceBrightLightHighContrast !com.example.todoschedule.ui.theme   surfaceBrightLightMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerDark !com.example.todoschedule.ui.theme   surfaceContainerDarkHighContrast !com.example.todoschedule.ui.theme  "surfaceContainerDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerHighDark !com.example.todoschedule.ui.theme  $surfaceContainerHighDarkHighContrast !com.example.todoschedule.ui.theme  &surfaceContainerHighDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerHighLight !com.example.todoschedule.ui.theme  %surfaceContainerHighLightHighContrast !com.example.todoschedule.ui.theme  'surfaceContainerHighLightMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerHighestDark !com.example.todoschedule.ui.theme  'surfaceContainerHighestDarkHighContrast !com.example.todoschedule.ui.theme  )surfaceContainerHighestDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerHighestLight !com.example.todoschedule.ui.theme  (surfaceContainerHighestLightHighContrast !com.example.todoschedule.ui.theme  *surfaceContainerHighestLightMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerLight !com.example.todoschedule.ui.theme  !surfaceContainerLightHighContrast !com.example.todoschedule.ui.theme  #surfaceContainerLightMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerLowDark !com.example.todoschedule.ui.theme  #surfaceContainerLowDarkHighContrast !com.example.todoschedule.ui.theme  %surfaceContainerLowDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerLowLight !com.example.todoschedule.ui.theme  $surfaceContainerLowLightHighContrast !com.example.todoschedule.ui.theme  &surfaceContainerLowLightMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerLowestDark !com.example.todoschedule.ui.theme  &surfaceContainerLowestDarkHighContrast !com.example.todoschedule.ui.theme  (surfaceContainerLowestDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceContainerLowestLight !com.example.todoschedule.ui.theme  'surfaceContainerLowestLightHighContrast !com.example.todoschedule.ui.theme  )surfaceContainerLowestLightMediumContrast !com.example.todoschedule.ui.theme  surfaceDark !com.example.todoschedule.ui.theme  surfaceDarkHighContrast !com.example.todoschedule.ui.theme  surfaceDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceDimDark !com.example.todoschedule.ui.theme  surfaceDimDarkHighContrast !com.example.todoschedule.ui.theme  surfaceDimDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceDimLight !com.example.todoschedule.ui.theme  surfaceDimLightHighContrast !com.example.todoschedule.ui.theme  surfaceDimLightMediumContrast !com.example.todoschedule.ui.theme  surfaceLight !com.example.todoschedule.ui.theme  surfaceLightHighContrast !com.example.todoschedule.ui.theme  surfaceLightMediumContrast !com.example.todoschedule.ui.theme  surfaceVariantDark !com.example.todoschedule.ui.theme  surfaceVariantDarkHighContrast !com.example.todoschedule.ui.theme   surfaceVariantDarkMediumContrast !com.example.todoschedule.ui.theme  surfaceVariantLight !com.example.todoschedule.ui.theme  surfaceVariantLightHighContrast !com.example.todoschedule.ui.theme  !surfaceVariantLightMediumContrast !com.example.todoschedule.ui.theme  tertiaryContainerDark !com.example.todoschedule.ui.theme  !tertiaryContainerDarkHighContrast !com.example.todoschedule.ui.theme  #tertiaryContainerDarkMediumContrast !com.example.todoschedule.ui.theme  tertiaryContainerLight !com.example.todoschedule.ui.theme  "tertiaryContainerLightHighContrast !com.example.todoschedule.ui.theme  $tertiaryContainerLightMediumContrast !com.example.todoschedule.ui.theme  tertiaryDark !com.example.todoschedule.ui.theme  tertiaryDarkHighContrast !com.example.todoschedule.ui.theme  tertiaryDarkMediumContrast !com.example.todoschedule.ui.theme  
tertiaryLight !com.example.todoschedule.ui.theme  tertiaryLightHighContrast !com.example.todoschedule.ui.theme  tertiaryLightMediumContrast !com.example.todoschedule.ui.theme  to16Bit !com.example.todoschedule.ui.theme  toColor !com.example.todoschedule.ui.theme  toColorOrDefault !com.example.todoschedule.ui.theme  toColorSchemeEnum !com.example.todoschedule.ui.theme  Color 1com.example.todoschedule.ui.theme.ColorSchemeEnum  ColorScheme 1com.example.todoschedule.ui.theme.ColorSchemeEnum  ColorSchemeEnum 1com.example.todoschedule.ui.theme.ColorSchemeEnum  List 1com.example.todoschedule.ui.theme.ColorSchemeEnum  ONERROR 1com.example.todoschedule.ui.theme.ColorSchemeEnum  String 1com.example.todoschedule.ui.theme.ColorSchemeEnum  Color ;com.example.todoschedule.ui.theme.ColorSchemeEnum.Companion  ColorScheme ;com.example.todoschedule.ui.theme.ColorSchemeEnum.Companion  ColorSchemeEnum ;com.example.todoschedule.ui.theme.ColorSchemeEnum.Companion  List ;com.example.todoschedule.ui.theme.ColorSchemeEnum.Companion  ONERROR ;com.example.todoschedule.ui.theme.ColorSchemeEnum.Companion  String ;com.example.todoschedule.ui.theme.ColorSchemeEnum.Companion  Color 7com.example.todoschedule.ui.theme.ColorSchemeEnum.Fixed  Boolean  com.example.todoschedule.ui.todo  DateItem  com.example.todoschedule.ui.todo  DateSelector  com.example.todoschedule.ui.todo  ExperimentalMaterial3Api  com.example.todoschedule.ui.todo  Int  com.example.todoschedule.ui.todo  List  com.example.todoschedule.ui.todo  	LocalDate  com.example.todoschedule.ui.todo  MutableStateFlow  com.example.todoschedule.ui.todo  OptIn  com.example.todoschedule.ui.todo  String  com.example.todoschedule.ui.todo  TodoItem  com.example.todoschedule.ui.todo  TodoPriority  com.example.todoschedule.ui.todo  
TodoScreen  com.example.todoschedule.ui.todo  TodoUiModel  com.example.todoschedule.ui.todo  TodoUiState  com.example.todoschedule.ui.todo  
TodoViewModel  com.example.todoschedule.ui.todo  Unit  com.example.todoschedule.ui.todo  asStateFlow  com.example.todoschedule.ui.todo  generateDateList  com.example.todoschedule.ui.todo  listOf  com.example.todoschedule.ui.todo  onClick  com.example.todoschedule.ui.todo  Boolean ,com.example.todoschedule.ui.todo.TodoUiModel  Int ,com.example.todoschedule.ui.todo.TodoUiModel  	LocalDate ,com.example.todoschedule.ui.todo.TodoUiModel  String ,com.example.todoschedule.ui.todo.TodoUiModel  TodoPriority ,com.example.todoschedule.ui.todo.TodoUiModel  List ,com.example.todoschedule.ui.todo.TodoUiState  Loading ,com.example.todoschedule.ui.todo.TodoUiState  String ,com.example.todoschedule.ui.todo.TodoUiState  TodoUiModel ,com.example.todoschedule.ui.todo.TodoUiState  TodoUiState ,com.example.todoschedule.ui.todo.TodoUiState  String 2com.example.todoschedule.ui.todo.TodoUiState.Error  List 4com.example.todoschedule.ui.todo.TodoUiState.Success  TodoUiModel 4com.example.todoschedule.ui.todo.TodoUiState.Success  Inject .com.example.todoschedule.ui.todo.TodoViewModel  Int .com.example.todoschedule.ui.todo.TodoViewModel  	LocalDate .com.example.todoschedule.ui.todo.TodoViewModel  MutableStateFlow .com.example.todoschedule.ui.todo.TodoViewModel  	StateFlow .com.example.todoschedule.ui.todo.TodoViewModel  TodoUiModel .com.example.todoschedule.ui.todo.TodoViewModel  TodoUiState .com.example.todoschedule.ui.todo.TodoViewModel  
_selectedDate .com.example.todoschedule.ui.todo.TodoViewModel  _uiState .com.example.todoschedule.ui.todo.TodoViewModel  asStateFlow .com.example.todoschedule.ui.todo.TodoViewModel  getASStateFlow .com.example.todoschedule.ui.todo.TodoViewModel  getAsStateFlow .com.example.todoschedule.ui.todo.TodoViewModel  	getLISTOf .com.example.todoschedule.ui.todo.TodoViewModel  	getListOf .com.example.todoschedule.ui.todo.TodoViewModel  listOf .com.example.todoschedule.ui.todo.TodoViewModel  Boolean !com.example.todoschedule.ui.utils  
ColorUtils !com.example.todoschedule.ui.utils  Float !com.example.todoschedule.ui.utils  Int !com.example.todoschedule.ui.utils  Pair !com.example.todoschedule.ui.utils  Boolean ,com.example.todoschedule.ui.utils.ColorUtils  Color ,com.example.todoschedule.ui.utils.ColorUtils  ColorScheme ,com.example.todoschedule.ui.utils.ColorUtils  ColorSchemeEnum ,com.example.todoschedule.ui.utils.ColorUtils  Float ,com.example.todoschedule.ui.utils.ColorUtils  Int ,com.example.todoschedule.ui.utils.ColorUtils  Pair ,com.example.todoschedule.ui.utils.ColorUtils  Resource com.example.todoschedule.util  String com.example.todoschedule.util  Resource &com.example.todoschedule.util.Resource  String &com.example.todoschedule.util.Resource  String ,com.example.todoschedule.util.Resource.Error  T ,com.example.todoschedule.util.Resource.Error  T .com.example.todoschedule.util.Resource.Loading  T .com.example.todoschedule.util.Resource.Success  	ArrayList 1com.example.todoschedule.utils.courseadapter.bean  ParserResult 1com.example.todoschedule.utils.courseadapter.bean  	ArrayList >com.example.todoschedule.utils.courseadapter.bean.ParserResult  CourseBaseBean >com.example.todoschedule.utils.courseadapter.bean.ParserResult  CourseDetailBean >com.example.todoschedule.utils.courseadapter.bean.ParserResult  Err com.github.michaelbull.result  Ok com.github.michaelbull.result  Result com.github.michaelbull.result  getOr com.github.michaelbull.result  GsonBuilder com.google.gson  
JsonParser com.google.gson  Moshi com.squareup.moshi  KotlinJsonAdapterFactory !com.squareup.moshi.kotlin.reflect  HybridLogicalClock com.tap.hlc  NodeID com.tap.hlc  	Timestamp com.tap.hlc  Binds dagger  Module dagger  Provides dagger  Assisted dagger.assisted  AssistedInject dagger.assisted  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  IOException java.io  delete java.io.File  AddEditOrdinaryScheduleUiState 	java.lang  AppConstants 	java.lang  	AppRoutes 	java.lang  
CalendarUtils 	java.lang  Channel 	java.lang  ColorScheme 	java.lang  ColorSchemeEnum 	java.lang  
Converters 	java.lang  CoroutineScope 	java.lang  CourseDetailItem 	java.lang  CourseDetailUiState 	java.lang  CourseEntity 	java.lang  CourseNodeEntity 	java.lang  CreateEditTableUiState 	java.lang  DatabaseOperation 	java.lang  
DatePeriod 	java.lang  DateTimeFormatter 	java.lang  Dispatchers 	java.lang  EditCourseUiState 	java.lang  	Exception 	java.lang  ExperimentalCoroutinesApi 	java.lang  ExperimentalFoundationApi 	java.lang  ExperimentalMaterial3Api 	java.lang  GlobalTableSettingEntity 	java.lang  Header 	java.lang  Json 	java.lang  	LocalDate 	java.lang  Locale 	java.lang  Log 	java.lang  LoginUiState 	java.lang  Manifest 	java.lang  Math 	java.lang  MutableLiveData 	java.lang  MutableSharedFlow 	java.lang  MutableStateFlow 	java.lang  Mutex 	java.lang  OnConflictStrategy 	java.lang  OrdinaryScheduleDetailUiState 	java.lang  OrdinaryScheduleEntity 	java.lang  PreferencesKeys 	java.lang  QuickAddScheduleUiState 	java.lang  Regex 	java.lang  RegisterUiState 	java.lang  SaveCourseState 	java.lang  	SaveState 	java.lang  ScheduleType 	java.lang  ScheduleUiState 	java.lang  School 	java.lang  SharingStarted 	java.lang  SimpleDateFormat 	java.lang  SingletonComponent 	java.lang  
SupervisorJob 	java.lang  SyncMessageEntity 	java.lang  	SyncState 	java.lang  TableEntity 	java.lang  TableTimeConfigEntity 	java.lang   TableTimeConfigNodeDetaileEntity 	java.lang  TaskCalendarSyncState 	java.lang  TaskItemType 	java.lang  TaskUiState 	java.lang  
ThemeSettings 	java.lang  
TimeDetail 	java.lang  TimePreferenceItem 	java.lang  TimeSlotEntity 	java.lang  	TimeTable 	java.lang  TimeZone 	java.lang  TodoUiModel 	java.lang  TodoUiState 	java.lang  Triple 	java.lang  
UserEntity 	java.lang  _rawSchools 	java.lang  also 	java.lang  androidx 	java.lang  arrayListOf 	java.lang  arrayOf 	java.lang  asSharedFlow 	java.lang  asStateFlow 	java.lang  atTime 	java.lang  booleanPreferencesKey 	java.lang  catch 	java.lang  checkNotNull 	java.lang  com 	java.lang  combine 	java.lang  context 	java.lang  db 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  emptyPreferences 	java.lang  filter 	java.lang  
filterNotNull 	java.lang  firstOrNull 	java.lang  flatMap 	java.lang  
flatMapLatest 	java.lang  flow 	java.lang  flowOf 	java.lang  getValue 	java.lang  invoke 	java.lang  
isNotEmpty 	java.lang  let 	java.lang  listOf 	java.lang  longPreferencesKey 	java.lang  map 	java.lang  minus 	java.lang  mutableStateOf 	java.lang  plus 	java.lang  preferencesDataStore 	java.lang  preferencesDataStoreFile 	java.lang  provideDelegate 	java.lang  
receiveAsFlow 	java.lang  setValue 	java.lang  sortedBy 	java.lang  stateIn 	java.lang  stringPreferencesKey 	java.lang  takeIf 	java.lang  to 	java.lang  	toInstant 	java.lang  withContext 	java.lang  printStackTrace java.lang.Exception  abs java.lang.Math  
URLDecoder java.net  
URLEncoder java.net  
MessageDigest 
java.security  SimpleDateFormat 	java.text  Instant 	java.time  	LocalDate 	java.time  	LocalTime 	java.time  ZoneId 	java.time  getISODayNumber java.time.DayOfWeek  getIsoDayNumber java.time.DayOfWeek  isoDayNumber java.time.DayOfWeek  now java.time.LocalDate  plusDays java.time.LocalDate  DateTimeFormatter java.time.format  FormatStyle java.time.format  	ofPattern "java.time.format.DateTimeFormatter  	ArrayList 	java.util  Calendar 	java.util  
Composable 	java.util  Date 	java.util  ExperimentalMaterial3Api 	java.util  Locale 	java.util  TimeZone 	java.util  Timer 	java.util  	TimerTask 	java.util  UUID 	java.util  map java.util.AbstractCollection  map java.util.AbstractList  getMAP java.util.ArrayList  getMap java.util.ArrayList  map java.util.ArrayList  
getDefault java.util.Locale  TimeUnit java.util.concurrent  Inject javax.inject  	Qualifier javax.inject  	Singleton javax.inject  AddEditOrdinaryScheduleUiState kotlin  Any kotlin  AppConstants kotlin  	AppRoutes kotlin  Array kotlin  	ArrayList kotlin  Boolean kotlin  	ByteArray kotlin  
CalendarUtils kotlin  Channel kotlin  Char kotlin  ColorScheme kotlin  ColorSchemeEnum kotlin  
Converters kotlin  CoroutineScope kotlin  CourseDetailItem kotlin  CourseDetailUiState kotlin  CourseEntity kotlin  CourseNodeEntity kotlin  CreateEditTableUiState kotlin  DatabaseOperation kotlin  
DatePeriod kotlin  DateTimeFormatter kotlin  Dispatchers kotlin  Double kotlin  EditCourseUiState kotlin  	Exception kotlin  ExperimentalCoroutinesApi kotlin  ExperimentalFoundationApi kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  	Function1 kotlin  GlobalTableSettingEntity kotlin  Header kotlin  Int kotlin  Json kotlin  Lazy kotlin  	LocalDate kotlin  Locale kotlin  Log kotlin  LoginUiState kotlin  Long kotlin  Manifest kotlin  MutableLiveData kotlin  MutableSharedFlow kotlin  MutableStateFlow kotlin  Mutex kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  OrdinaryScheduleDetailUiState kotlin  OrdinaryScheduleEntity kotlin  Pair kotlin  PreferencesKeys kotlin  QuickAddScheduleUiState kotlin  Regex kotlin  RegisterUiState kotlin  Result kotlin  SaveCourseState kotlin  	SaveState kotlin  ScheduleType kotlin  ScheduleUiState kotlin  School kotlin  SharingStarted kotlin  SimpleDateFormat kotlin  SingletonComponent kotlin  String kotlin  
SupervisorJob kotlin  SyncMessageEntity kotlin  	SyncState kotlin  TableEntity kotlin  TableTimeConfigEntity kotlin   TableTimeConfigNodeDetaileEntity kotlin  TaskCalendarSyncState kotlin  TaskItemType kotlin  TaskUiState kotlin  
ThemeSettings kotlin  	Throwable kotlin  
TimeDetail kotlin  TimePreferenceItem kotlin  TimeSlotEntity kotlin  	TimeTable kotlin  TimeZone kotlin  TodoUiModel kotlin  TodoUiState kotlin  Triple kotlin  Unit kotlin  
UserEntity kotlin  _rawSchools kotlin  also kotlin  androidx kotlin  arrayListOf kotlin  arrayOf kotlin  asSharedFlow kotlin  asStateFlow kotlin  atTime kotlin  booleanPreferencesKey kotlin  catch kotlin  checkNotNull kotlin  com kotlin  combine kotlin  context kotlin  db kotlin  	emptyList kotlin  emptyMap kotlin  emptyPreferences kotlin  filter kotlin  
filterNotNull kotlin  firstOrNull kotlin  flatMap kotlin  
flatMapLatest kotlin  flow kotlin  flowOf kotlin  getValue kotlin  invoke kotlin  
isNotEmpty kotlin  let kotlin  listOf kotlin  longPreferencesKey kotlin  map kotlin  minus kotlin  mutableStateOf kotlin  plus kotlin  preferencesDataStore kotlin  preferencesDataStoreFile kotlin  provideDelegate kotlin  
receiveAsFlow kotlin  setValue kotlin  sortedBy kotlin  stateIn kotlin  stringPreferencesKey kotlin  takeIf kotlin  to kotlin  	toInstant kotlin  withContext kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getLET 
kotlin.Int  getLet 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  	getTAKEIf 
kotlin.Int  getTO 
kotlin.Int  	getTakeIf 
kotlin.Int  getTo 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getLET kotlin.Long  getLet kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  AddEditOrdinaryScheduleUiState kotlin.annotation  AppConstants kotlin.annotation  	AppRoutes kotlin.annotation  	ArrayList kotlin.annotation  
CalendarUtils kotlin.annotation  Channel kotlin.annotation  ColorScheme kotlin.annotation  ColorSchemeEnum kotlin.annotation  
Converters kotlin.annotation  CoroutineScope kotlin.annotation  CourseDetailItem kotlin.annotation  CourseDetailUiState kotlin.annotation  CourseEntity kotlin.annotation  CourseNodeEntity kotlin.annotation  CreateEditTableUiState kotlin.annotation  DatabaseOperation kotlin.annotation  
DatePeriod kotlin.annotation  DateTimeFormatter kotlin.annotation  Dispatchers kotlin.annotation  EditCourseUiState kotlin.annotation  	Exception kotlin.annotation  ExperimentalCoroutinesApi kotlin.annotation  ExperimentalFoundationApi kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  GlobalTableSettingEntity kotlin.annotation  Header kotlin.annotation  Json kotlin.annotation  	LocalDate kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  LoginUiState kotlin.annotation  Manifest kotlin.annotation  MutableLiveData kotlin.annotation  MutableSharedFlow kotlin.annotation  MutableStateFlow kotlin.annotation  Mutex kotlin.annotation  OnConflictStrategy kotlin.annotation  OrdinaryScheduleDetailUiState kotlin.annotation  OrdinaryScheduleEntity kotlin.annotation  Pair kotlin.annotation  PreferencesKeys kotlin.annotation  QuickAddScheduleUiState kotlin.annotation  Regex kotlin.annotation  RegisterUiState kotlin.annotation  Result kotlin.annotation  SaveCourseState kotlin.annotation  	SaveState kotlin.annotation  ScheduleType kotlin.annotation  ScheduleUiState kotlin.annotation  School kotlin.annotation  SharingStarted kotlin.annotation  SimpleDateFormat kotlin.annotation  SingletonComponent kotlin.annotation  
SupervisorJob kotlin.annotation  SyncMessageEntity kotlin.annotation  	SyncState kotlin.annotation  TableEntity kotlin.annotation  TableTimeConfigEntity kotlin.annotation   TableTimeConfigNodeDetaileEntity kotlin.annotation  TaskCalendarSyncState kotlin.annotation  TaskItemType kotlin.annotation  TaskUiState kotlin.annotation  
ThemeSettings kotlin.annotation  
TimeDetail kotlin.annotation  TimePreferenceItem kotlin.annotation  TimeSlotEntity kotlin.annotation  	TimeTable kotlin.annotation  TimeZone kotlin.annotation  TodoUiModel kotlin.annotation  TodoUiState kotlin.annotation  Triple kotlin.annotation  
UserEntity kotlin.annotation  _rawSchools kotlin.annotation  also kotlin.annotation  androidx kotlin.annotation  arrayListOf kotlin.annotation  arrayOf kotlin.annotation  asSharedFlow kotlin.annotation  asStateFlow kotlin.annotation  atTime kotlin.annotation  booleanPreferencesKey kotlin.annotation  catch kotlin.annotation  checkNotNull kotlin.annotation  com kotlin.annotation  combine kotlin.annotation  context kotlin.annotation  db kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  emptyPreferences kotlin.annotation  filter kotlin.annotation  
filterNotNull kotlin.annotation  firstOrNull kotlin.annotation  flatMap kotlin.annotation  
flatMapLatest kotlin.annotation  flow kotlin.annotation  flowOf kotlin.annotation  getValue kotlin.annotation  invoke kotlin.annotation  
isNotEmpty kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  longPreferencesKey kotlin.annotation  map kotlin.annotation  minus kotlin.annotation  mutableStateOf kotlin.annotation  plus kotlin.annotation  preferencesDataStore kotlin.annotation  preferencesDataStoreFile kotlin.annotation  provideDelegate kotlin.annotation  
receiveAsFlow kotlin.annotation  setValue kotlin.annotation  sortedBy kotlin.annotation  stateIn kotlin.annotation  stringPreferencesKey kotlin.annotation  takeIf kotlin.annotation  to kotlin.annotation  	toInstant kotlin.annotation  withContext kotlin.annotation  AddEditOrdinaryScheduleUiState kotlin.collections  AppConstants kotlin.collections  	AppRoutes kotlin.collections  	ArrayList kotlin.collections  
CalendarUtils kotlin.collections  Channel kotlin.collections  ColorScheme kotlin.collections  ColorSchemeEnum kotlin.collections  
Converters kotlin.collections  CoroutineScope kotlin.collections  CourseDetailItem kotlin.collections  CourseDetailUiState kotlin.collections  CourseEntity kotlin.collections  CourseNodeEntity kotlin.collections  CreateEditTableUiState kotlin.collections  DatabaseOperation kotlin.collections  
DatePeriod kotlin.collections  DateTimeFormatter kotlin.collections  Dispatchers kotlin.collections  EditCourseUiState kotlin.collections  	Exception kotlin.collections  ExperimentalCoroutinesApi kotlin.collections  ExperimentalFoundationApi kotlin.collections  ExperimentalMaterial3Api kotlin.collections  GlobalTableSettingEntity kotlin.collections  Header kotlin.collections  Json kotlin.collections  List kotlin.collections  	LocalDate kotlin.collections  Locale kotlin.collections  Log kotlin.collections  LoginUiState kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  MutableLiveData kotlin.collections  MutableSharedFlow kotlin.collections  MutableStateFlow kotlin.collections  Mutex kotlin.collections  OnConflictStrategy kotlin.collections  OrdinaryScheduleDetailUiState kotlin.collections  OrdinaryScheduleEntity kotlin.collections  Pair kotlin.collections  PreferencesKeys kotlin.collections  QuickAddScheduleUiState kotlin.collections  Regex kotlin.collections  RegisterUiState kotlin.collections  Result kotlin.collections  SaveCourseState kotlin.collections  	SaveState kotlin.collections  ScheduleType kotlin.collections  ScheduleUiState kotlin.collections  School kotlin.collections  Set kotlin.collections  SharingStarted kotlin.collections  SimpleDateFormat kotlin.collections  SingletonComponent kotlin.collections  
SupervisorJob kotlin.collections  SyncMessageEntity kotlin.collections  	SyncState kotlin.collections  TableEntity kotlin.collections  TableTimeConfigEntity kotlin.collections   TableTimeConfigNodeDetaileEntity kotlin.collections  TaskCalendarSyncState kotlin.collections  TaskItemType kotlin.collections  TaskUiState kotlin.collections  
ThemeSettings kotlin.collections  
TimeDetail kotlin.collections  TimePreferenceItem kotlin.collections  TimeSlotEntity kotlin.collections  	TimeTable kotlin.collections  TimeZone kotlin.collections  TodoUiModel kotlin.collections  TodoUiState kotlin.collections  Triple kotlin.collections  
UserEntity kotlin.collections  _rawSchools kotlin.collections  also kotlin.collections  androidx kotlin.collections  arrayListOf kotlin.collections  arrayOf kotlin.collections  asSharedFlow kotlin.collections  asStateFlow kotlin.collections  atTime kotlin.collections  booleanPreferencesKey kotlin.collections  catch kotlin.collections  checkNotNull kotlin.collections  com kotlin.collections  combine kotlin.collections  context kotlin.collections  db kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  emptyPreferences kotlin.collections  filter kotlin.collections  
filterNotNull kotlin.collections  firstOrNull kotlin.collections  flatMap kotlin.collections  
flatMapLatest kotlin.collections  flow kotlin.collections  flowOf kotlin.collections  getValue kotlin.collections  invoke kotlin.collections  
isNotEmpty kotlin.collections  let kotlin.collections  listOf kotlin.collections  longPreferencesKey kotlin.collections  map kotlin.collections  minus kotlin.collections  mutableStateOf kotlin.collections  plus kotlin.collections  preferencesDataStore kotlin.collections  preferencesDataStoreFile kotlin.collections  provideDelegate kotlin.collections  
receiveAsFlow kotlin.collections  setValue kotlin.collections  sortedBy kotlin.collections  stateIn kotlin.collections  stringPreferencesKey kotlin.collections  takeIf kotlin.collections  to kotlin.collections  	toInstant kotlin.collections  withContext kotlin.collections  getALSO kotlin.collections.List  getAlso kotlin.collections.List  	getFILTER kotlin.collections.List  getFIRSTOrNull kotlin.collections.List  
getFLATMap kotlin.collections.List  	getFilter kotlin.collections.List  getFirstOrNull kotlin.collections.List  
getFlatMap kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getPLUS kotlin.collections.List  getPlus kotlin.collections.List  getSORTEDBy kotlin.collections.List  getSortedBy kotlin.collections.List  
isNotEmpty kotlin.collections.List  AddEditOrdinaryScheduleUiState kotlin.comparisons  AppConstants kotlin.comparisons  	AppRoutes kotlin.comparisons  	ArrayList kotlin.comparisons  
CalendarUtils kotlin.comparisons  Channel kotlin.comparisons  ColorScheme kotlin.comparisons  ColorSchemeEnum kotlin.comparisons  
Converters kotlin.comparisons  CoroutineScope kotlin.comparisons  CourseDetailItem kotlin.comparisons  CourseDetailUiState kotlin.comparisons  CourseEntity kotlin.comparisons  CourseNodeEntity kotlin.comparisons  CreateEditTableUiState kotlin.comparisons  DatabaseOperation kotlin.comparisons  
DatePeriod kotlin.comparisons  DateTimeFormatter kotlin.comparisons  Dispatchers kotlin.comparisons  EditCourseUiState kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalCoroutinesApi kotlin.comparisons  ExperimentalFoundationApi kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  GlobalTableSettingEntity kotlin.comparisons  Header kotlin.comparisons  Json kotlin.comparisons  	LocalDate kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  LoginUiState kotlin.comparisons  Manifest kotlin.comparisons  MutableLiveData kotlin.comparisons  MutableSharedFlow kotlin.comparisons  MutableStateFlow kotlin.comparisons  Mutex kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OrdinaryScheduleDetailUiState kotlin.comparisons  OrdinaryScheduleEntity kotlin.comparisons  Pair kotlin.comparisons  PreferencesKeys kotlin.comparisons  QuickAddScheduleUiState kotlin.comparisons  Regex kotlin.comparisons  RegisterUiState kotlin.comparisons  Result kotlin.comparisons  SaveCourseState kotlin.comparisons  	SaveState kotlin.comparisons  ScheduleType kotlin.comparisons  ScheduleUiState kotlin.comparisons  School kotlin.comparisons  SharingStarted kotlin.comparisons  SimpleDateFormat kotlin.comparisons  SingletonComponent kotlin.comparisons  
SupervisorJob kotlin.comparisons  SyncMessageEntity kotlin.comparisons  	SyncState kotlin.comparisons  TableEntity kotlin.comparisons  TableTimeConfigEntity kotlin.comparisons   TableTimeConfigNodeDetaileEntity kotlin.comparisons  TaskCalendarSyncState kotlin.comparisons  TaskItemType kotlin.comparisons  TaskUiState kotlin.comparisons  
ThemeSettings kotlin.comparisons  
TimeDetail kotlin.comparisons  TimePreferenceItem kotlin.comparisons  TimeSlotEntity kotlin.comparisons  	TimeTable kotlin.comparisons  TimeZone kotlin.comparisons  TodoUiModel kotlin.comparisons  TodoUiState kotlin.comparisons  Triple kotlin.comparisons  
UserEntity kotlin.comparisons  _rawSchools kotlin.comparisons  also kotlin.comparisons  androidx kotlin.comparisons  arrayListOf kotlin.comparisons  arrayOf kotlin.comparisons  asSharedFlow kotlin.comparisons  asStateFlow kotlin.comparisons  atTime kotlin.comparisons  booleanPreferencesKey kotlin.comparisons  catch kotlin.comparisons  checkNotNull kotlin.comparisons  com kotlin.comparisons  combine kotlin.comparisons  context kotlin.comparisons  db kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  emptyPreferences kotlin.comparisons  filter kotlin.comparisons  
filterNotNull kotlin.comparisons  firstOrNull kotlin.comparisons  flatMap kotlin.comparisons  
flatMapLatest kotlin.comparisons  flow kotlin.comparisons  flowOf kotlin.comparisons  getValue kotlin.comparisons  invoke kotlin.comparisons  
isNotEmpty kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  longPreferencesKey kotlin.comparisons  map kotlin.comparisons  minus kotlin.comparisons  mutableStateOf kotlin.comparisons  plus kotlin.comparisons  preferencesDataStore kotlin.comparisons  preferencesDataStoreFile kotlin.comparisons  provideDelegate kotlin.comparisons  
receiveAsFlow kotlin.comparisons  setValue kotlin.comparisons  sortedBy kotlin.comparisons  stateIn kotlin.comparisons  stringPreferencesKey kotlin.comparisons  takeIf kotlin.comparisons  to kotlin.comparisons  	toInstant kotlin.comparisons  withContext kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  SuspendFunction3 kotlin.coroutines  SuspendFunction4 kotlin.coroutines  AddEditOrdinaryScheduleUiState 	kotlin.io  AppConstants 	kotlin.io  	AppRoutes 	kotlin.io  	ArrayList 	kotlin.io  
CalendarUtils 	kotlin.io  Channel 	kotlin.io  ColorScheme 	kotlin.io  ColorSchemeEnum 	kotlin.io  
Converters 	kotlin.io  CoroutineScope 	kotlin.io  CourseDetailItem 	kotlin.io  CourseDetailUiState 	kotlin.io  CourseEntity 	kotlin.io  CourseNodeEntity 	kotlin.io  CreateEditTableUiState 	kotlin.io  DatabaseOperation 	kotlin.io  
DatePeriod 	kotlin.io  DateTimeFormatter 	kotlin.io  Dispatchers 	kotlin.io  EditCourseUiState 	kotlin.io  	Exception 	kotlin.io  ExperimentalCoroutinesApi 	kotlin.io  ExperimentalFoundationApi 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  GlobalTableSettingEntity 	kotlin.io  Header 	kotlin.io  Json 	kotlin.io  	LocalDate 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  LoginUiState 	kotlin.io  Manifest 	kotlin.io  MutableLiveData 	kotlin.io  MutableSharedFlow 	kotlin.io  MutableStateFlow 	kotlin.io  Mutex 	kotlin.io  OnConflictStrategy 	kotlin.io  OrdinaryScheduleDetailUiState 	kotlin.io  OrdinaryScheduleEntity 	kotlin.io  Pair 	kotlin.io  PreferencesKeys 	kotlin.io  QuickAddScheduleUiState 	kotlin.io  Regex 	kotlin.io  RegisterUiState 	kotlin.io  Result 	kotlin.io  SaveCourseState 	kotlin.io  	SaveState 	kotlin.io  ScheduleType 	kotlin.io  ScheduleUiState 	kotlin.io  School 	kotlin.io  SharingStarted 	kotlin.io  SimpleDateFormat 	kotlin.io  SingletonComponent 	kotlin.io  
SupervisorJob 	kotlin.io  SyncMessageEntity 	kotlin.io  	SyncState 	kotlin.io  TableEntity 	kotlin.io  TableTimeConfigEntity 	kotlin.io   TableTimeConfigNodeDetaileEntity 	kotlin.io  TaskCalendarSyncState 	kotlin.io  TaskItemType 	kotlin.io  TaskUiState 	kotlin.io  
ThemeSettings 	kotlin.io  
TimeDetail 	kotlin.io  TimePreferenceItem 	kotlin.io  TimeSlotEntity 	kotlin.io  	TimeTable 	kotlin.io  TimeZone 	kotlin.io  TodoUiModel 	kotlin.io  TodoUiState 	kotlin.io  Triple 	kotlin.io  
UserEntity 	kotlin.io  _rawSchools 	kotlin.io  also 	kotlin.io  androidx 	kotlin.io  arrayListOf 	kotlin.io  arrayOf 	kotlin.io  asSharedFlow 	kotlin.io  asStateFlow 	kotlin.io  atTime 	kotlin.io  booleanPreferencesKey 	kotlin.io  catch 	kotlin.io  checkNotNull 	kotlin.io  com 	kotlin.io  combine 	kotlin.io  context 	kotlin.io  db 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  emptyPreferences 	kotlin.io  filter 	kotlin.io  
filterNotNull 	kotlin.io  firstOrNull 	kotlin.io  flatMap 	kotlin.io  
flatMapLatest 	kotlin.io  flow 	kotlin.io  flowOf 	kotlin.io  getValue 	kotlin.io  invoke 	kotlin.io  
isNotEmpty 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  longPreferencesKey 	kotlin.io  map 	kotlin.io  minus 	kotlin.io  mutableStateOf 	kotlin.io  plus 	kotlin.io  preferencesDataStore 	kotlin.io  preferencesDataStoreFile 	kotlin.io  provideDelegate 	kotlin.io  
receiveAsFlow 	kotlin.io  setValue 	kotlin.io  sortedBy 	kotlin.io  stateIn 	kotlin.io  stringPreferencesKey 	kotlin.io  takeIf 	kotlin.io  to 	kotlin.io  	toInstant 	kotlin.io  withContext 	kotlin.io  AddEditOrdinaryScheduleUiState 
kotlin.jvm  AppConstants 
kotlin.jvm  	AppRoutes 
kotlin.jvm  	ArrayList 
kotlin.jvm  
CalendarUtils 
kotlin.jvm  Channel 
kotlin.jvm  ColorScheme 
kotlin.jvm  ColorSchemeEnum 
kotlin.jvm  
Converters 
kotlin.jvm  CoroutineScope 
kotlin.jvm  CourseDetailItem 
kotlin.jvm  CourseDetailUiState 
kotlin.jvm  CourseEntity 
kotlin.jvm  CourseNodeEntity 
kotlin.jvm  CreateEditTableUiState 
kotlin.jvm  DatabaseOperation 
kotlin.jvm  
DatePeriod 
kotlin.jvm  DateTimeFormatter 
kotlin.jvm  Dispatchers 
kotlin.jvm  EditCourseUiState 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalCoroutinesApi 
kotlin.jvm  ExperimentalFoundationApi 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  GlobalTableSettingEntity 
kotlin.jvm  Header 
kotlin.jvm  Json 
kotlin.jvm  	LocalDate 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  LoginUiState 
kotlin.jvm  Manifest 
kotlin.jvm  MutableLiveData 
kotlin.jvm  MutableSharedFlow 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  Mutex 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OrdinaryScheduleDetailUiState 
kotlin.jvm  OrdinaryScheduleEntity 
kotlin.jvm  Pair 
kotlin.jvm  PreferencesKeys 
kotlin.jvm  QuickAddScheduleUiState 
kotlin.jvm  Regex 
kotlin.jvm  RegisterUiState 
kotlin.jvm  Result 
kotlin.jvm  SaveCourseState 
kotlin.jvm  	SaveState 
kotlin.jvm  ScheduleType 
kotlin.jvm  ScheduleUiState 
kotlin.jvm  School 
kotlin.jvm  SharingStarted 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  SingletonComponent 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  SyncMessageEntity 
kotlin.jvm  	SyncState 
kotlin.jvm  TableEntity 
kotlin.jvm  TableTimeConfigEntity 
kotlin.jvm   TableTimeConfigNodeDetaileEntity 
kotlin.jvm  TaskCalendarSyncState 
kotlin.jvm  TaskItemType 
kotlin.jvm  TaskUiState 
kotlin.jvm  
ThemeSettings 
kotlin.jvm  
TimeDetail 
kotlin.jvm  TimePreferenceItem 
kotlin.jvm  TimeSlotEntity 
kotlin.jvm  	TimeTable 
kotlin.jvm  TimeZone 
kotlin.jvm  TodoUiModel 
kotlin.jvm  TodoUiState 
kotlin.jvm  Triple 
kotlin.jvm  
UserEntity 
kotlin.jvm  _rawSchools 
kotlin.jvm  also 
kotlin.jvm  androidx 
kotlin.jvm  arrayListOf 
kotlin.jvm  arrayOf 
kotlin.jvm  asSharedFlow 
kotlin.jvm  asStateFlow 
kotlin.jvm  atTime 
kotlin.jvm  booleanPreferencesKey 
kotlin.jvm  catch 
kotlin.jvm  checkNotNull 
kotlin.jvm  com 
kotlin.jvm  combine 
kotlin.jvm  context 
kotlin.jvm  db 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  emptyPreferences 
kotlin.jvm  filter 
kotlin.jvm  
filterNotNull 
kotlin.jvm  firstOrNull 
kotlin.jvm  flatMap 
kotlin.jvm  
flatMapLatest 
kotlin.jvm  flow 
kotlin.jvm  flowOf 
kotlin.jvm  getValue 
kotlin.jvm  invoke 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  longPreferencesKey 
kotlin.jvm  map 
kotlin.jvm  minus 
kotlin.jvm  mutableStateOf 
kotlin.jvm  plus 
kotlin.jvm  preferencesDataStore 
kotlin.jvm  preferencesDataStoreFile 
kotlin.jvm  provideDelegate 
kotlin.jvm  
receiveAsFlow 
kotlin.jvm  setValue 
kotlin.jvm  sortedBy 
kotlin.jvm  stateIn 
kotlin.jvm  stringPreferencesKey 
kotlin.jvm  takeIf 
kotlin.jvm  to 
kotlin.jvm  	toInstant 
kotlin.jvm  withContext 
kotlin.jvm  max kotlin.math  pow kotlin.math  
roundToInt kotlin.math  ReadOnlyProperty kotlin.properties  getPROVIDEDelegate "kotlin.properties.ReadOnlyProperty  getProvideDelegate "kotlin.properties.ReadOnlyProperty  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  Random 
kotlin.random  AddEditOrdinaryScheduleUiState 
kotlin.ranges  AppConstants 
kotlin.ranges  	AppRoutes 
kotlin.ranges  	ArrayList 
kotlin.ranges  
CalendarUtils 
kotlin.ranges  Channel 
kotlin.ranges  ColorScheme 
kotlin.ranges  ColorSchemeEnum 
kotlin.ranges  
Converters 
kotlin.ranges  CoroutineScope 
kotlin.ranges  CourseDetailItem 
kotlin.ranges  CourseDetailUiState 
kotlin.ranges  CourseEntity 
kotlin.ranges  CourseNodeEntity 
kotlin.ranges  CreateEditTableUiState 
kotlin.ranges  DatabaseOperation 
kotlin.ranges  
DatePeriod 
kotlin.ranges  DateTimeFormatter 
kotlin.ranges  Dispatchers 
kotlin.ranges  EditCourseUiState 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalCoroutinesApi 
kotlin.ranges  ExperimentalFoundationApi 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  GlobalTableSettingEntity 
kotlin.ranges  Header 
kotlin.ranges  Json 
kotlin.ranges  	LocalDate 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  LoginUiState 
kotlin.ranges  Manifest 
kotlin.ranges  MutableLiveData 
kotlin.ranges  MutableSharedFlow 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  Mutex 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OrdinaryScheduleDetailUiState 
kotlin.ranges  OrdinaryScheduleEntity 
kotlin.ranges  Pair 
kotlin.ranges  PreferencesKeys 
kotlin.ranges  QuickAddScheduleUiState 
kotlin.ranges  Regex 
kotlin.ranges  RegisterUiState 
kotlin.ranges  Result 
kotlin.ranges  SaveCourseState 
kotlin.ranges  	SaveState 
kotlin.ranges  ScheduleType 
kotlin.ranges  ScheduleUiState 
kotlin.ranges  School 
kotlin.ranges  SharingStarted 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  SingletonComponent 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  SyncMessageEntity 
kotlin.ranges  	SyncState 
kotlin.ranges  TableEntity 
kotlin.ranges  TableTimeConfigEntity 
kotlin.ranges   TableTimeConfigNodeDetaileEntity 
kotlin.ranges  TaskCalendarSyncState 
kotlin.ranges  TaskItemType 
kotlin.ranges  TaskUiState 
kotlin.ranges  
ThemeSettings 
kotlin.ranges  
TimeDetail 
kotlin.ranges  TimePreferenceItem 
kotlin.ranges  TimeSlotEntity 
kotlin.ranges  	TimeTable 
kotlin.ranges  TimeZone 
kotlin.ranges  TodoUiModel 
kotlin.ranges  TodoUiState 
kotlin.ranges  Triple 
kotlin.ranges  
UserEntity 
kotlin.ranges  _rawSchools 
kotlin.ranges  also 
kotlin.ranges  androidx 
kotlin.ranges  arrayListOf 
kotlin.ranges  arrayOf 
kotlin.ranges  asSharedFlow 
kotlin.ranges  asStateFlow 
kotlin.ranges  atTime 
kotlin.ranges  booleanPreferencesKey 
kotlin.ranges  catch 
kotlin.ranges  checkNotNull 
kotlin.ranges  com 
kotlin.ranges  combine 
kotlin.ranges  context 
kotlin.ranges  db 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  emptyPreferences 
kotlin.ranges  filter 
kotlin.ranges  
filterNotNull 
kotlin.ranges  firstOrNull 
kotlin.ranges  flatMap 
kotlin.ranges  
flatMapLatest 
kotlin.ranges  flow 
kotlin.ranges  flowOf 
kotlin.ranges  getValue 
kotlin.ranges  invoke 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  longPreferencesKey 
kotlin.ranges  map 
kotlin.ranges  minus 
kotlin.ranges  mutableStateOf 
kotlin.ranges  plus 
kotlin.ranges  preferencesDataStore 
kotlin.ranges  preferencesDataStoreFile 
kotlin.ranges  provideDelegate 
kotlin.ranges  
receiveAsFlow 
kotlin.ranges  setValue 
kotlin.ranges  sortedBy 
kotlin.ranges  stateIn 
kotlin.ranges  stringPreferencesKey 
kotlin.ranges  takeIf 
kotlin.ranges  to 
kotlin.ranges  	toInstant 
kotlin.ranges  withContext 
kotlin.ranges  KClass kotlin.reflect  AddEditOrdinaryScheduleUiState kotlin.sequences  AppConstants kotlin.sequences  	AppRoutes kotlin.sequences  	ArrayList kotlin.sequences  
CalendarUtils kotlin.sequences  Channel kotlin.sequences  ColorScheme kotlin.sequences  ColorSchemeEnum kotlin.sequences  
Converters kotlin.sequences  CoroutineScope kotlin.sequences  CourseDetailItem kotlin.sequences  CourseDetailUiState kotlin.sequences  CourseEntity kotlin.sequences  CourseNodeEntity kotlin.sequences  CreateEditTableUiState kotlin.sequences  DatabaseOperation kotlin.sequences  
DatePeriod kotlin.sequences  DateTimeFormatter kotlin.sequences  Dispatchers kotlin.sequences  EditCourseUiState kotlin.sequences  	Exception kotlin.sequences  ExperimentalCoroutinesApi kotlin.sequences  ExperimentalFoundationApi kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  GlobalTableSettingEntity kotlin.sequences  Header kotlin.sequences  Json kotlin.sequences  	LocalDate kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  LoginUiState kotlin.sequences  Manifest kotlin.sequences  MutableLiveData kotlin.sequences  MutableSharedFlow kotlin.sequences  MutableStateFlow kotlin.sequences  Mutex kotlin.sequences  OnConflictStrategy kotlin.sequences  OrdinaryScheduleDetailUiState kotlin.sequences  OrdinaryScheduleEntity kotlin.sequences  Pair kotlin.sequences  PreferencesKeys kotlin.sequences  QuickAddScheduleUiState kotlin.sequences  Regex kotlin.sequences  RegisterUiState kotlin.sequences  Result kotlin.sequences  SaveCourseState kotlin.sequences  	SaveState kotlin.sequences  ScheduleType kotlin.sequences  ScheduleUiState kotlin.sequences  School kotlin.sequences  SharingStarted kotlin.sequences  SimpleDateFormat kotlin.sequences  SingletonComponent kotlin.sequences  
SupervisorJob kotlin.sequences  SyncMessageEntity kotlin.sequences  	SyncState kotlin.sequences  TableEntity kotlin.sequences  TableTimeConfigEntity kotlin.sequences   TableTimeConfigNodeDetaileEntity kotlin.sequences  TaskCalendarSyncState kotlin.sequences  TaskItemType kotlin.sequences  TaskUiState kotlin.sequences  
ThemeSettings kotlin.sequences  
TimeDetail kotlin.sequences  TimePreferenceItem kotlin.sequences  TimeSlotEntity kotlin.sequences  	TimeTable kotlin.sequences  TimeZone kotlin.sequences  TodoUiModel kotlin.sequences  TodoUiState kotlin.sequences  Triple kotlin.sequences  
UserEntity kotlin.sequences  _rawSchools kotlin.sequences  also kotlin.sequences  androidx kotlin.sequences  arrayListOf kotlin.sequences  arrayOf kotlin.sequences  asSharedFlow kotlin.sequences  asStateFlow kotlin.sequences  atTime kotlin.sequences  booleanPreferencesKey kotlin.sequences  catch kotlin.sequences  checkNotNull kotlin.sequences  com kotlin.sequences  combine kotlin.sequences  context kotlin.sequences  db kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  emptyPreferences kotlin.sequences  filter kotlin.sequences  
filterNotNull kotlin.sequences  firstOrNull kotlin.sequences  flatMap kotlin.sequences  
flatMapLatest kotlin.sequences  flow kotlin.sequences  flowOf kotlin.sequences  getValue kotlin.sequences  invoke kotlin.sequences  
isNotEmpty kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  longPreferencesKey kotlin.sequences  map kotlin.sequences  minus kotlin.sequences  mutableStateOf kotlin.sequences  plus kotlin.sequences  preferencesDataStore kotlin.sequences  preferencesDataStoreFile kotlin.sequences  provideDelegate kotlin.sequences  
receiveAsFlow kotlin.sequences  setValue kotlin.sequences  sortedBy kotlin.sequences  stateIn kotlin.sequences  stringPreferencesKey kotlin.sequences  takeIf kotlin.sequences  to kotlin.sequences  	toInstant kotlin.sequences  withContext kotlin.sequences  AddEditOrdinaryScheduleUiState kotlin.text  AppConstants kotlin.text  	AppRoutes kotlin.text  	ArrayList kotlin.text  
CalendarUtils kotlin.text  Channel kotlin.text  ColorScheme kotlin.text  ColorSchemeEnum kotlin.text  
Converters kotlin.text  CoroutineScope kotlin.text  CourseDetailItem kotlin.text  CourseDetailUiState kotlin.text  CourseEntity kotlin.text  CourseNodeEntity kotlin.text  CreateEditTableUiState kotlin.text  DatabaseOperation kotlin.text  
DatePeriod kotlin.text  DateTimeFormatter kotlin.text  Dispatchers kotlin.text  EditCourseUiState kotlin.text  	Exception kotlin.text  ExperimentalCoroutinesApi kotlin.text  ExperimentalFoundationApi kotlin.text  ExperimentalMaterial3Api kotlin.text  GlobalTableSettingEntity kotlin.text  Header kotlin.text  Json kotlin.text  	LocalDate kotlin.text  Locale kotlin.text  Log kotlin.text  LoginUiState kotlin.text  Manifest kotlin.text  MutableLiveData kotlin.text  MutableSharedFlow kotlin.text  MutableStateFlow kotlin.text  Mutex kotlin.text  OnConflictStrategy kotlin.text  OrdinaryScheduleDetailUiState kotlin.text  OrdinaryScheduleEntity kotlin.text  Pair kotlin.text  PreferencesKeys kotlin.text  QuickAddScheduleUiState kotlin.text  Regex kotlin.text  RegisterUiState kotlin.text  Result kotlin.text  SaveCourseState kotlin.text  	SaveState kotlin.text  ScheduleType kotlin.text  ScheduleUiState kotlin.text  School kotlin.text  SharingStarted kotlin.text  SimpleDateFormat kotlin.text  SingletonComponent kotlin.text  
SupervisorJob kotlin.text  SyncMessageEntity kotlin.text  	SyncState kotlin.text  TableEntity kotlin.text  TableTimeConfigEntity kotlin.text   TableTimeConfigNodeDetaileEntity kotlin.text  TaskCalendarSyncState kotlin.text  TaskItemType kotlin.text  TaskUiState kotlin.text  
ThemeSettings kotlin.text  
TimeDetail kotlin.text  TimePreferenceItem kotlin.text  TimeSlotEntity kotlin.text  	TimeTable kotlin.text  TimeZone kotlin.text  TodoUiModel kotlin.text  TodoUiState kotlin.text  Triple kotlin.text  
UserEntity kotlin.text  _rawSchools kotlin.text  also kotlin.text  androidx kotlin.text  arrayListOf kotlin.text  arrayOf kotlin.text  asSharedFlow kotlin.text  asStateFlow kotlin.text  atTime kotlin.text  booleanPreferencesKey kotlin.text  catch kotlin.text  checkNotNull kotlin.text  com kotlin.text  combine kotlin.text  context kotlin.text  db kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  emptyPreferences kotlin.text  filter kotlin.text  
filterNotNull kotlin.text  firstOrNull kotlin.text  flatMap kotlin.text  
flatMapLatest kotlin.text  flow kotlin.text  flowOf kotlin.text  getValue kotlin.text  invoke kotlin.text  
isNotEmpty kotlin.text  let kotlin.text  listOf kotlin.text  longPreferencesKey kotlin.text  map kotlin.text  minus kotlin.text  mutableStateOf kotlin.text  plus kotlin.text  preferencesDataStore kotlin.text  preferencesDataStoreFile kotlin.text  provideDelegate kotlin.text  
receiveAsFlow kotlin.text  setValue kotlin.text  sortedBy kotlin.text  stateIn kotlin.text  stringPreferencesKey kotlin.text  takeIf kotlin.text  to kotlin.text  	toInstant kotlin.text  withContext kotlin.text  invoke kotlin.text.Regex.Companion  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  delay kotlinx.coroutines  isActive kotlinx.coroutines  launch kotlinx.coroutines  runBlocking kotlinx.coroutines  withContext kotlinx.coroutines  plus !kotlinx.coroutines.CompletableJob  AppConstants !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  db !kotlinx.coroutines.CoroutineScope  
getCONTEXT !kotlinx.coroutines.CoroutineScope  
getContext !kotlinx.coroutines.CoroutineScope  getDB !kotlinx.coroutines.CoroutineScope  getDb !kotlinx.coroutines.CoroutineScope  getPREFERENCESDataStoreFile !kotlinx.coroutines.CoroutineScope  getPreferencesDataStoreFile !kotlinx.coroutines.CoroutineScope  preferencesDataStoreFile !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  Channel kotlinx.coroutines.channels  getRECEIVEAsFlow #kotlinx.coroutines.channels.Channel  getReceiveAsFlow #kotlinx.coroutines.channels.Channel  
receiveAsFlow #kotlinx.coroutines.channels.Channel  invoke +kotlinx.coroutines.channels.Channel.Factory  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableSharedFlow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  School kotlinx.coroutines.flow  
SharedFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  _rawSchools kotlinx.coroutines.flow  asSharedFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  
collectLatest kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  distinctUntilChanged kotlinx.coroutines.flow  emptyMap kotlinx.coroutines.flow  
filterNotNull kotlinx.coroutines.flow  first kotlinx.coroutines.flow  firstOrNull kotlinx.coroutines.flow  
flatMapLatest kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow  listOf kotlinx.coroutines.flow  map kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  
receiveAsFlow kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  update kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  catch kotlinx.coroutines.flow.Flow  
filterNotNull kotlinx.coroutines.flow.Flow  
flatMapLatest kotlinx.coroutines.flow.Flow  getCATCH kotlinx.coroutines.flow.Flow  getCatch kotlinx.coroutines.flow.Flow  getFILTERNotNull kotlinx.coroutines.flow.Flow  getFLATMapLatest kotlinx.coroutines.flow.Flow  getFilterNotNull kotlinx.coroutines.flow.Flow  getFlatMapLatest kotlinx.coroutines.flow.Flow  getMAP kotlinx.coroutines.flow.Flow  getMap kotlinx.coroutines.flow.Flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  _rawSchools %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  emptyPreferences %kotlinx.coroutines.flow.FlowCollector  getEMPTYPreferences %kotlinx.coroutines.flow.FlowCollector  getEmptyPreferences %kotlinx.coroutines.flow.FlowCollector  get_rawSchools %kotlinx.coroutines.flow.FlowCollector  asSharedFlow )kotlinx.coroutines.flow.MutableSharedFlow  getASSharedFlow )kotlinx.coroutines.flow.MutableSharedFlow  getAsSharedFlow )kotlinx.coroutines.flow.MutableSharedFlow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  combine (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  
getCOMBINE (kotlinx.coroutines.flow.MutableStateFlow  
getCombine (kotlinx.coroutines.flow.MutableStateFlow  Eagerly &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  Eagerly 0kotlinx.coroutines.flow.SharingStarted.Companion  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  
filterNotNull !kotlinx.coroutines.flow.StateFlow  
flatMapLatest !kotlinx.coroutines.flow.StateFlow  getFILTERNotNull !kotlinx.coroutines.flow.StateFlow  getFLATMapLatest !kotlinx.coroutines.flow.StateFlow  getFilterNotNull !kotlinx.coroutines.flow.StateFlow  getFlatMapLatest !kotlinx.coroutines.flow.StateFlow  getMAP !kotlinx.coroutines.flow.StateFlow  getMap !kotlinx.coroutines.flow.StateFlow  
getSTATEIn !kotlinx.coroutines.flow.StateFlow  
getStateIn !kotlinx.coroutines.flow.StateFlow  map !kotlinx.coroutines.flow.StateFlow  stateIn !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  Mutex kotlinx.coroutines.sync  	Semaphore kotlinx.coroutines.sync  withLock kotlinx.coroutines.sync  Clock kotlinx.datetime  
DatePeriod kotlinx.datetime  DateTimeUnit kotlinx.datetime  	DayOfWeek kotlinx.datetime  Instant kotlinx.datetime  	LocalDate kotlinx.datetime  
LocalDateTime kotlinx.datetime  	LocalTime kotlinx.datetime  TimeZone kotlinx.datetime  atStartOfDayIn kotlinx.datetime  atTime kotlinx.datetime  isoDayNumber kotlinx.datetime  minus kotlinx.datetime  plus kotlinx.datetime  	toInstant kotlinx.datetime  toJavaLocalDate kotlinx.datetime  toJavaLocalDateTime kotlinx.datetime  toJavaLocalTime kotlinx.datetime  toKotlinTimeZone kotlinx.datetime  toLocalDateTime kotlinx.datetime  invoke %kotlinx.datetime.DatePeriod.Companion  toEpochMilliseconds kotlinx.datetime.Instant  atTime kotlinx.datetime.LocalDate  	dayOfWeek kotlinx.datetime.LocalDate  	getATTime kotlinx.datetime.LocalDate  	getAtTime kotlinx.datetime.LocalDate  getMINUS kotlinx.datetime.LocalDate  getMinus kotlinx.datetime.LocalDate  getPLUS kotlinx.datetime.LocalDate  getPlus kotlinx.datetime.LocalDate  minus kotlinx.datetime.LocalDate  plus kotlinx.datetime.LocalDate  invoke $kotlinx.datetime.LocalDate.Companion  getTOInstant kotlinx.datetime.LocalDateTime  getToInstant kotlinx.datetime.LocalDateTime  	toInstant kotlinx.datetime.LocalDateTime  currentSystemDefault kotlinx.datetime.TimeZone  currentSystemDefault #kotlinx.datetime.TimeZone.Companion  	Parcelize kotlinx.parcelize  Serializable kotlinx.serialization  encodeToString kotlinx.serialization  Json kotlinx.serialization.json  JsonBuilder kotlinx.serialization.json  invoke kotlinx.serialization.json.Json  invoke 'kotlinx.serialization.json.Json.Default  encodeDefaults &kotlinx.serialization.json.JsonBuilder  ignoreUnknownKeys &kotlinx.serialization.json.JsonBuilder  	ArrayList 	main.java  Boolean 	main.java  ColorScheme 	main.java  CourseDetailItem 	main.java  	Generator 	main.java  Header 	main.java  Int 	main.java  String 	main.java  TimePreferenceItem 	main.java  listOf 	main.java  map 	main.java  	ArrayList main.java.Generator  Boolean main.java.Generator  ColorScheme main.java.Generator  CourseBaseBean main.java.Generator  CourseDetailBean main.java.Generator  CourseDetailItem main.java.Generator  Header main.java.Generator  Int main.java.Generator  String main.java.Generator  TimePreferenceItem main.java.Generator  	TimeTable main.java.Generator  baseList main.java.Generator  
detailList main.java.Generator  getDefaultTimePreference main.java.Generator  	getLISTOf main.java.Generator  	getListOf main.java.Generator  getMAP main.java.Generator  getMap main.java.Generator  listOf main.java.Generator  map main.java.Generator  maxWeek main.java.Generator  nodes main.java.Generator  	startDate main.java.Generator  	tableName main.java.Generator  	timeTable main.java.Generator  Boolean main.java.Generator.ColorScheme  Int main.java.Generator.ColorScheme  String main.java.Generator.ColorScheme  Boolean $main.java.Generator.CourseDetailItem  Int $main.java.Generator.CourseDetailItem  String $main.java.Generator.CourseDetailItem  Boolean main.java.Generator.Header  Int main.java.Generator.Header  String main.java.Generator.Header  Int &main.java.Generator.TimePreferenceItem  String &main.java.Generator.TimePreferenceItem  Array main.java.parser  	ArrayList main.java.parser  
ImportBean main.java.parser  Int main.java.parser  List main.java.parser  String main.java.parser  
TimeDetail main.java.parser  	TimeTable main.java.parser  	ZZUParser main.java.parser  listOf main.java.parser  Int main.java.parser.ImportBean  String main.java.parser.ImportBean  Array main.java.parser.ZZUParser  	ArrayList main.java.parser.ZZUParser  Course main.java.parser.ZZUParser  
ImportBean main.java.parser.ZZUParser  Int main.java.parser.ZZUParser  List main.java.parser.ZZUParser  String main.java.parser.ZZUParser  
TimeDetail main.java.parser.ZZUParser  	TimeTable main.java.parser.ZZUParser  	getLISTOf main.java.parser.ZZUParser  	getListOf main.java.parser.ZZUParser  listOf main.java.parser.ZZUParser  List main.java.parser.supwisdom  String main.java.parser.supwisdom  SupwisdomParser main.java.parser.supwisdom  Course *main.java.parser.supwisdom.SupwisdomParser  List *main.java.parser.supwisdom.SupwisdomParser  String *main.java.parser.supwisdom.SupwisdomParser  PinyinHelper net.sourceforge.pinyin4j  Interceptor okhttp3  OkHttpClient okhttp3  Request okhttp3  HttpLoggingInterceptor okhttp3.logging  BCrypt org.mindrot.jbcrypt  	ArrayList parser  Int parser  List parser  Parser parser  String parser  arrayListOf parser  Array 
parser.Parser  	ArrayList 
parser.Parser  ColorSchemeEnum 
parser.Parser  Course 
parser.Parser  CourseBaseBean 
parser.Parser  CourseDetailBean 
parser.Parser  
ImportBean 
parser.Parser  Int 
parser.Parser  List 
parser.Parser  ParserResult 
parser.Parser  String 
parser.Parser  
TimeDetail 
parser.Parser  	TimeTable 
parser.Parser  arrayListOf 
parser.Parser  getARRAYListOf 
parser.Parser  getArrayListOf 
parser.Parser  listOf 
parser.Parser  
HttpException 	retrofit2  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  MoshiConverterFactory retrofit2.converter.moshi  Body retrofit2.http  DELETE retrofit2.http  GET retrofit2.http  Header retrofit2.http  POST retrofit2.http  PUT retrofit2.http  Path retrofit2.http  Query retrofit2.http  ExperimentalMaterial3Api +com.example.todoschedule.ui.screens.setting  OptIn +com.example.todoschedule.ui.screens.setting  com (com.example.todoschedule.data.repository  com ;com.example.todoschedule.data.repository.SyncRepositoryImpl  com com.example.todoschedule.di  com &com.example.todoschedule.di.SyncModule  CoroutineScope androidx.lifecycle.ViewModel  Dispatchers androidx.lifecycle.ViewModel  
SupervisorJob androidx.lifecycle.ViewModel  CoroutineScope  com.example.todoschedule.ui.auth  Dispatchers  com.example.todoschedule.ui.auth  
SupervisorJob  com.example.todoschedule.ui.auth  CoroutineScope /com.example.todoschedule.ui.auth.LoginViewModel  Dispatchers /com.example.todoschedule.ui.auth.LoginViewModel  
SupervisorJob /com.example.todoschedule.ui.auth.LoginViewModel  DeviceRegistrationResponseDto 0com.example.todoschedule.data.remote.api.SyncApi  DeviceRegistrationResponseDto &com.example.todoschedule.data.sync.dto  Int Dcom.example.todoschedule.data.sync.dto.DeviceRegistrationResponseDto  Long Dcom.example.todoschedule.data.sync.dto.DeviceRegistrationResponseDto  String Dcom.example.todoschedule.data.sync.dto.DeviceRegistrationResponseDto  Int Ncom.example.todoschedule.data.sync.dto.DeviceRegistrationResponseDto.Companion  Long Ncom.example.todoschedule.data.sync.dto.DeviceRegistrationResponseDto.Companion  String Ncom.example.todoschedule.data.sync.dto.DeviceRegistrationResponseDto.Companion  Suppress (com.example.todoschedule.data.repository  Suppress ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Long "com.example.todoschedule.data.sync  Map "com.example.todoschedule.data.sync  Long .com.example.todoschedule.data.sync.SyncManager  Map .com.example.todoschedule.data.sync.SyncManager  Syncable .com.example.todoschedule.data.sync.SyncManager  SynkAdapter .com.example.todoschedule.data.sync.SyncManager  Long 8com.example.todoschedule.data.sync.SyncManager.Companion  Map 8com.example.todoschedule.data.sync.SyncManager.Companion  Syncable 8com.example.todoschedule.data.sync.SyncManager.Companion  SynkAdapter 8com.example.todoschedule.data.sync.SyncManager.Companion  Suppress kotlin  
JSONObject org.json  SyncManager ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Suppress "com.example.todoschedule.data.sync  Suppress .com.example.todoschedule.data.sync.SyncManager  Suppress 8com.example.todoschedule.data.sync.SyncManager.Companion  Provider ;com.example.todoschedule.data.repository.SyncRepositoryImpl  syncManagerProvider ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Provider 2com.example.todoschedule.data.sync.CrdtKeyResolver  syncRepositoryProvider 2com.example.todoschedule.data.sync.CrdtKeyResolver  Provider <com.example.todoschedule.data.sync.CrdtKeyResolver.Companion  Provider &com.example.todoschedule.di.SyncModule  Provider javax.inject  get javax.inject.Provider  com android.app.Application  com android.content.Context  com android.content.ContextWrapper  com com.example.todoschedule  com 0com.example.todoschedule.TodoScheduleApplication  com :com.example.todoschedule.TodoScheduleApplication.Companion  SessionRepository android.app.Application  SessionRepository 0com.example.todoschedule.TodoScheduleApplication  SessionRepository :com.example.todoschedule.TodoScheduleApplication.Companion  
PrimitiveKind &com.example.todoschedule.data.sync.dto  PrimitiveSerialDescriptor &com.example.todoschedule.data.sync.dto  TimestampDtoSerializer &com.example.todoschedule.data.sync.dto  
SerialName 5com.example.todoschedule.data.sync.dto.SyncMessageDto  Serializable 5com.example.todoschedule.data.sync.dto.SyncMessageDto  TimestampDtoSerializer 5com.example.todoschedule.data.sync.dto.SyncMessageDto  
SerialName ?com.example.todoschedule.data.sync.dto.SyncMessageDto.Companion  Serializable ?com.example.todoschedule.data.sync.dto.SyncMessageDto.Companion  TimestampDtoSerializer ?com.example.todoschedule.data.sync.dto.SyncMessageDto.Companion  Decoder =com.example.todoschedule.data.sync.dto.TimestampDtoSerializer  Encoder =com.example.todoschedule.data.sync.dto.TimestampDtoSerializer  
PrimitiveKind =com.example.todoschedule.data.sync.dto.TimestampDtoSerializer  PrimitiveSerialDescriptor =com.example.todoschedule.data.sync.dto.TimestampDtoSerializer  SerialDescriptor =com.example.todoschedule.data.sync.dto.TimestampDtoSerializer  TimestampDto =com.example.todoschedule.data.sync.dto.TimestampDtoSerializer  
PrimitiveKind 	java.lang  PrimitiveSerialDescriptor 	java.lang  TimestampDtoSerializer 	java.lang  
PrimitiveKind kotlin  PrimitiveSerialDescriptor kotlin  TimestampDtoSerializer kotlin  
PrimitiveKind kotlin.annotation  PrimitiveSerialDescriptor kotlin.annotation  TimestampDtoSerializer kotlin.annotation  
PrimitiveKind kotlin.collections  PrimitiveSerialDescriptor kotlin.collections  TimestampDtoSerializer kotlin.collections  
PrimitiveKind kotlin.comparisons  PrimitiveSerialDescriptor kotlin.comparisons  TimestampDtoSerializer kotlin.comparisons  
PrimitiveKind 	kotlin.io  PrimitiveSerialDescriptor 	kotlin.io  TimestampDtoSerializer 	kotlin.io  
PrimitiveKind 
kotlin.jvm  PrimitiveSerialDescriptor 
kotlin.jvm  TimestampDtoSerializer 
kotlin.jvm  
PrimitiveKind 
kotlin.ranges  PrimitiveSerialDescriptor 
kotlin.ranges  TimestampDtoSerializer 
kotlin.ranges  
PrimitiveKind kotlin.sequences  PrimitiveSerialDescriptor kotlin.sequences  TimestampDtoSerializer kotlin.sequences  
PrimitiveKind kotlin.text  PrimitiveSerialDescriptor kotlin.text  TimestampDtoSerializer kotlin.text  KSerializer kotlinx.serialization  
SerialName kotlinx.serialization  
PrimitiveKind !kotlinx.serialization.descriptors  PrimitiveSerialDescriptor !kotlinx.serialization.descriptors  SerialDescriptor !kotlinx.serialization.descriptors  LONG /kotlinx.serialization.descriptors.PrimitiveKind  Decoder kotlinx.serialization.encoding  Encoder kotlinx.serialization.encoding  Json (com.example.todoschedule.data.repository  Json ;com.example.todoschedule.data.repository.SyncRepositoryImpl  invoke ;com.example.todoschedule.data.repository.SyncRepositoryImpl  Json .com.example.todoschedule.data.sync.SyncApiImpl  invoke .com.example.todoschedule.data.sync.SyncApiImpl  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  DatabaseMigrations 0com.example.todoschedule.data.database.migration  	Migration Ccom.example.todoschedule.data.database.migration.DatabaseMigrations  SupportSQLiteDatabase Ccom.example.todoschedule.data.database.migration.DatabaseMigrations  SyncApiImpl (com.example.todoschedule.data.remote.api  TAG (com.example.todoschedule.data.repository  syncAllEnhanced (com.example.todoschedule.data.repository  syncEntityTypeEnhanced (com.example.todoschedule.data.repository  uploadMessagesEnhanced (com.example.todoschedule.data.repository  SyncMessageUploader "com.example.todoschedule.data.sync  DeviceIdManager 6com.example.todoschedule.data.sync.SyncMessageUploader  Inject 6com.example.todoschedule.data.sync.SyncMessageUploader  Json 6com.example.todoschedule.data.sync.SyncMessageUploader  List 6com.example.todoschedule.data.sync.SyncMessageUploader  String 6com.example.todoschedule.data.sync.SyncMessageUploader  SyncApi 6com.example.todoschedule.data.sync.SyncMessageUploader  SyncMessageDao 6com.example.todoschedule.data.sync.SyncMessageUploader  SyncMessageEntity 6com.example.todoschedule.data.sync.SyncMessageUploader  invoke 6com.example.todoschedule.data.sync.SyncMessageUploader  SyncModuleExtensions com.example.todoschedule.di  AppDatabase 0com.example.todoschedule.di.SyncModuleExtensions  DeviceIdManager 0com.example.todoschedule.di.SyncModuleExtensions  Provides 0com.example.todoschedule.di.SyncModuleExtensions  
RemoteSyncApi 0com.example.todoschedule.di.SyncModuleExtensions  	Singleton 0com.example.todoschedule.di.SyncModuleExtensions  SyncMessageUploader 0com.example.todoschedule.di.SyncModuleExtensions  Boolean com.example.todoschedule.util  Int com.example.todoschedule.util  Long com.example.todoschedule.util  NetworkUtils com.example.todoschedule.util  Boolean *com.example.todoschedule.util.NetworkUtils  Int *com.example.todoschedule.util.NetworkUtils  Long *com.example.todoschedule.util.NetworkUtils  String *com.example.todoschedule.util.NetworkUtils  SyncMessageUploader &com.example.todoschedule.di.SyncModule  toDto -com.example.todoschedule.data.database.entity  
withStatus -com.example.todoschedule.data.database.entity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            