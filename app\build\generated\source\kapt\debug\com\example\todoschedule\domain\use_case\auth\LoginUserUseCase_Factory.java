// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.auth;

import com.example.todoschedule.domain.repository.RemoteUserRepository;
import com.example.todoschedule.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LoginUserUseCase_Factory implements Factory<LoginUserUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<RemoteUserRepository> remoteUserRepositoryProvider;

  private final Provider<VerifyPasswordUseCase> verifyPasswordUseCaseProvider;

  public LoginUserUseCase_Factory(Provider<UserRepository> userRepositoryProvider,
      Provider<RemoteUserRepository> remoteUserRepositoryProvider,
      Provider<VerifyPasswordUseCase> verifyPasswordUseCaseProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.remoteUserRepositoryProvider = remoteUserRepositoryProvider;
    this.verifyPasswordUseCaseProvider = verifyPasswordUseCaseProvider;
  }

  @Override
  public LoginUserUseCase get() {
    return newInstance(userRepositoryProvider.get(), remoteUserRepositoryProvider.get(), verifyPasswordUseCaseProvider.get());
  }

  public static LoginUserUseCase_Factory create(Provider<UserRepository> userRepositoryProvider,
      Provider<RemoteUserRepository> remoteUserRepositoryProvider,
      Provider<VerifyPasswordUseCase> verifyPasswordUseCaseProvider) {
    return new LoginUserUseCase_Factory(userRepositoryProvider, remoteUserRepositoryProvider, verifyPasswordUseCaseProvider);
  }

  public static LoginUserUseCase newInstance(UserRepository userRepository,
      RemoteUserRepository remoteUserRepository, VerifyPasswordUseCase verifyPasswordUseCase) {
    return new LoginUserUseCase(userRepository, remoteUserRepository, verifyPasswordUseCase);
  }
}
