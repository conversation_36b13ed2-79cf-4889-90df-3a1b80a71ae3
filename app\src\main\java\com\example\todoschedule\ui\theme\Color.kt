package com.example.todoschedule.ui.theme

import android.util.Log
import androidx.compose.material3.ColorScheme
import androidx.compose.ui.graphics.Color
import androidx.core.graphics.toColorInt
import com.example.todoschedule.core.constants.AppConstants

val primaryLight = Color(0xFF3E6700)
val onPrimaryLight = Color(0xFFFFFFFF)
val primaryContainerLight = Color(0xFF4F8200)
val onPrimaryContainerLight = Color(0xFFF9FFEA)
val secondaryLight = Color(0xFF4B662A)
val onSecondaryLight = Color(0xFFFFFFFF)
val secondaryContainerLight = Color(0xFFCCEEA1)
val onSecondaryContainerLight = Color(0xFF516D2F)
val tertiaryLight = Color(0xFF00694E)
val onTertiaryLight = Color(0xFFFFFFFF)
val tertiaryContainerLight = Color(0xFF008564)
val onTertiaryContainerLight = Color(0xFFF5FFF8)
val errorLight = Color(0xFFBA1A1A)
val onErrorLight = Color(0xFFFFFFFF)
val errorContainerLight = Color(0xFFFFDAD6)
val onErrorContainerLight = Color(0xFF93000A)
val backgroundLight = Color(0xFFF7FBEA)
val onBackgroundLight = Color(0xFF191D13)
val surfaceLight = Color(0xFFF7FBEA)
val onSurfaceLight = Color(0xFF191D13)
val surfaceVariantLight = Color(0xFFDEE6CD)
val onSurfaceVariantLight = Color(0xFF424937)
val outlineLight = Color(0xFF727A66)
val outlineVariantLight = Color(0xFFC2CAB2)
val scrimLight = Color(0xFF000000)
val inverseSurfaceLight = Color(0xFF2E3227)
val inverseOnSurfaceLight = Color(0xFFEFF3E2)
val inversePrimaryLight = Color(0xFF97D945)
val surfaceDimLight = Color(0xFFD8DCCC)
val surfaceBrightLight = Color(0xFFF7FBEA)
val surfaceContainerLowestLight = Color(0xFFFFFFFF)
val surfaceContainerLowLight = Color(0xFFF2F5E5)
val surfaceContainerLight = Color(0xFFECF0DF)
val surfaceContainerHighLight = Color(0xFFE6EADA)
val surfaceContainerHighestLight = Color(0xFFE0E4D4)

val primaryLightMediumContrast = Color(0xFF233D00)
val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
val primaryContainerLightMediumContrast = Color(0xFF4A7A00)
val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
val secondaryLightMediumContrast = Color(0xFF243D03)
val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
val secondaryContainerLightMediumContrast = Color(0xFF597637)
val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
val tertiaryLightMediumContrast = Color(0xFF003F2D)
val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
val tertiaryContainerLightMediumContrast = Color(0xFF007C5D)
val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
val errorLightMediumContrast = Color(0xFF740006)
val onErrorLightMediumContrast = Color(0xFFFFFFFF)
val errorContainerLightMediumContrast = Color(0xFFCF2C27)
val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
val backgroundLightMediumContrast = Color(0xFFF7FBEA)
val onBackgroundLightMediumContrast = Color(0xFF191D13)
val surfaceLightMediumContrast = Color(0xFFF7FBEA)
val onSurfaceLightMediumContrast = Color(0xFF0E1209)
val surfaceVariantLightMediumContrast = Color(0xFFDEE6CD)
val onSurfaceVariantLightMediumContrast = Color(0xFF313928)
val outlineLightMediumContrast = Color(0xFF4E5543)
val outlineVariantLightMediumContrast = Color(0xFF68705C)
val scrimLightMediumContrast = Color(0xFF000000)
val inverseSurfaceLightMediumContrast = Color(0xFF2E3227)
val inverseOnSurfaceLightMediumContrast = Color(0xFFEFF3E2)
val inversePrimaryLightMediumContrast = Color(0xFF97D945)
val surfaceDimLightMediumContrast = Color(0xFFC4C8B9)
val surfaceBrightLightMediumContrast = Color(0xFFF7FBEA)
val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
val surfaceContainerLowLightMediumContrast = Color(0xFFF2F5E5)
val surfaceContainerLightMediumContrast = Color(0xFFE6EADA)
val surfaceContainerHighLightMediumContrast = Color(0xFFDBDFCE)
val surfaceContainerHighestLightMediumContrast = Color(0xFFCFD3C4)

val primaryLightHighContrast = Color(0xFF1C3200)
val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
val primaryContainerLightHighContrast = Color(0xFF305200)
val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
val secondaryLightHighContrast = Color(0xFF1C3200)
val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
val secondaryContainerLightHighContrast = Color(0xFF365116)
val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
val tertiaryLightHighContrast = Color(0xFF003325)
val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
val tertiaryContainerLightHighContrast = Color(0xFF00543E)
val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
val errorLightHighContrast = Color(0xFF600004)
val onErrorLightHighContrast = Color(0xFFFFFFFF)
val errorContainerLightHighContrast = Color(0xFF98000A)
val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
val backgroundLightHighContrast = Color(0xFFF7FBEA)
val onBackgroundLightHighContrast = Color(0xFF191D13)
val surfaceLightHighContrast = Color(0xFFF7FBEA)
val onSurfaceLightHighContrast = Color(0xFF000000)
val surfaceVariantLightHighContrast = Color(0xFFDEE6CD)
val onSurfaceVariantLightHighContrast = Color(0xFF000000)
val outlineLightHighContrast = Color(0xFF272E1E)
val outlineVariantLightHighContrast = Color(0xFF444C3A)
val scrimLightHighContrast = Color(0xFF000000)
val inverseSurfaceLightHighContrast = Color(0xFF2E3227)
val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
val inversePrimaryLightHighContrast = Color(0xFF97D945)
val surfaceDimLightHighContrast = Color(0xFFB6BBAB)
val surfaceBrightLightHighContrast = Color(0xFFF7FBEA)
val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
val surfaceContainerLowLightHighContrast = Color(0xFFEFF3E2)
val surfaceContainerLightHighContrast = Color(0xFFE0E4D4)
val surfaceContainerHighLightHighContrast = Color(0xFFD2D6C6)
val surfaceContainerHighestLightHighContrast = Color(0xFFC4C8B9)

val primaryDark = Color(0xFF97D945)
val onPrimaryDark = Color(0xFF1F3700)
val primaryContainerDark = Color(0xFF64A104)
val onPrimaryContainerDark = Color(0xFF192F00)
val secondaryDark = Color(0xFFB1D188)
val onSecondaryDark = Color(0xFF1F3700)
val secondaryContainerDark = Color(0xFF365016)
val onSecondaryContainerDark = Color(0xFFA3C37B)
val tertiaryDark = Color(0xFF5DDCB0)
val onTertiaryDark = Color(0xFF003828)
val tertiaryContainerDark = Color(0xFF05A47C)
val onTertiaryContainerDark = Color(0xFF002F21)
val errorDark = Color(0xFFFFB4AB)
val onErrorDark = Color(0xFF690005)
val errorContainerDark = Color(0xFF93000A)
val onErrorContainerDark = Color(0xFFFFDAD6)
val backgroundDark = Color(0xFF11150B)
val onBackgroundDark = Color(0xFFE0E4D4)
val surfaceDark = Color(0xFF11150B)
val onSurfaceDark = Color(0xFFE0E4D4)
val surfaceVariantDark = Color(0xFF424937)
val onSurfaceVariantDark = Color(0xFFC2CAB2)
val outlineDark = Color(0xFF8C947E)
val outlineVariantDark = Color(0xFF424937)
val scrimDark = Color(0xFF000000)
val inverseSurfaceDark = Color(0xFFE0E4D4)
val inverseOnSurfaceDark = Color(0xFF2E3227)
val inversePrimaryDark = Color(0xFF3F6900)
val surfaceDimDark = Color(0xFF11150B)
val surfaceBrightDark = Color(0xFF363B2F)
val surfaceContainerLowestDark = Color(0xFF0B0F07)
val surfaceContainerLowDark = Color(0xFF191D13)
val surfaceContainerDark = Color(0xFF1D2117)
val surfaceContainerHighDark = Color(0xFF272B21)
val surfaceContainerHighestDark = Color(0xFF32362B)

val primaryDarkMediumContrast = Color(0xFFACF05A)
val onPrimaryDarkMediumContrast = Color(0xFF172B00)
val primaryContainerDarkMediumContrast = Color(0xFF64A104)
val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
val secondaryDarkMediumContrast = Color(0xFFC6E79C)
val onSecondaryDarkMediumContrast = Color(0xFF172B00)
val secondaryContainerDarkMediumContrast = Color(0xFF7C9A57)
val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
val tertiaryDarkMediumContrast = Color(0xFF75F3C5)
val onTertiaryDarkMediumContrast = Color(0xFF002C1F)
val tertiaryContainerDarkMediumContrast = Color(0xFF05A47C)
val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
val errorDarkMediumContrast = Color(0xFFFFD2CC)
val onErrorDarkMediumContrast = Color(0xFF540003)
val errorContainerDarkMediumContrast = Color(0xFFFF5449)
val onErrorContainerDarkMediumContrast = Color(0xFF000000)
val backgroundDarkMediumContrast = Color(0xFF11150B)
val onBackgroundDarkMediumContrast = Color(0xFFE0E4D4)
val surfaceDarkMediumContrast = Color(0xFF11150B)
val onSurfaceDarkMediumContrast = Color(0xFFFFFFFF)
val surfaceVariantDarkMediumContrast = Color(0xFF424937)
val onSurfaceVariantDarkMediumContrast = Color(0xFFD8DFC7)
val outlineDarkMediumContrast = Color(0xFFADB59E)
val outlineVariantDarkMediumContrast = Color(0xFF8B937E)
val scrimDarkMediumContrast = Color(0xFF000000)
val inverseSurfaceDarkMediumContrast = Color(0xFFE0E4D4)
val inverseOnSurfaceDarkMediumContrast = Color(0xFF272B21)
val inversePrimaryDarkMediumContrast = Color(0xFF2F5100)
val surfaceDimDarkMediumContrast = Color(0xFF11150B)
val surfaceBrightDarkMediumContrast = Color(0xFF42463A)
val surfaceContainerLowestDarkMediumContrast = Color(0xFF050803)
val surfaceContainerLowDarkMediumContrast = Color(0xFF1B1F15)
val surfaceContainerDarkMediumContrast = Color(0xFF25291F)
val surfaceContainerHighDarkMediumContrast = Color(0xFF303429)
val surfaceContainerHighestDarkMediumContrast = Color(0xFF3B3F34)

val primaryDarkHighContrast = Color(0xFFCFFF95)
val onPrimaryDarkHighContrast = Color(0xFF000000)
val primaryContainerDarkHighContrast = Color(0xFF93D542)
val onPrimaryContainerDarkHighContrast = Color(0xFF050E00)
val secondaryDarkHighContrast = Color(0xFFD9FBAE)
val onSecondaryDarkHighContrast = Color(0xFF000000)
val secondaryContainerDarkHighContrast = Color(0xFFADCD84)
val onSecondaryContainerDarkHighContrast = Color(0xFF050E00)
val tertiaryDarkHighContrast = Color(0xFFB7FFE0)
val onTertiaryDarkHighContrast = Color(0xFF000000)
val tertiaryContainerDarkHighContrast = Color(0xFF59D8AC)
val onTertiaryContainerDarkHighContrast = Color(0xFF000E08)
val errorDarkHighContrast = Color(0xFFFFECE9)
val onErrorDarkHighContrast = Color(0xFF000000)
val errorContainerDarkHighContrast = Color(0xFFFFAEA4)
val onErrorContainerDarkHighContrast = Color(0xFF220001)
val backgroundDarkHighContrast = Color(0xFF11150B)
val onBackgroundDarkHighContrast = Color(0xFFE0E4D4)
val surfaceDarkHighContrast = Color(0xFF11150B)
val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
val surfaceVariantDarkHighContrast = Color(0xFF424937)
val onSurfaceVariantDarkHighContrast = Color(0xFFFFFFFF)
val outlineDarkHighContrast = Color(0xFFEBF3DA)
val outlineVariantDarkHighContrast = Color(0xFFBEC6AE)
val scrimDarkHighContrast = Color(0xFF000000)
val inverseSurfaceDarkHighContrast = Color(0xFFE0E4D4)
val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
val inversePrimaryDarkHighContrast = Color(0xFF2F5100)
val surfaceDimDarkHighContrast = Color(0xFF11150B)
val surfaceBrightDarkHighContrast = Color(0xFF4D5245)
val surfaceContainerLowestDarkHighContrast = Color(0xFF000000)
val surfaceContainerLowDarkHighContrast = Color(0xFF1D2117)
val surfaceContainerDarkHighContrast = Color(0xFF2E3227)
val surfaceContainerHighDarkHighContrast = Color(0xFF393D32)
val surfaceContainerHighestDarkHighContrast = Color(0xFF44483C)

sealed class ColorSchemeEnum {
    // 固定颜色
    data class Fixed(val color: Color) : ColorSchemeEnum()

    // Material3 主题颜色
    object PRIMARY : ColorSchemeEnum()
    object ONPRIMARY : ColorSchemeEnum()
    object PRIMARYCONTAINER : ColorSchemeEnum()
    object ONPRIMARYCONTAINER : ColorSchemeEnum()
    object INVERSEPRIMARY : ColorSchemeEnum()
    object SECONDARY : ColorSchemeEnum()
    object ONSECONDARY : ColorSchemeEnum()
    object SECONDARYCONTAINER : ColorSchemeEnum()
    object ONSECONDARYCONTAINER : ColorSchemeEnum()
    object TERTIARY : ColorSchemeEnum()
    object ONTERTIARY : ColorSchemeEnum()
    object TERTIARYCONTAINER : ColorSchemeEnum()
    object ONTERTIARYCONTAINER : ColorSchemeEnum()
    object BACKGROUND : ColorSchemeEnum()
    object ONBACKGROUND : ColorSchemeEnum()
    object SURFACE : ColorSchemeEnum()
    object ONSURFACE : ColorSchemeEnum()
    object SURFACEVARIANT : ColorSchemeEnum()
    object ONSURFACEVARIANT : ColorSchemeEnum()
    object SURFACETINT : ColorSchemeEnum()
    object INVERSESURFACE : ColorSchemeEnum()
    object INVERSEONSURFACE : ColorSchemeEnum()
    object ERROR : ColorSchemeEnum()
    object ONERROR : ColorSchemeEnum()
    object ERRORCONTAINER : ColorSchemeEnum()
    object ONERRORCONTAINER : ColorSchemeEnum()
    object OUTLINE : ColorSchemeEnum()
    object OUTLINEVARIANT : ColorSchemeEnum()
    object SCRIM : ColorSchemeEnum()
    object SURFACEBRIGHT : ColorSchemeEnum()
    object SURFACEDIM : ColorSchemeEnum()
    object SURFACECONTAINER : ColorSchemeEnum()
    object SURFACECONTAINERHIGH : ColorSchemeEnum()
    object SURFACECONTAINERHIGHEST : ColorSchemeEnum()
    object SURFACECONTAINERLOW : ColorSchemeEnum()
    object SURFACECONTAINERLOWEST : ColorSchemeEnum()

    // 获取颜色
    fun toColor(colorScheme: ColorScheme): Color {
        Log.w("ColorSchemeEnum", "toColor: $this")
        return when (this) {
            is Fixed -> color
            PRIMARY -> colorScheme.primary
            ONPRIMARY -> colorScheme.onPrimary
            PRIMARYCONTAINER -> colorScheme.primaryContainer
            ONPRIMARYCONTAINER -> colorScheme.onPrimaryContainer
            INVERSEPRIMARY -> colorScheme.inversePrimary
            SECONDARY -> colorScheme.secondary
            ONSECONDARY -> colorScheme.onSecondary
            SECONDARYCONTAINER -> colorScheme.secondaryContainer
            ONSECONDARYCONTAINER -> colorScheme.onSecondaryContainer
            TERTIARY -> colorScheme.tertiary
            ONTERTIARY -> colorScheme.onTertiary
            TERTIARYCONTAINER -> colorScheme.tertiaryContainer
            ONTERTIARYCONTAINER -> colorScheme.onTertiaryContainer
            BACKGROUND -> colorScheme.background
            ONBACKGROUND -> colorScheme.onBackground
            SURFACE -> colorScheme.surface
            ONSURFACE -> colorScheme.onSurface
            SURFACEVARIANT -> colorScheme.surfaceVariant
            ONSURFACEVARIANT -> colorScheme.onSurfaceVariant
            SURFACETINT -> colorScheme.surfaceTint
            INVERSESURFACE -> colorScheme.inverseSurface
            INVERSEONSURFACE -> colorScheme.inverseOnSurface
            ERROR -> colorScheme.error
            ONERROR -> colorScheme.onError
            ERRORCONTAINER -> colorScheme.errorContainer
            ONERRORCONTAINER -> colorScheme.onErrorContainer
            OUTLINE -> colorScheme.outline
            OUTLINEVARIANT -> colorScheme.outlineVariant
            SCRIM -> colorScheme.scrim
            SURFACEBRIGHT -> colorScheme.surfaceBright
            SURFACEDIM -> colorScheme.surfaceDim
            SURFACECONTAINER -> colorScheme.surfaceContainer
            SURFACECONTAINERHIGH -> colorScheme.surfaceContainerHigh
            SURFACECONTAINERHIGHEST -> colorScheme.surfaceContainerHighest
            SURFACECONTAINERLOW -> colorScheme.surfaceContainerLow
            SURFACECONTAINERLOWEST -> colorScheme.surfaceContainerLowest
        }
    }

    override fun toString(): String {
        return when (this) {
            BACKGROUND -> "BACKGROUND"
            ERROR -> "ERROR"
            ERRORCONTAINER -> "ERRORCONTAINER"
            is Fixed -> "#${color.to16Bit()}"
            INVERSEONSURFACE -> "INVERSEONSURFACE"
            INVERSEPRIMARY -> "INVERSEPRIMARY"
            INVERSESURFACE -> "INVERSESURFACE"
            ONBACKGROUND -> "ONBACKGROUND"
            ONERROR -> "ONERROR"
            ONERRORCONTAINER -> "ONERRORCONTAINER"
            ONPRIMARY -> "ONPRIMARY"
            ONPRIMARYCONTAINER -> "ONPRIMARYCONTAINER"
            ONSECONDARY -> "ONSECONDARY"
            ONSECONDARYCONTAINER -> "ONSECONDARYCONTAINER"
            ONSURFACE -> "ONSURFACE"
            ONSURFACEVARIANT -> "ONSURFACEVARIANT"
            ONTERTIARY -> "ONTERTIARY"
            ONTERTIARYCONTAINER -> "ONTERTIARYCONTAINER"
            OUTLINE -> "OUTLINE"
            OUTLINEVARIANT -> "OUTLINEVARIANT"
            PRIMARY -> "PRIMARY"
            PRIMARYCONTAINER -> "PRIMARYCONTAINER"
            SCRIM -> "SCRIM"
            SECONDARY -> "SECONDARY"
            SECONDARYCONTAINER -> "SECONDARYCONTAINER"
            SURFACE -> "SURFACE"
            SURFACEBRIGHT -> "SURFACEBRIGHT"
            SURFACECONTAINER -> "SURFACECONTAINER"
            SURFACECONTAINERHIGH -> "SURFACECONTAINERHIGH"
            SURFACECONTAINERHIGHEST -> "SURFACECONTAINERHIGHEST"
            SURFACECONTAINERLOW -> "SURFACECONTAINERLOW"
            SURFACECONTAINERLOWEST -> "SURFACECONTAINERLOWEST"
            SURFACEDIM -> "SURFACEDIM"
            SURFACETINT -> "SURFACETINT"
            SURFACEVARIANT -> "SURFACEVARIANT"
            TERTIARY -> "TERTIARY"
            TERTIARYCONTAINER -> "TERTIARYCONTAINER"
        }
    }

    companion object {
        fun getColorSchemeEnumList(): List<ColorSchemeEnum> {
            return listOf(
                BACKGROUND,
                ERROR,
                ERRORCONTAINER,
                INVERSEONSURFACE,
                INVERSEPRIMARY,
                INVERSESURFACE,
                ONBACKGROUND,
                ONERROR,
                ONERRORCONTAINER,
                ONPRIMARY,
                ONPRIMARYCONTAINER,
                ONSECONDARY,
                ONSECONDARYCONTAINER,
                ONSURFACE,
                ONSURFACEVARIANT,
                ONTERTIARY,
                ONTERTIARYCONTAINER,
                OUTLINE,
                OUTLINEVARIANT,
                PRIMARY,
                PRIMARYCONTAINER,
                SCRIM,
                SECONDARY,
                SECONDARYCONTAINER,
                SURFACE,
                SURFACEBRIGHT,
                SURFACECONTAINER,
                SURFACECONTAINERHIGH,
                SURFACECONTAINERHIGHEST,
                SURFACECONTAINERLOW,
                SURFACECONTAINERLOWEST,
                SURFACEDIM,
                SURFACETINT,
                SURFACEVARIANT,
                TERTIARY,
                TERTIARYCONTAINER,
            )
        }

        // 从字符串转换为枚举
        fun fromString(value: String): ColorSchemeEnum? {
            Log.w("ColorSchemeEnum", "fromString: $value")
            // 尝试解析为十六进制颜色
            return if (value.startsWith("#")) {
                Fixed(value.toColor())
            } else {
                // 匹配预定义颜色名称
                when (value.uppercase()) {
                    "PRIMARY" -> PRIMARY
                    "ONPRIMARY" -> ONPRIMARY
                    "PRIMARYCONTAINER" -> PRIMARYCONTAINER
                    "ONPRIMARYCONTAINER" -> ONPRIMARYCONTAINER
                    "INVERSEPRIMARY" -> INVERSEPRIMARY
                    "SECONDARY" -> SECONDARY
                    "ONSECONDARY" -> ONSECONDARY
                    "SECONDARYCONTAINER" -> SECONDARYCONTAINER
                    "ONSECONDARYCONTAINER" -> ONSECONDARYCONTAINER
                    "TERTIARY" -> TERTIARY
                    "ONTERTIARY" -> ONTERTIARY
                    "TERTIARYCONTAINER" -> TERTIARYCONTAINER
                    "ONTERTIARYCONTAINER" -> ONTERTIARYCONTAINER
                    "BACKGROUND" -> BACKGROUND
                    "ONBACKGROUND" -> ONBACKGROUND
                    "SURFACE" -> SURFACE
                    "ONSURFACE" -> ONSURFACE
                    "SURFACEVARIANT" -> SURFACEVARIANT
                    "ONSURFACEVARIANT" -> ONSURFACEVARIANT
                    "SURFACETINT" -> SURFACETINT
                    "INVERSESURFACE" -> INVERSESURFACE
                    "INVERSEONSURFACE" -> INVERSEONSURFACE
                    "ERROR" -> ERROR
                    "ONERROR" -> ONERROR
                    "ERRORCONTAINER" -> ERRORCONTAINER
                    "ONERRORCONTAINER" -> ONERRORCONTAINER
                    "OUTLINE" -> OUTLINE
                    "OUTLINEVARIANT" -> OUTLINEVARIANT
                    "SCRIM" -> SCRIM
                    "SURFACEBRIGHT" -> SURFACEBRIGHT
                    "SURFACEDIM" -> SURFACEDIM
                    "SURFACECONTAINER" -> SURFACECONTAINER
                    "SURFACECONTAINERHIGH" -> SURFACECONTAINERHIGH
                    "SURFACECONTAINERHIGHEST" -> SURFACECONTAINERHIGHEST
                    "SURFACECONTAINERLOW" -> SURFACECONTAINERLOW
                    "SURFACECONTAINERLOWEST" -> SURFACECONTAINERLOWEST
                    else -> null // 默认黑色
                }
            }
        }

        fun getOnSomethingColorList(): List<ColorSchemeEnum> {
            return listOf(
                ONPRIMARY,
                ONPRIMARYCONTAINER,
                ONSECONDARY,
                ONSECONDARYCONTAINER,
                ONTERTIARY,
                ONTERTIARYCONTAINER,
                ONBACKGROUND,
                ONSURFACE,
                ONSURFACEVARIANT,
                INVERSEONSURFACE,
                ONERROR,
                ONERRORCONTAINER,
            )
        }

        fun getColorList(): List<ColorSchemeEnum> {
            return listOf(
                PRIMARY,
                PRIMARYCONTAINER,
                SECONDARY,
                SECONDARYCONTAINER,
                TERTIARY,
                TERTIARYCONTAINER,
                BACKGROUND,
                SURFACE,
                SURFACEVARIANT,
                INVERSESURFACE,
                ERROR,
                ERRORCONTAINER,
            )
        }
    }
}

fun getColorListFromColorScheme(colorScheme: ColorScheme): List<Color> = listOf(
    colorScheme.primary,
    colorScheme.onPrimary,
    colorScheme.primaryContainer,
    colorScheme.onPrimaryContainer,
    colorScheme.inversePrimary,
    colorScheme.secondary,
    colorScheme.onSecondary,
    colorScheme.secondaryContainer,
    colorScheme.onSecondaryContainer,
    colorScheme.tertiary,
    colorScheme.onTertiary,
    colorScheme.tertiaryContainer,
    colorScheme.onTertiaryContainer,
    colorScheme.background,
    colorScheme.onBackground,
    colorScheme.surface,
    colorScheme.onSurface,
    colorScheme.surfaceVariant,
    colorScheme.onSurfaceVariant,
    colorScheme.surfaceTint,
    colorScheme.inverseSurface,
    colorScheme.inverseOnSurface,
    colorScheme.error,
    colorScheme.onError,
    colorScheme.errorContainer,
    colorScheme.onErrorContainer,
    colorScheme.outline,
    colorScheme.outlineVariant,
    colorScheme.scrim,
    colorScheme.surfaceBright,
    colorScheme.surfaceDim,
    colorScheme.surfaceContainer,
    colorScheme.surfaceContainerHigh,
    colorScheme.surfaceContainerHighest,
    colorScheme.surfaceContainerLow,
    colorScheme.surfaceContainerLowest,
)

fun Color.to16Bit(): String {
    val alpha = (alpha * 255).toInt()
    val red = (red * 255).toInt()
    val green = (green * 255).toInt()
    val blue = (blue * 255).toInt()
    return String.format("#%02X%02X%02X%02X", alpha, red, green, blue)
}

fun String.toColor(): Color {
    return Color(this.toColorInt())
}

fun String.toColorSchemeEnum(): ColorSchemeEnum? {
    return ColorSchemeEnum.fromString(this)
}

fun ColorSchemeEnum?.toColorOrDefault(colorScheme: ColorScheme): Color {
    return this?.toColor(colorScheme) ?: AppConstants.DEFAULT_COURSE_COLOR.toColor(colorScheme)
}