// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.ordinaryschedule;

import androidx.lifecycle.SavedStateHandle;
import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.UpdateOrdinaryScheduleUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddEditOrdinaryScheduleViewModel_Factory implements Factory<AddEditOrdinaryScheduleViewModel> {
  private final Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider;

  private final Provider<GetOrdinaryScheduleByIdUseCase> getOrdinaryScheduleByIdUseCaseProvider;

  private final Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<SavedStateHandle> savedStateHandleProvider;

  public AddEditOrdinaryScheduleViewModel_Factory(
      Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider,
      Provider<GetOrdinaryScheduleByIdUseCase> getOrdinaryScheduleByIdUseCaseProvider,
      Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    this.addOrdinaryScheduleUseCaseProvider = addOrdinaryScheduleUseCaseProvider;
    this.getOrdinaryScheduleByIdUseCaseProvider = getOrdinaryScheduleByIdUseCaseProvider;
    this.updateOrdinaryScheduleUseCaseProvider = updateOrdinaryScheduleUseCaseProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.savedStateHandleProvider = savedStateHandleProvider;
  }

  @Override
  public AddEditOrdinaryScheduleViewModel get() {
    return newInstance(addOrdinaryScheduleUseCaseProvider.get(), getOrdinaryScheduleByIdUseCaseProvider.get(), updateOrdinaryScheduleUseCaseProvider.get(), sessionRepositoryProvider.get(), savedStateHandleProvider.get());
  }

  public static AddEditOrdinaryScheduleViewModel_Factory create(
      Provider<AddOrdinaryScheduleUseCase> addOrdinaryScheduleUseCaseProvider,
      Provider<GetOrdinaryScheduleByIdUseCase> getOrdinaryScheduleByIdUseCaseProvider,
      Provider<UpdateOrdinaryScheduleUseCase> updateOrdinaryScheduleUseCaseProvider,
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    return new AddEditOrdinaryScheduleViewModel_Factory(addOrdinaryScheduleUseCaseProvider, getOrdinaryScheduleByIdUseCaseProvider, updateOrdinaryScheduleUseCaseProvider, sessionRepositoryProvider, savedStateHandleProvider);
  }

  public static AddEditOrdinaryScheduleViewModel newInstance(
      AddOrdinaryScheduleUseCase addOrdinaryScheduleUseCase,
      GetOrdinaryScheduleByIdUseCase getOrdinaryScheduleByIdUseCase,
      UpdateOrdinaryScheduleUseCase updateOrdinaryScheduleUseCase,
      SessionRepository sessionRepository, SavedStateHandle savedStateHandle) {
    return new AddEditOrdinaryScheduleViewModel(addOrdinaryScheduleUseCase, getOrdinaryScheduleByIdUseCase, updateOrdinaryScheduleUseCase, sessionRepository, savedStateHandle);
  }
}
