package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.LongSparseArray;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.RelationUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.entity.CourseEntity;
import com.example.todoschedule.data.database.entity.CourseNodeEntity;
import com.example.todoschedule.data.model.CourseWithNodes;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class CourseDao_Impl implements CourseDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CourseEntity> __insertionAdapterOfCourseEntity;

  private final EntityInsertionAdapter<CourseNodeEntity> __insertionAdapterOfCourseNodeEntity;

  private final EntityDeletionOrUpdateAdapter<CourseEntity> __updateAdapterOfCourseEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteCourse;

  private final SharedSQLiteStatement __preparedStmtOfDeleteCourseNode;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllNodesOfCourse;

  public CourseDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCourseEntity = new EntityInsertionAdapter<CourseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `course` (`id`,`tableId`,`courseName`,`color`,`room`,`teacher`,`startWeek`,`endWeek`,`credit`,`courseCode`,`syllabusLink`,`crdtKey`,`tableCrdtKey`,`update_timestamp`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTableId());
        if (entity.getCourseName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCourseName());
        }
        if (entity.getColor() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getColor());
        }
        if (entity.getRoom() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getRoom());
        }
        if (entity.getTeacher() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTeacher());
        }
        statement.bindLong(7, entity.getStartWeek());
        statement.bindLong(8, entity.getEndWeek());
        if (entity.getCredit() == null) {
          statement.bindNull(9);
        } else {
          statement.bindDouble(9, entity.getCredit());
        }
        if (entity.getCourseCode() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getCourseCode());
        }
        if (entity.getSyllabusLink() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getSyllabusLink());
        }
        if (entity.getCrdtKey() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getCrdtKey());
        }
        if (entity.getTableCrdtKey() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getTableCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getUpdateTimestamp());
        }
      }
    };
    this.__insertionAdapterOfCourseNodeEntity = new EntityInsertionAdapter<CourseNodeEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `course_node` (`id`,`courseId`,`courseNodeName`,`color`,`room`,`teacher`,`startNode`,`step`,`day`,`startWeek`,`endWeek`,`weekType`,`crdtKey`,`courseCrdtKey`,`update_timestamp`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseNodeEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getCourseId());
        if (entity.getCourseNodeName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCourseNodeName());
        }
        if (entity.getColor() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getColor());
        }
        if (entity.getRoom() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getRoom());
        }
        if (entity.getTeacher() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTeacher());
        }
        statement.bindLong(7, entity.getStartNode());
        statement.bindLong(8, entity.getStep());
        statement.bindLong(9, entity.getDay());
        statement.bindLong(10, entity.getStartWeek());
        statement.bindLong(11, entity.getEndWeek());
        statement.bindLong(12, entity.getWeekType());
        if (entity.getCrdtKey() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getCrdtKey());
        }
        if (entity.getCourseCrdtKey() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getCourseCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getUpdateTimestamp());
        }
      }
    };
    this.__updateAdapterOfCourseEntity = new EntityDeletionOrUpdateAdapter<CourseEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `course` SET `id` = ?,`tableId` = ?,`courseName` = ?,`color` = ?,`room` = ?,`teacher` = ?,`startWeek` = ?,`endWeek` = ?,`credit` = ?,`courseCode` = ?,`syllabusLink` = ?,`crdtKey` = ?,`tableCrdtKey` = ?,`update_timestamp` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CourseEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTableId());
        if (entity.getCourseName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCourseName());
        }
        if (entity.getColor() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getColor());
        }
        if (entity.getRoom() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getRoom());
        }
        if (entity.getTeacher() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getTeacher());
        }
        statement.bindLong(7, entity.getStartWeek());
        statement.bindLong(8, entity.getEndWeek());
        if (entity.getCredit() == null) {
          statement.bindNull(9);
        } else {
          statement.bindDouble(9, entity.getCredit());
        }
        if (entity.getCourseCode() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getCourseCode());
        }
        if (entity.getSyllabusLink() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getSyllabusLink());
        }
        if (entity.getCrdtKey() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getCrdtKey());
        }
        if (entity.getTableCrdtKey() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getTableCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getUpdateTimestamp());
        }
        statement.bindLong(15, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteCourse = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM course WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteCourseNode = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM course_node WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllNodesOfCourse = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM course_node WHERE courseId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertCourse(final CourseEntity course,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfCourseEntity.insertAndReturnId(course);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertCourses(final List<CourseEntity> courses,
      final Continuation<? super List<Long>> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<List<Long>>() {
      @Override
      @NonNull
      public List<Long> call() throws Exception {
        __db.beginTransaction();
        try {
          final List<Long> _result = __insertionAdapterOfCourseEntity.insertAndReturnIdsList(courses);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertCourseNode(final CourseNodeEntity courseNode,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfCourseNodeEntity.insertAndReturnId(courseNode);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertCourseNodes(final List<CourseNodeEntity> courseNodes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCourseNodeEntity.insert(courseNodes);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCourse(final CourseEntity course,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCourseEntity.handle(course);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCourse(final int courseId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteCourse.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, courseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteCourse.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCourseNode(final int nodeId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteCourseNode.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, nodeId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteCourseNode.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllNodesOfCourse(final int courseId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllNodesOfCourse.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, courseId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllNodesOfCourse.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CourseWithNodes>> getCoursesByTableId(final int tableId) {
    final String _sql = "SELECT * FROM course WHERE tableId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tableId);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"course_node",
        "course"}, new Callable<List<CourseWithNodes>>() {
      @Override
      @NonNull
      public List<CourseWithNodes> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "tableId");
            final int _cursorIndexOfCourseName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseName");
            final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
            final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
            final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
            final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
            final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
            final int _cursorIndexOfCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "credit");
            final int _cursorIndexOfCourseCode = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCode");
            final int _cursorIndexOfSyllabusLink = CursorUtil.getColumnIndexOrThrow(_cursor, "syllabusLink");
            final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
            final int _cursorIndexOfTableCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "tableCrdtKey");
            final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
            final LongSparseArray<ArrayList<CourseNodeEntity>> _collectionNodes = new LongSparseArray<ArrayList<CourseNodeEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionNodes.containsKey(_tmpKey)) {
                _collectionNodes.put(_tmpKey, new ArrayList<CourseNodeEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipcourseNodeAscomExampleTodoscheduleDataDatabaseEntityCourseNodeEntity(_collectionNodes);
            final List<CourseWithNodes> _result = new ArrayList<CourseWithNodes>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final CourseWithNodes _item;
              final CourseEntity _tmpCourse;
              final int _tmpId;
              _tmpId = _cursor.getInt(_cursorIndexOfId);
              final int _tmpTableId;
              _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
              final String _tmpCourseName;
              if (_cursor.isNull(_cursorIndexOfCourseName)) {
                _tmpCourseName = null;
              } else {
                _tmpCourseName = _cursor.getString(_cursorIndexOfCourseName);
              }
              final String _tmpColor;
              if (_cursor.isNull(_cursorIndexOfColor)) {
                _tmpColor = null;
              } else {
                _tmpColor = _cursor.getString(_cursorIndexOfColor);
              }
              final String _tmpRoom;
              if (_cursor.isNull(_cursorIndexOfRoom)) {
                _tmpRoom = null;
              } else {
                _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
              }
              final String _tmpTeacher;
              if (_cursor.isNull(_cursorIndexOfTeacher)) {
                _tmpTeacher = null;
              } else {
                _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
              }
              final int _tmpStartWeek;
              _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
              final int _tmpEndWeek;
              _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
              final Float _tmpCredit;
              if (_cursor.isNull(_cursorIndexOfCredit)) {
                _tmpCredit = null;
              } else {
                _tmpCredit = _cursor.getFloat(_cursorIndexOfCredit);
              }
              final String _tmpCourseCode;
              if (_cursor.isNull(_cursorIndexOfCourseCode)) {
                _tmpCourseCode = null;
              } else {
                _tmpCourseCode = _cursor.getString(_cursorIndexOfCourseCode);
              }
              final String _tmpSyllabusLink;
              if (_cursor.isNull(_cursorIndexOfSyllabusLink)) {
                _tmpSyllabusLink = null;
              } else {
                _tmpSyllabusLink = _cursor.getString(_cursorIndexOfSyllabusLink);
              }
              final String _tmpCrdtKey;
              if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
                _tmpCrdtKey = null;
              } else {
                _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
              }
              final String _tmpTableCrdtKey;
              if (_cursor.isNull(_cursorIndexOfTableCrdtKey)) {
                _tmpTableCrdtKey = null;
              } else {
                _tmpTableCrdtKey = _cursor.getString(_cursorIndexOfTableCrdtKey);
              }
              final Long _tmpUpdateTimestamp;
              if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
                _tmpUpdateTimestamp = null;
              } else {
                _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
              }
              _tmpCourse = new CourseEntity(_tmpId,_tmpTableId,_tmpCourseName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartWeek,_tmpEndWeek,_tmpCredit,_tmpCourseCode,_tmpSyllabusLink,_tmpCrdtKey,_tmpTableCrdtKey,_tmpUpdateTimestamp);
              final ArrayList<CourseNodeEntity> _tmpNodesCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpNodesCollection = _collectionNodes.get(_tmpKey_1);
              _item = new CourseWithNodes(_tmpCourse,_tmpNodesCollection);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CourseWithNodes>> getCoursesByWeek(final int tableId, final int week) {
    final String _sql = "\n"
            + "        SELECT DISTINCT c.* FROM course c\n"
            + "        JOIN course_node cn ON c.id = cn.courseId\n"
            + "        WHERE c.tableId = ?\n"
            + "        AND cn.startWeek <= ? AND cn.endWeek >= ?\n"
            + "        AND (cn.weekType = 0 OR (cn.weekType = 1 AND ? % 2 = 1) OR (cn.weekType = 2 AND ? % 2 = 0))\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 5);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tableId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, week);
    _argIndex = 3;
    _statement.bindLong(_argIndex, week);
    _argIndex = 4;
    _statement.bindLong(_argIndex, week);
    _argIndex = 5;
    _statement.bindLong(_argIndex, week);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"course_node",
        "course"}, new Callable<List<CourseWithNodes>>() {
      @Override
      @NonNull
      public List<CourseWithNodes> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "tableId");
            final int _cursorIndexOfCourseName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseName");
            final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
            final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
            final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
            final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
            final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
            final int _cursorIndexOfCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "credit");
            final int _cursorIndexOfCourseCode = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCode");
            final int _cursorIndexOfSyllabusLink = CursorUtil.getColumnIndexOrThrow(_cursor, "syllabusLink");
            final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
            final int _cursorIndexOfTableCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "tableCrdtKey");
            final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
            final LongSparseArray<ArrayList<CourseNodeEntity>> _collectionNodes = new LongSparseArray<ArrayList<CourseNodeEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionNodes.containsKey(_tmpKey)) {
                _collectionNodes.put(_tmpKey, new ArrayList<CourseNodeEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipcourseNodeAscomExampleTodoscheduleDataDatabaseEntityCourseNodeEntity(_collectionNodes);
            final List<CourseWithNodes> _result = new ArrayList<CourseWithNodes>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final CourseWithNodes _item;
              final CourseEntity _tmpCourse;
              final int _tmpId;
              _tmpId = _cursor.getInt(_cursorIndexOfId);
              final int _tmpTableId;
              _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
              final String _tmpCourseName;
              if (_cursor.isNull(_cursorIndexOfCourseName)) {
                _tmpCourseName = null;
              } else {
                _tmpCourseName = _cursor.getString(_cursorIndexOfCourseName);
              }
              final String _tmpColor;
              if (_cursor.isNull(_cursorIndexOfColor)) {
                _tmpColor = null;
              } else {
                _tmpColor = _cursor.getString(_cursorIndexOfColor);
              }
              final String _tmpRoom;
              if (_cursor.isNull(_cursorIndexOfRoom)) {
                _tmpRoom = null;
              } else {
                _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
              }
              final String _tmpTeacher;
              if (_cursor.isNull(_cursorIndexOfTeacher)) {
                _tmpTeacher = null;
              } else {
                _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
              }
              final int _tmpStartWeek;
              _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
              final int _tmpEndWeek;
              _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
              final Float _tmpCredit;
              if (_cursor.isNull(_cursorIndexOfCredit)) {
                _tmpCredit = null;
              } else {
                _tmpCredit = _cursor.getFloat(_cursorIndexOfCredit);
              }
              final String _tmpCourseCode;
              if (_cursor.isNull(_cursorIndexOfCourseCode)) {
                _tmpCourseCode = null;
              } else {
                _tmpCourseCode = _cursor.getString(_cursorIndexOfCourseCode);
              }
              final String _tmpSyllabusLink;
              if (_cursor.isNull(_cursorIndexOfSyllabusLink)) {
                _tmpSyllabusLink = null;
              } else {
                _tmpSyllabusLink = _cursor.getString(_cursorIndexOfSyllabusLink);
              }
              final String _tmpCrdtKey;
              if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
                _tmpCrdtKey = null;
              } else {
                _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
              }
              final String _tmpTableCrdtKey;
              if (_cursor.isNull(_cursorIndexOfTableCrdtKey)) {
                _tmpTableCrdtKey = null;
              } else {
                _tmpTableCrdtKey = _cursor.getString(_cursorIndexOfTableCrdtKey);
              }
              final Long _tmpUpdateTimestamp;
              if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
                _tmpUpdateTimestamp = null;
              } else {
                _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
              }
              _tmpCourse = new CourseEntity(_tmpId,_tmpTableId,_tmpCourseName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartWeek,_tmpEndWeek,_tmpCredit,_tmpCourseCode,_tmpSyllabusLink,_tmpCrdtKey,_tmpTableCrdtKey,_tmpUpdateTimestamp);
              final ArrayList<CourseNodeEntity> _tmpNodesCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpNodesCollection = _collectionNodes.get(_tmpKey_1);
              _item = new CourseWithNodes(_tmpCourse,_tmpNodesCollection);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CourseNodeEntity>> getCourseNodesByDayAndWeek(final int tableId, final int day,
      final int week) {
    final String _sql = "\n"
            + "        SELECT * FROM course_node\n"
            + "        WHERE courseId IN (SELECT id FROM course WHERE tableId = ?)\n"
            + "        AND day = ?\n"
            + "        AND startWeek <= ? AND endWeek >= ?\n"
            + "        AND (weekType = 0 OR (weekType = 1 AND ? % 2 = 1) OR (weekType = 2 AND ? % 2 = 0))\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 6);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tableId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, day);
    _argIndex = 3;
    _statement.bindLong(_argIndex, week);
    _argIndex = 4;
    _statement.bindLong(_argIndex, week);
    _argIndex = 5;
    _statement.bindLong(_argIndex, week);
    _argIndex = 6;
    _statement.bindLong(_argIndex, week);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"course_node",
        "course"}, new Callable<List<CourseNodeEntity>>() {
      @Override
      @NonNull
      public List<CourseNodeEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCourseNodeName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNodeName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartNode = CursorUtil.getColumnIndexOrThrow(_cursor, "startNode");
          final int _cursorIndexOfStep = CursorUtil.getColumnIndexOrThrow(_cursor, "step");
          final int _cursorIndexOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "day");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfWeekType = CursorUtil.getColumnIndexOrThrow(_cursor, "weekType");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfCourseCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<CourseNodeEntity> _result = new ArrayList<CourseNodeEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseNodeEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpCourseId;
            _tmpCourseId = _cursor.getInt(_cursorIndexOfCourseId);
            final String _tmpCourseNodeName;
            if (_cursor.isNull(_cursorIndexOfCourseNodeName)) {
              _tmpCourseNodeName = null;
            } else {
              _tmpCourseNodeName = _cursor.getString(_cursorIndexOfCourseNodeName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartNode;
            _tmpStartNode = _cursor.getInt(_cursorIndexOfStartNode);
            final int _tmpStep;
            _tmpStep = _cursor.getInt(_cursorIndexOfStep);
            final int _tmpDay;
            _tmpDay = _cursor.getInt(_cursorIndexOfDay);
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final int _tmpWeekType;
            _tmpWeekType = _cursor.getInt(_cursorIndexOfWeekType);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpCourseCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCourseCrdtKey)) {
              _tmpCourseCrdtKey = null;
            } else {
              _tmpCourseCrdtKey = _cursor.getString(_cursorIndexOfCourseCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new CourseNodeEntity(_tmpId,_tmpCourseId,_tmpCourseNodeName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartNode,_tmpStep,_tmpDay,_tmpStartWeek,_tmpEndWeek,_tmpWeekType,_tmpCrdtKey,_tmpCourseCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCourseWithNodesById(final int courseId,
      final Continuation<? super CourseWithNodes> $completion) {
    final String _sql = "SELECT * FROM course WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, true, _cancellationSignal, new Callable<CourseWithNodes>() {
      @Override
      @Nullable
      public CourseWithNodes call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "tableId");
            final int _cursorIndexOfCourseName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseName");
            final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
            final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
            final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
            final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
            final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
            final int _cursorIndexOfCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "credit");
            final int _cursorIndexOfCourseCode = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCode");
            final int _cursorIndexOfSyllabusLink = CursorUtil.getColumnIndexOrThrow(_cursor, "syllabusLink");
            final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
            final int _cursorIndexOfTableCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "tableCrdtKey");
            final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
            final LongSparseArray<ArrayList<CourseNodeEntity>> _collectionNodes = new LongSparseArray<ArrayList<CourseNodeEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionNodes.containsKey(_tmpKey)) {
                _collectionNodes.put(_tmpKey, new ArrayList<CourseNodeEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipcourseNodeAscomExampleTodoscheduleDataDatabaseEntityCourseNodeEntity(_collectionNodes);
            final CourseWithNodes _result;
            if (_cursor.moveToFirst()) {
              final CourseEntity _tmpCourse;
              final int _tmpId;
              _tmpId = _cursor.getInt(_cursorIndexOfId);
              final int _tmpTableId;
              _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
              final String _tmpCourseName;
              if (_cursor.isNull(_cursorIndexOfCourseName)) {
                _tmpCourseName = null;
              } else {
                _tmpCourseName = _cursor.getString(_cursorIndexOfCourseName);
              }
              final String _tmpColor;
              if (_cursor.isNull(_cursorIndexOfColor)) {
                _tmpColor = null;
              } else {
                _tmpColor = _cursor.getString(_cursorIndexOfColor);
              }
              final String _tmpRoom;
              if (_cursor.isNull(_cursorIndexOfRoom)) {
                _tmpRoom = null;
              } else {
                _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
              }
              final String _tmpTeacher;
              if (_cursor.isNull(_cursorIndexOfTeacher)) {
                _tmpTeacher = null;
              } else {
                _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
              }
              final int _tmpStartWeek;
              _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
              final int _tmpEndWeek;
              _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
              final Float _tmpCredit;
              if (_cursor.isNull(_cursorIndexOfCredit)) {
                _tmpCredit = null;
              } else {
                _tmpCredit = _cursor.getFloat(_cursorIndexOfCredit);
              }
              final String _tmpCourseCode;
              if (_cursor.isNull(_cursorIndexOfCourseCode)) {
                _tmpCourseCode = null;
              } else {
                _tmpCourseCode = _cursor.getString(_cursorIndexOfCourseCode);
              }
              final String _tmpSyllabusLink;
              if (_cursor.isNull(_cursorIndexOfSyllabusLink)) {
                _tmpSyllabusLink = null;
              } else {
                _tmpSyllabusLink = _cursor.getString(_cursorIndexOfSyllabusLink);
              }
              final String _tmpCrdtKey;
              if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
                _tmpCrdtKey = null;
              } else {
                _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
              }
              final String _tmpTableCrdtKey;
              if (_cursor.isNull(_cursorIndexOfTableCrdtKey)) {
                _tmpTableCrdtKey = null;
              } else {
                _tmpTableCrdtKey = _cursor.getString(_cursorIndexOfTableCrdtKey);
              }
              final Long _tmpUpdateTimestamp;
              if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
                _tmpUpdateTimestamp = null;
              } else {
                _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
              }
              _tmpCourse = new CourseEntity(_tmpId,_tmpTableId,_tmpCourseName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartWeek,_tmpEndWeek,_tmpCredit,_tmpCourseCode,_tmpSyllabusLink,_tmpCrdtKey,_tmpTableCrdtKey,_tmpUpdateTimestamp);
              final ArrayList<CourseNodeEntity> _tmpNodesCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpNodesCollection = _collectionNodes.get(_tmpKey_1);
              _result = new CourseWithNodes(_tmpCourse,_tmpNodesCollection);
            } else {
              _result = null;
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
            _statement.release();
          }
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object getIdByCrdtKey(final String crdtKey,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT id FROM course WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            if (_cursor.isNull(0)) {
              _result = null;
            } else {
              _result = _cursor.getInt(0);
            }
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseByCrdtKey(final String crdtKey,
      final Continuation<? super CourseEntity> $completion) {
    final String _sql = "SELECT * FROM course WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseEntity>() {
      @Override
      @Nullable
      public CourseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "tableId");
          final int _cursorIndexOfCourseName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "credit");
          final int _cursorIndexOfCourseCode = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCode");
          final int _cursorIndexOfSyllabusLink = CursorUtil.getColumnIndexOrThrow(_cursor, "syllabusLink");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfTableCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "tableCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final CourseEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpTableId;
            _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
            final String _tmpCourseName;
            if (_cursor.isNull(_cursorIndexOfCourseName)) {
              _tmpCourseName = null;
            } else {
              _tmpCourseName = _cursor.getString(_cursorIndexOfCourseName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final Float _tmpCredit;
            if (_cursor.isNull(_cursorIndexOfCredit)) {
              _tmpCredit = null;
            } else {
              _tmpCredit = _cursor.getFloat(_cursorIndexOfCredit);
            }
            final String _tmpCourseCode;
            if (_cursor.isNull(_cursorIndexOfCourseCode)) {
              _tmpCourseCode = null;
            } else {
              _tmpCourseCode = _cursor.getString(_cursorIndexOfCourseCode);
            }
            final String _tmpSyllabusLink;
            if (_cursor.isNull(_cursorIndexOfSyllabusLink)) {
              _tmpSyllabusLink = null;
            } else {
              _tmpSyllabusLink = _cursor.getString(_cursorIndexOfSyllabusLink);
            }
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpTableCrdtKey;
            if (_cursor.isNull(_cursorIndexOfTableCrdtKey)) {
              _tmpTableCrdtKey = null;
            } else {
              _tmpTableCrdtKey = _cursor.getString(_cursorIndexOfTableCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new CourseEntity(_tmpId,_tmpTableId,_tmpCourseName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartWeek,_tmpEndWeek,_tmpCredit,_tmpCourseCode,_tmpSyllabusLink,_tmpCrdtKey,_tmpTableCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCoursesByTableCrdtKey(final String tableCrdtKey,
      final Continuation<? super List<CourseEntity>> $completion) {
    final String _sql = "SELECT * FROM course WHERE tableCrdtKey = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (tableCrdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, tableCrdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CourseEntity>>() {
      @Override
      @NonNull
      public List<CourseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "tableId");
          final int _cursorIndexOfCourseName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "credit");
          final int _cursorIndexOfCourseCode = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCode");
          final int _cursorIndexOfSyllabusLink = CursorUtil.getColumnIndexOrThrow(_cursor, "syllabusLink");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfTableCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "tableCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<CourseEntity> _result = new ArrayList<CourseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CourseEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpTableId;
            _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
            final String _tmpCourseName;
            if (_cursor.isNull(_cursorIndexOfCourseName)) {
              _tmpCourseName = null;
            } else {
              _tmpCourseName = _cursor.getString(_cursorIndexOfCourseName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final Float _tmpCredit;
            if (_cursor.isNull(_cursorIndexOfCredit)) {
              _tmpCredit = null;
            } else {
              _tmpCredit = _cursor.getFloat(_cursorIndexOfCredit);
            }
            final String _tmpCourseCode;
            if (_cursor.isNull(_cursorIndexOfCourseCode)) {
              _tmpCourseCode = null;
            } else {
              _tmpCourseCode = _cursor.getString(_cursorIndexOfCourseCode);
            }
            final String _tmpSyllabusLink;
            if (_cursor.isNull(_cursorIndexOfSyllabusLink)) {
              _tmpSyllabusLink = null;
            } else {
              _tmpSyllabusLink = _cursor.getString(_cursorIndexOfSyllabusLink);
            }
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpTableCrdtKey;
            if (_cursor.isNull(_cursorIndexOfTableCrdtKey)) {
              _tmpTableCrdtKey = null;
            } else {
              _tmpTableCrdtKey = _cursor.getString(_cursorIndexOfTableCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new CourseEntity(_tmpId,_tmpTableId,_tmpCourseName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartWeek,_tmpEndWeek,_tmpCredit,_tmpCourseCode,_tmpSyllabusLink,_tmpCrdtKey,_tmpTableCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseNodeById(final int nodeId,
      final Continuation<? super CourseNodeEntity> $completion) {
    final String _sql = "SELECT * FROM course_node WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, nodeId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseNodeEntity>() {
      @Override
      @Nullable
      public CourseNodeEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCourseId = CursorUtil.getColumnIndexOrThrow(_cursor, "courseId");
          final int _cursorIndexOfCourseNodeName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseNodeName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartNode = CursorUtil.getColumnIndexOrThrow(_cursor, "startNode");
          final int _cursorIndexOfStep = CursorUtil.getColumnIndexOrThrow(_cursor, "step");
          final int _cursorIndexOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "day");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfWeekType = CursorUtil.getColumnIndexOrThrow(_cursor, "weekType");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfCourseCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final CourseNodeEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpCourseId;
            _tmpCourseId = _cursor.getInt(_cursorIndexOfCourseId);
            final String _tmpCourseNodeName;
            if (_cursor.isNull(_cursorIndexOfCourseNodeName)) {
              _tmpCourseNodeName = null;
            } else {
              _tmpCourseNodeName = _cursor.getString(_cursorIndexOfCourseNodeName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartNode;
            _tmpStartNode = _cursor.getInt(_cursorIndexOfStartNode);
            final int _tmpStep;
            _tmpStep = _cursor.getInt(_cursorIndexOfStep);
            final int _tmpDay;
            _tmpDay = _cursor.getInt(_cursorIndexOfDay);
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final int _tmpWeekType;
            _tmpWeekType = _cursor.getInt(_cursorIndexOfWeekType);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpCourseCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCourseCrdtKey)) {
              _tmpCourseCrdtKey = null;
            } else {
              _tmpCourseCrdtKey = _cursor.getString(_cursorIndexOfCourseCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new CourseNodeEntity(_tmpId,_tmpCourseId,_tmpCourseNodeName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartNode,_tmpStep,_tmpDay,_tmpStartWeek,_tmpEndWeek,_tmpWeekType,_tmpCrdtKey,_tmpCourseCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCourseById(final int courseId,
      final Continuation<? super CourseEntity> $completion) {
    final String _sql = "SELECT * FROM course WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, courseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CourseEntity>() {
      @Override
      @Nullable
      public CourseEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTableId = CursorUtil.getColumnIndexOrThrow(_cursor, "tableId");
          final int _cursorIndexOfCourseName = CursorUtil.getColumnIndexOrThrow(_cursor, "courseName");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfRoom = CursorUtil.getColumnIndexOrThrow(_cursor, "room");
          final int _cursorIndexOfTeacher = CursorUtil.getColumnIndexOrThrow(_cursor, "teacher");
          final int _cursorIndexOfStartWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "startWeek");
          final int _cursorIndexOfEndWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "endWeek");
          final int _cursorIndexOfCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "credit");
          final int _cursorIndexOfCourseCode = CursorUtil.getColumnIndexOrThrow(_cursor, "courseCode");
          final int _cursorIndexOfSyllabusLink = CursorUtil.getColumnIndexOrThrow(_cursor, "syllabusLink");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfTableCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "tableCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final CourseEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpTableId;
            _tmpTableId = _cursor.getInt(_cursorIndexOfTableId);
            final String _tmpCourseName;
            if (_cursor.isNull(_cursorIndexOfCourseName)) {
              _tmpCourseName = null;
            } else {
              _tmpCourseName = _cursor.getString(_cursorIndexOfCourseName);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final String _tmpRoom;
            if (_cursor.isNull(_cursorIndexOfRoom)) {
              _tmpRoom = null;
            } else {
              _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
            }
            final String _tmpTeacher;
            if (_cursor.isNull(_cursorIndexOfTeacher)) {
              _tmpTeacher = null;
            } else {
              _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
            }
            final int _tmpStartWeek;
            _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
            final int _tmpEndWeek;
            _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
            final Float _tmpCredit;
            if (_cursor.isNull(_cursorIndexOfCredit)) {
              _tmpCredit = null;
            } else {
              _tmpCredit = _cursor.getFloat(_cursorIndexOfCredit);
            }
            final String _tmpCourseCode;
            if (_cursor.isNull(_cursorIndexOfCourseCode)) {
              _tmpCourseCode = null;
            } else {
              _tmpCourseCode = _cursor.getString(_cursorIndexOfCourseCode);
            }
            final String _tmpSyllabusLink;
            if (_cursor.isNull(_cursorIndexOfSyllabusLink)) {
              _tmpSyllabusLink = null;
            } else {
              _tmpSyllabusLink = _cursor.getString(_cursorIndexOfSyllabusLink);
            }
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpTableCrdtKey;
            if (_cursor.isNull(_cursorIndexOfTableCrdtKey)) {
              _tmpTableCrdtKey = null;
            } else {
              _tmpTableCrdtKey = _cursor.getString(_cursorIndexOfTableCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new CourseEntity(_tmpId,_tmpTableId,_tmpCourseName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartWeek,_tmpEndWeek,_tmpCredit,_tmpCourseCode,_tmpSyllabusLink,_tmpCrdtKey,_tmpTableCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private void __fetchRelationshipcourseNodeAscomExampleTodoscheduleDataDatabaseEntityCourseNodeEntity(
      @NonNull final LongSparseArray<ArrayList<CourseNodeEntity>> _map) {
    if (_map.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchLongSparseArray(_map, true, (map) -> {
        __fetchRelationshipcourseNodeAscomExampleTodoscheduleDataDatabaseEntityCourseNodeEntity(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`courseId`,`courseNodeName`,`color`,`room`,`teacher`,`startNode`,`step`,`day`,`startWeek`,`endWeek`,`weekType`,`crdtKey`,`courseCrdtKey`,`update_timestamp` FROM `course_node` WHERE `courseId` IN (");
    final int _inputSize = _map.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    for (int i = 0; i < _map.size(); i++) {
      final long _item = _map.keyAt(i);
      _stmt.bindLong(_argIndex, _item);
      _argIndex++;
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, false, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "courseId");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfCourseId = 1;
      final int _cursorIndexOfCourseNodeName = 2;
      final int _cursorIndexOfColor = 3;
      final int _cursorIndexOfRoom = 4;
      final int _cursorIndexOfTeacher = 5;
      final int _cursorIndexOfStartNode = 6;
      final int _cursorIndexOfStep = 7;
      final int _cursorIndexOfDay = 8;
      final int _cursorIndexOfStartWeek = 9;
      final int _cursorIndexOfEndWeek = 10;
      final int _cursorIndexOfWeekType = 11;
      final int _cursorIndexOfCrdtKey = 12;
      final int _cursorIndexOfCourseCrdtKey = 13;
      final int _cursorIndexOfUpdateTimestamp = 14;
      while (_cursor.moveToNext()) {
        final long _tmpKey;
        _tmpKey = _cursor.getLong(_itemKeyIndex);
        final ArrayList<CourseNodeEntity> _tmpRelation = _map.get(_tmpKey);
        if (_tmpRelation != null) {
          final CourseNodeEntity _item_1;
          final int _tmpId;
          _tmpId = _cursor.getInt(_cursorIndexOfId);
          final int _tmpCourseId;
          _tmpCourseId = _cursor.getInt(_cursorIndexOfCourseId);
          final String _tmpCourseNodeName;
          if (_cursor.isNull(_cursorIndexOfCourseNodeName)) {
            _tmpCourseNodeName = null;
          } else {
            _tmpCourseNodeName = _cursor.getString(_cursorIndexOfCourseNodeName);
          }
          final String _tmpColor;
          if (_cursor.isNull(_cursorIndexOfColor)) {
            _tmpColor = null;
          } else {
            _tmpColor = _cursor.getString(_cursorIndexOfColor);
          }
          final String _tmpRoom;
          if (_cursor.isNull(_cursorIndexOfRoom)) {
            _tmpRoom = null;
          } else {
            _tmpRoom = _cursor.getString(_cursorIndexOfRoom);
          }
          final String _tmpTeacher;
          if (_cursor.isNull(_cursorIndexOfTeacher)) {
            _tmpTeacher = null;
          } else {
            _tmpTeacher = _cursor.getString(_cursorIndexOfTeacher);
          }
          final int _tmpStartNode;
          _tmpStartNode = _cursor.getInt(_cursorIndexOfStartNode);
          final int _tmpStep;
          _tmpStep = _cursor.getInt(_cursorIndexOfStep);
          final int _tmpDay;
          _tmpDay = _cursor.getInt(_cursorIndexOfDay);
          final int _tmpStartWeek;
          _tmpStartWeek = _cursor.getInt(_cursorIndexOfStartWeek);
          final int _tmpEndWeek;
          _tmpEndWeek = _cursor.getInt(_cursorIndexOfEndWeek);
          final int _tmpWeekType;
          _tmpWeekType = _cursor.getInt(_cursorIndexOfWeekType);
          final String _tmpCrdtKey;
          if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
            _tmpCrdtKey = null;
          } else {
            _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
          }
          final String _tmpCourseCrdtKey;
          if (_cursor.isNull(_cursorIndexOfCourseCrdtKey)) {
            _tmpCourseCrdtKey = null;
          } else {
            _tmpCourseCrdtKey = _cursor.getString(_cursorIndexOfCourseCrdtKey);
          }
          final Long _tmpUpdateTimestamp;
          if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
            _tmpUpdateTimestamp = null;
          } else {
            _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
          }
          _item_1 = new CourseNodeEntity(_tmpId,_tmpCourseId,_tmpCourseNodeName,_tmpColor,_tmpRoom,_tmpTeacher,_tmpStartNode,_tmpStep,_tmpDay,_tmpStartWeek,_tmpEndWeek,_tmpWeekType,_tmpCrdtKey,_tmpCourseCrdtKey,_tmpUpdateTimestamp);
          _tmpRelation.add(_item_1);
        }
      }
    } finally {
      _cursor.close();
    }
  }
}
