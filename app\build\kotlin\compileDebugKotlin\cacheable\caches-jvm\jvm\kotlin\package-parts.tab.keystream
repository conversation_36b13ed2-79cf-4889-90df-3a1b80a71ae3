   = c o m / e x a m p l e / t o d o s c h e d u l e / c o r e / e x t e n s i o n s / D a t e T i m e E x t e n s i o n s K t   A c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / d a t a b a s e / e n t i t y / S y n c M e s s a g e E n t i t y K t   K c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / d a t a b a s e / e n t i t y / S y n c M e s s a g e E n t i t y E x t e n s i o n s K t   3 c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / m a p p e r / C o u r s e M a p p e r K t   : c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / m a p p e r / G l o b a l S e t t i n g M a p p e r K t   > c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / m a p p e r / O r d i n a r y S c h e d u l e M a p p p e r K t   < c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / m a p p e r / T a b l e T i m e C o n f i g M a p p e r K t   5 c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / m a p p e r / T i m e S l o t M a p p e r K t   1 c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / m a p p e r / U s e r M a p p e r K t   C c o m / e x a m p l e / t o d o s c h e d u l e / d a t a / r e p o s i t o r y / S y n c R e p o s i t o r y E x t e n s i o n s K t   . c o m / e x a m p l e / t o d o s c h e d u l e / n a v i g a t i o n / N a v G r a p h K t   . c o m / e x a m p l e / t o d o s c h e d u l e / u i / a u t h / L o g i n S c r e e n K t   1 c o m / e x a m p l e / t o d o s c h e d u l e / u i / a u t h / R e g i s t e r S c r e e n K t   9 c o m / e x a m p l e / t o d o s c h e d u l e / u i / c o m p o n e n t s / P e r m i s s i o n D i a l o g K t   8 c o m / e x a m p l e / t o d o s c h e d u l e / u i / c o u r s e / a d d / A d d C o u r s e S c r e e n K t   ; c o m / e x a m p l e / t o d o s c h e d u l e / u i / c o u r s e / a d d / A d d C o u r s e V i e w M o d e l K t   > c o m / e x a m p l e / t o d o s c h e d u l e / u i / c o u r s e / d e t a i l / C o u r s e D e t a i l S c r e e n K t   : c o m / e x a m p l e / t o d o s c h e d u l e / u i / c o u r s e / e d i t / E d i t C o u r s e S c r e e n K t   > c o m / e x a m p l e / t o d o s c h e d u l e / u i / c o u r s e / l o a d / S c h o o l S e l e c t o r S c r e e n K t   7 c o m / e x a m p l e / t o d o s c h e d u l e / u i / c o u r s e / l o a d / W e b V i e w S c r e e n K t   - c o m / e x a m p l e / t o d o s c h e d u l e / u i / h o m e / H o m e S c r e e n K t   < c o m / e x a m p l e / t o d o s c h e d u l e / u i / n a v i g a t i o n / A p p B o t t o m N a v i g a t i o n K t   6 c o m / e x a m p l e / t o d o s c h e d u l e / u i / n a v i g a t i o n / A p p N a v i g a t i o n K t   L c o m / e x a m p l e / t o d o s c h e d u l e / u i / o r d i n a r y s c h e d u l e / A d d E d i t O r d i n a r y S c h e d u l e S c r e e n K t   K c o m / e x a m p l e / t o d o s c h e d u l e / u i / o r d i n a r y s c h e d u l e / O r d i n a r y S c h e d u l e D e t a i l S c r e e n K t   3 c o m / e x a m p l e / t o d o s c h e d u l e / u i / p r o f i l e / P r o f i l e S c r e e n K t   C c o m / e x a m p l e / t o d o s c h e d u l e / u i / s c h e d u l e / Q u i c k A d d S c h e d u l e S h e e t C o n t e n t K t   5 c o m / e x a m p l e / t o d o s c h e d u l e / u i / s c h e d u l e / S c h e d u l e S c r e e n K t   8 c o m / e x a m p l e / t o d o s c h e d u l e / u i / s c h e d u l e / S c h e d u l e V i e w M o d e l K t   / c o m / e x a m p l e / t o d o s c h e d u l e / u i / s c h e d u l e / T i m e A x i s K t   / c o m / e x a m p l e / t o d o s c h e d u l e / u i / s c h e d u l e / W e e k G r i d K t   ; c o m / e x a m p l e / t o d o s c h e d u l e / u i / s c r e e n s / s e t t i n g / S e t t i n g S c r e e n K t   5 c o m / e x a m p l e / t o d o s c h e d u l e / u i / s e t t i n g s / S e t t i n g s S c r e e n K t   / c o m / e x a m p l e / t o d o s c h e d u l e / u i / s t u d y / S t u d y S c r e e n K t   6 c o m / e x a m p l e / t o d o s c h e d u l e / u i / s y n c / S y n c S t a t u s I n d i c a t o r K t   9 c o m / e x a m p l e / t o d o s c h e d u l e / u i / t a b l e / C r e a t e E d i t T a b l e S c r e e n K t   9 c o m / e x a m p l e / t o d o s c h e d u l e / u i / t a s k / T a s k C a l e n d a r S y n c S c r e e n K t   5 c o m / e x a m p l e / t o d o s c h e d u l e / u i / t a s k / T a s k R e m i n d e r S c r e e n K t   - c o m / e x a m p l e / t o d o s c h e d u l e / u i / t a s k / T a s k S c r e e n K t   ) c o m / e x a m p l e / t o d o s c h e d u l e / u i / t h e m e / C o l o r K t   ) c o m / e x a m p l e / t o d o s c h e d u l e / u i / t h e m e / T h e m e K t   ( c o m / e x a m p l e / t o d o s c h e d u l e / u i / t h e m e / T y p e K t   - c o m / e x a m p l e / t o d o s c h e d u l e / u i / t o d o / T o d o S c r e e n K t   < c o m / e x a m p l e / m y k o t l i n a p p l i c a t i o n / u t i l s / i n j e c t j s / Z Z U I n j e c t J s K t                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            