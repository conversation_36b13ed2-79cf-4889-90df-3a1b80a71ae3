package com.example.todoschedule.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.LongSparseArray;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.RelationUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.todoschedule.data.database.converter.Converters;
import com.example.todoschedule.data.database.converter.ReminderType;
import com.example.todoschedule.data.database.converter.ScheduleStatus;
import com.example.todoschedule.data.database.converter.ScheduleType;
import com.example.todoschedule.data.database.entity.OrdinaryScheduleEntity;
import com.example.todoschedule.data.database.entity.TimeSlotEntity;
import com.example.todoschedule.data.model.OrdinaryScheduleWithTimeSlots;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class OrdinaryScheduleDao_Impl implements OrdinaryScheduleDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<OrdinaryScheduleEntity> __insertionAdapterOfOrdinaryScheduleEntity;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<OrdinaryScheduleEntity> __deletionAdapterOfOrdinaryScheduleEntity;

  private final EntityDeletionOrUpdateAdapter<OrdinaryScheduleEntity> __updateAdapterOfOrdinaryScheduleEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOrdinarySchedule;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllSchedules;

  public OrdinaryScheduleDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfOrdinaryScheduleEntity = new EntityInsertionAdapter<OrdinaryScheduleEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `ordinary_schedule` (`id`,`userId`,`title`,`description`,`location`,`category`,`color`,`is_all_day`,`status`,`crdtKey`,`userCrdtKey`,`update_timestamp`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final OrdinaryScheduleEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getTitle() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTitle());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getLocation());
        }
        if (entity.getCategory() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getCategory());
        }
        if (entity.getColor() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getColor());
        }
        final int _tmp = entity.isAllDay() ? 1 : 0;
        statement.bindLong(8, _tmp);
        final String _tmp_1 = __converters.fromScheduleStatus(entity.getStatus());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        if (entity.getCrdtKey() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getCrdtKey());
        }
        if (entity.getUserCrdtKey() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getUserCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getUpdateTimestamp());
        }
      }
    };
    this.__deletionAdapterOfOrdinaryScheduleEntity = new EntityDeletionOrUpdateAdapter<OrdinaryScheduleEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `ordinary_schedule` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final OrdinaryScheduleEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfOrdinaryScheduleEntity = new EntityDeletionOrUpdateAdapter<OrdinaryScheduleEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `ordinary_schedule` SET `id` = ?,`userId` = ?,`title` = ?,`description` = ?,`location` = ?,`category` = ?,`color` = ?,`is_all_day` = ?,`status` = ?,`crdtKey` = ?,`userCrdtKey` = ?,`update_timestamp` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final OrdinaryScheduleEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getTitle() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTitle());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getLocation());
        }
        if (entity.getCategory() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getCategory());
        }
        if (entity.getColor() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getColor());
        }
        final int _tmp = entity.isAllDay() ? 1 : 0;
        statement.bindLong(8, _tmp);
        final String _tmp_1 = __converters.fromScheduleStatus(entity.getStatus());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        if (entity.getCrdtKey() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getCrdtKey());
        }
        if (entity.getUserCrdtKey() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getUserCrdtKey());
        }
        if (entity.getUpdateTimestamp() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getUpdateTimestamp());
        }
        statement.bindLong(13, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteOrdinarySchedule = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM ordinary_schedule WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllSchedules = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM ordinary_schedule WHERE userId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertSchedule(final OrdinaryScheduleEntity schedule,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfOrdinaryScheduleEntity.insertAndReturnId(schedule);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertSchedules(final List<OrdinaryScheduleEntity> schedules,
      final Continuation<? super List<Long>> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<List<Long>>() {
      @Override
      @NonNull
      public List<Long> call() throws Exception {
        __db.beginTransaction();
        try {
          final List<Long> _result = __insertionAdapterOfOrdinaryScheduleEntity.insertAndReturnIdsList(schedules);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertOrdinarySchedule(final OrdinaryScheduleEntity schedule,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfOrdinaryScheduleEntity.insertAndReturnId(schedule);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSchedule(final OrdinaryScheduleEntity schedule,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfOrdinaryScheduleEntity.handle(schedule);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSchedule(final OrdinaryScheduleEntity schedule,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfOrdinaryScheduleEntity.handle(schedule);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOrdinarySchedule(final int id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOrdinarySchedule.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOrdinarySchedule.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllSchedules(final int userId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllSchedules.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllSchedules.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<OrdinaryScheduleWithTimeSlots> getScheduleWithTimeSlotsById(final int id) {
    final String _sql = "SELECT * FROM ordinary_schedule WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"time_slot",
        "ordinary_schedule"}, new Callable<OrdinaryScheduleWithTimeSlots>() {
      @Override
      @Nullable
      public OrdinaryScheduleWithTimeSlots call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
            final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
            final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
            final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
            final int _cursorIndexOfIsAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "is_all_day");
            final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
            final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
            final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
            final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
            final LongSparseArray<ArrayList<TimeSlotEntity>> _collectionTimeSlots = new LongSparseArray<ArrayList<TimeSlotEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionTimeSlots.containsKey(_tmpKey)) {
                _collectionTimeSlots.put(_tmpKey, new ArrayList<TimeSlotEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshiptimeSlotAscomExampleTodoscheduleDataDatabaseEntityTimeSlotEntity(_collectionTimeSlots);
            final OrdinaryScheduleWithTimeSlots _result;
            if (_cursor.moveToFirst()) {
              final OrdinaryScheduleEntity _tmpSchedule;
              final int _tmpId;
              _tmpId = _cursor.getInt(_cursorIndexOfId);
              final int _tmpUserId;
              _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
              final String _tmpTitle;
              if (_cursor.isNull(_cursorIndexOfTitle)) {
                _tmpTitle = null;
              } else {
                _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
              }
              final String _tmpDescription;
              if (_cursor.isNull(_cursorIndexOfDescription)) {
                _tmpDescription = null;
              } else {
                _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
              }
              final String _tmpLocation;
              if (_cursor.isNull(_cursorIndexOfLocation)) {
                _tmpLocation = null;
              } else {
                _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
              }
              final String _tmpCategory;
              if (_cursor.isNull(_cursorIndexOfCategory)) {
                _tmpCategory = null;
              } else {
                _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
              }
              final String _tmpColor;
              if (_cursor.isNull(_cursorIndexOfColor)) {
                _tmpColor = null;
              } else {
                _tmpColor = _cursor.getString(_cursorIndexOfColor);
              }
              final boolean _tmpIsAllDay;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsAllDay);
              _tmpIsAllDay = _tmp != 0;
              final ScheduleStatus _tmpStatus;
              final String _tmp_1;
              if (_cursor.isNull(_cursorIndexOfStatus)) {
                _tmp_1 = null;
              } else {
                _tmp_1 = _cursor.getString(_cursorIndexOfStatus);
              }
              _tmpStatus = __converters.toScheduleStatus(_tmp_1);
              final String _tmpCrdtKey;
              if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
                _tmpCrdtKey = null;
              } else {
                _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
              }
              final String _tmpUserCrdtKey;
              if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
                _tmpUserCrdtKey = null;
              } else {
                _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
              }
              final Long _tmpUpdateTimestamp;
              if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
                _tmpUpdateTimestamp = null;
              } else {
                _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
              }
              _tmpSchedule = new OrdinaryScheduleEntity(_tmpId,_tmpUserId,_tmpTitle,_tmpDescription,_tmpLocation,_tmpCategory,_tmpColor,_tmpIsAllDay,_tmpStatus,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
              final ArrayList<TimeSlotEntity> _tmpTimeSlotsCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpTimeSlotsCollection = _collectionTimeSlots.get(_tmpKey_1);
              _result = new OrdinaryScheduleWithTimeSlots(_tmpSchedule,_tmpTimeSlotsCollection);
            } else {
              _result = null;
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<OrdinaryScheduleWithTimeSlots>> getAllSchedulesWithTimeSlots(final int userId) {
    final String _sql = "\n"
            + "        SELECT * \n"
            + "        FROM ordinary_schedule \n"
            + "        WHERE userId = ?\n"
            + "        ORDER BY id DESC\n"
            + "        ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"time_slot",
        "ordinary_schedule"}, new Callable<List<OrdinaryScheduleWithTimeSlots>>() {
      @Override
      @NonNull
      public List<OrdinaryScheduleWithTimeSlots> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
            final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
            final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
            final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
            final int _cursorIndexOfIsAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "is_all_day");
            final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
            final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
            final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
            final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
            final LongSparseArray<ArrayList<TimeSlotEntity>> _collectionTimeSlots = new LongSparseArray<ArrayList<TimeSlotEntity>>();
            while (_cursor.moveToNext()) {
              final long _tmpKey;
              _tmpKey = _cursor.getLong(_cursorIndexOfId);
              if (!_collectionTimeSlots.containsKey(_tmpKey)) {
                _collectionTimeSlots.put(_tmpKey, new ArrayList<TimeSlotEntity>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshiptimeSlotAscomExampleTodoscheduleDataDatabaseEntityTimeSlotEntity(_collectionTimeSlots);
            final List<OrdinaryScheduleWithTimeSlots> _result = new ArrayList<OrdinaryScheduleWithTimeSlots>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final OrdinaryScheduleWithTimeSlots _item;
              final OrdinaryScheduleEntity _tmpSchedule;
              final int _tmpId;
              _tmpId = _cursor.getInt(_cursorIndexOfId);
              final int _tmpUserId;
              _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
              final String _tmpTitle;
              if (_cursor.isNull(_cursorIndexOfTitle)) {
                _tmpTitle = null;
              } else {
                _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
              }
              final String _tmpDescription;
              if (_cursor.isNull(_cursorIndexOfDescription)) {
                _tmpDescription = null;
              } else {
                _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
              }
              final String _tmpLocation;
              if (_cursor.isNull(_cursorIndexOfLocation)) {
                _tmpLocation = null;
              } else {
                _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
              }
              final String _tmpCategory;
              if (_cursor.isNull(_cursorIndexOfCategory)) {
                _tmpCategory = null;
              } else {
                _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
              }
              final String _tmpColor;
              if (_cursor.isNull(_cursorIndexOfColor)) {
                _tmpColor = null;
              } else {
                _tmpColor = _cursor.getString(_cursorIndexOfColor);
              }
              final boolean _tmpIsAllDay;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsAllDay);
              _tmpIsAllDay = _tmp != 0;
              final ScheduleStatus _tmpStatus;
              final String _tmp_1;
              if (_cursor.isNull(_cursorIndexOfStatus)) {
                _tmp_1 = null;
              } else {
                _tmp_1 = _cursor.getString(_cursorIndexOfStatus);
              }
              _tmpStatus = __converters.toScheduleStatus(_tmp_1);
              final String _tmpCrdtKey;
              if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
                _tmpCrdtKey = null;
              } else {
                _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
              }
              final String _tmpUserCrdtKey;
              if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
                _tmpUserCrdtKey = null;
              } else {
                _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
              }
              final Long _tmpUpdateTimestamp;
              if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
                _tmpUpdateTimestamp = null;
              } else {
                _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
              }
              _tmpSchedule = new OrdinaryScheduleEntity(_tmpId,_tmpUserId,_tmpTitle,_tmpDescription,_tmpLocation,_tmpCategory,_tmpColor,_tmpIsAllDay,_tmpStatus,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
              final ArrayList<TimeSlotEntity> _tmpTimeSlotsCollection;
              final long _tmpKey_1;
              _tmpKey_1 = _cursor.getLong(_cursorIndexOfId);
              _tmpTimeSlotsCollection = _collectionTimeSlots.get(_tmpKey_1);
              _item = new OrdinaryScheduleWithTimeSlots(_tmpSchedule,_tmpTimeSlotsCollection);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getIdByCrdtKey(final String crdtKey,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT id FROM ordinary_schedule WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            if (_cursor.isNull(0)) {
              _result = null;
            } else {
              _result = _cursor.getInt(0);
            }
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getOrdinaryScheduleByCrdtKey(final String crdtKey,
      final Continuation<? super OrdinaryScheduleEntity> $completion) {
    final String _sql = "SELECT * FROM ordinary_schedule WHERE crdtKey = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (crdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, crdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<OrdinaryScheduleEntity>() {
      @Override
      @Nullable
      public OrdinaryScheduleEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "is_all_day");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final OrdinaryScheduleEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsAllDay;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAllDay);
            _tmpIsAllDay = _tmp != 0;
            final ScheduleStatus _tmpStatus;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toScheduleStatus(_tmp_1);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _result = new OrdinaryScheduleEntity(_tmpId,_tmpUserId,_tmpTitle,_tmpDescription,_tmpLocation,_tmpCategory,_tmpColor,_tmpIsAllDay,_tmpStatus,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getOrdinarySchedulesByUserCrdtKey(final String userCrdtKey,
      final Continuation<? super List<OrdinaryScheduleEntity>> $completion) {
    final String _sql = "SELECT * FROM ordinary_schedule WHERE userCrdtKey = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userCrdtKey == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userCrdtKey);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<OrdinaryScheduleEntity>>() {
      @Override
      @NonNull
      public List<OrdinaryScheduleEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfColor = CursorUtil.getColumnIndexOrThrow(_cursor, "color");
          final int _cursorIndexOfIsAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "is_all_day");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "crdtKey");
          final int _cursorIndexOfUserCrdtKey = CursorUtil.getColumnIndexOrThrow(_cursor, "userCrdtKey");
          final int _cursorIndexOfUpdateTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "update_timestamp");
          final List<OrdinaryScheduleEntity> _result = new ArrayList<OrdinaryScheduleEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final OrdinaryScheduleEntity _item;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpColor;
            if (_cursor.isNull(_cursorIndexOfColor)) {
              _tmpColor = null;
            } else {
              _tmpColor = _cursor.getString(_cursorIndexOfColor);
            }
            final boolean _tmpIsAllDay;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsAllDay);
            _tmpIsAllDay = _tmp != 0;
            final ScheduleStatus _tmpStatus;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toScheduleStatus(_tmp_1);
            final String _tmpCrdtKey;
            if (_cursor.isNull(_cursorIndexOfCrdtKey)) {
              _tmpCrdtKey = null;
            } else {
              _tmpCrdtKey = _cursor.getString(_cursorIndexOfCrdtKey);
            }
            final String _tmpUserCrdtKey;
            if (_cursor.isNull(_cursorIndexOfUserCrdtKey)) {
              _tmpUserCrdtKey = null;
            } else {
              _tmpUserCrdtKey = _cursor.getString(_cursorIndexOfUserCrdtKey);
            }
            final Long _tmpUpdateTimestamp;
            if (_cursor.isNull(_cursorIndexOfUpdateTimestamp)) {
              _tmpUpdateTimestamp = null;
            } else {
              _tmpUpdateTimestamp = _cursor.getLong(_cursorIndexOfUpdateTimestamp);
            }
            _item = new OrdinaryScheduleEntity(_tmpId,_tmpUserId,_tmpTitle,_tmpDescription,_tmpLocation,_tmpCategory,_tmpColor,_tmpIsAllDay,_tmpStatus,_tmpCrdtKey,_tmpUserCrdtKey,_tmpUpdateTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private void __fetchRelationshiptimeSlotAscomExampleTodoscheduleDataDatabaseEntityTimeSlotEntity(
      @NonNull final LongSparseArray<ArrayList<TimeSlotEntity>> _map) {
    if (_map.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchLongSparseArray(_map, true, (map) -> {
        __fetchRelationshiptimeSlotAscomExampleTodoscheduleDataDatabaseEntityTimeSlotEntity(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`user_id`,`start_time`,`end_time`,`schedule_type`,`schedule_id`,`head`,`priority`,`is_completed`,`is_repeated`,`repeat_pattern`,`reminder_type`,`reminder_offset` FROM `time_slot` WHERE `schedule_id` IN (");
    final int _inputSize = _map.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    for (int i = 0; i < _map.size(); i++) {
      final long _item = _map.keyAt(i);
      _stmt.bindLong(_argIndex, _item);
      _argIndex++;
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, false, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "schedule_id");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfUserId = 1;
      final int _cursorIndexOfStartTime = 2;
      final int _cursorIndexOfEndTime = 3;
      final int _cursorIndexOfScheduleType = 4;
      final int _cursorIndexOfScheduleId = 5;
      final int _cursorIndexOfHead = 6;
      final int _cursorIndexOfPriority = 7;
      final int _cursorIndexOfIsCompleted = 8;
      final int _cursorIndexOfIsRepeated = 9;
      final int _cursorIndexOfRepeatPattern = 10;
      final int _cursorIndexOfReminderType = 11;
      final int _cursorIndexOfReminderOffset = 12;
      while (_cursor.moveToNext()) {
        final long _tmpKey;
        _tmpKey = _cursor.getLong(_itemKeyIndex);
        final ArrayList<TimeSlotEntity> _tmpRelation = _map.get(_tmpKey);
        if (_tmpRelation != null) {
          final TimeSlotEntity _item_1;
          final int _tmpId;
          _tmpId = _cursor.getInt(_cursorIndexOfId);
          final int _tmpUserId;
          _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
          final long _tmpStartTime;
          _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
          final long _tmpEndTime;
          _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
          final ScheduleType _tmpScheduleType;
          final String _tmp;
          if (_cursor.isNull(_cursorIndexOfScheduleType)) {
            _tmp = null;
          } else {
            _tmp = _cursor.getString(_cursorIndexOfScheduleType);
          }
          _tmpScheduleType = __converters.toScheduleType(_tmp);
          final int _tmpScheduleId;
          _tmpScheduleId = _cursor.getInt(_cursorIndexOfScheduleId);
          final String _tmpHead;
          if (_cursor.isNull(_cursorIndexOfHead)) {
            _tmpHead = null;
          } else {
            _tmpHead = _cursor.getString(_cursorIndexOfHead);
          }
          final Integer _tmpPriority;
          if (_cursor.isNull(_cursorIndexOfPriority)) {
            _tmpPriority = null;
          } else {
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
          }
          final boolean _tmpIsCompleted;
          final int _tmp_1;
          _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
          _tmpIsCompleted = _tmp_1 != 0;
          final boolean _tmpIsRepeated;
          final int _tmp_2;
          _tmp_2 = _cursor.getInt(_cursorIndexOfIsRepeated);
          _tmpIsRepeated = _tmp_2 != 0;
          final String _tmpRepeatPattern;
          if (_cursor.isNull(_cursorIndexOfRepeatPattern)) {
            _tmpRepeatPattern = null;
          } else {
            _tmpRepeatPattern = _cursor.getString(_cursorIndexOfRepeatPattern);
          }
          final ReminderType _tmpReminderType;
          final String _tmp_3;
          if (_cursor.isNull(_cursorIndexOfReminderType)) {
            _tmp_3 = null;
          } else {
            _tmp_3 = _cursor.getString(_cursorIndexOfReminderType);
          }
          _tmpReminderType = __converters.toReminderType(_tmp_3);
          final Long _tmpReminderOffset;
          if (_cursor.isNull(_cursorIndexOfReminderOffset)) {
            _tmpReminderOffset = null;
          } else {
            _tmpReminderOffset = _cursor.getLong(_cursorIndexOfReminderOffset);
          }
          _item_1 = new TimeSlotEntity(_tmpId,_tmpUserId,_tmpStartTime,_tmpEndTime,_tmpScheduleType,_tmpScheduleId,_tmpHead,_tmpPriority,_tmpIsCompleted,_tmpIsRepeated,_tmpRepeatPattern,_tmpReminderType,_tmpReminderOffset);
          _tmpRelation.add(_item_1);
        }
      }
    } finally {
      _cursor.close();
    }
  }
}
