# TodoSchedule 数据同步模块使用指南

本文档介绍了如何使用优化后的数据同步模块，确保多设备间的数据可靠同步。

## 核心组件

数据同步模块经过优化，现在包含以下核心组件：

1. **SyncMessageUploader** - 处理消息上传逻辑，包括重试机制和错误处理
2. **SyncRepositoryExtensions** - 提供对SyncRepository的增强功能
3. **NetworkUtils** - 提供通用的网络请求重试功能
4. **TimestampDto** - 修复了序列化格式，确保与API要求一致

## 如何使用

### 1. 上传同步消息

使用增强的上传方法可以更可靠地同步数据：

```kotlin
// 在需要同步数据的组件中注入依赖
@Inject
lateinit var syncRepository: SyncRepository

@Inject
lateinit var syncMessageUploader: SyncMessageUploader

// 使用增强的上传方法
viewModelScope.launch {
    val pendingMessages = syncRepository.getPendingMessagesByType("OrdinarySchedule")
    
    // 使用扩展方法上传消息
    val syncedIds = syncRepository.uploadMessagesEnhanced(
        messages = pendingMessages,
        entityType = "OrdinarySchedule",
        uploader = syncMessageUploader
    )
    
    if (syncedIds.isNotEmpty()) {
        // 上传成功
        _uiState.update { it.copy(syncStatus = "同步成功") }
    } else {
        // 上传失败
        _uiState.update { it.copy(syncStatus = "同步失败") }
    }
}
```

### 2. 同步特定类型的实体

使用增强的同步方法可以更可靠地同步特定类型的实体：

```kotlin
viewModelScope.launch {
    // 使用扩展方法同步特定类型的实体
    val success = syncRepository.syncEntityTypeEnhanced(
        entityType = "OrdinarySchedule",
        uploader = syncMessageUploader
    )
    
    _uiState.update { it.copy(syncStatus = if (success) "同步成功" else "同步失败") }
}
```

### 3. 同步所有类型的实体

使用增强的同步方法可以更可靠地同步所有类型的实体：

```kotlin
viewModelScope.launch {
    // 使用扩展方法同步所有类型的实体
    val success = syncRepository.syncAllEnhanced(
        uploader = syncMessageUploader
    )
    
    _uiState.update { it.copy(syncStatus = if (success) "全部同步成功" else "部分同步失败") }
}
```

## 技术特性

优化后的数据同步模块具有以下特性：

1. **正确的序列化格式** - 确保消息格式与API要求一致
2. **智能重试机制** - 对网络错误和可恢复的服务器错误进行重试
3. **指数退避策略** - 避免频繁重试给服务器造成压力
4. **完整的错误处理** - 记录详细的错误信息，便于排查问题

## 实现细节

数据同步模块的优化主要集中在以下几个方面：

1. **TimestampDto序列化** - 确保时间戳正确序列化为单一的Long值，而不是嵌套对象
2. **消息格式** - 确保消息格式符合API文档的要求，使用JSON字符串数组传输消息
3. **重试逻辑** - 添加智能重试逻辑，使用指数退避策略处理临时错误
4. **模块化设计** - 将核心功能分离到专门的类中，提高代码可维护性

## 故障排除

如果遇到同步问题，请检查以下几点：

1. 检查网络连接是否正常
2. 检查设备ID是否正确
3. 检查日志中是否有详细的错误信息
4. 确保使用的是增强版的同步方法

## 总结

通过使用优化后的数据同步模块，可以更可靠地在多设备间同步数据，确保用户数据的一致性和可用性。
