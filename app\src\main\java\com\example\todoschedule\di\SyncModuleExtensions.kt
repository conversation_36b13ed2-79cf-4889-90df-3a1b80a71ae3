package com.example.todoschedule.di

import com.example.todoschedule.data.database.AppDatabase
import com.example.todoschedule.data.remote.api.SyncApi as RemoteSyncApi
import com.example.todoschedule.data.sync.DeviceIdManager
import com.example.todoschedule.data.sync.SyncMessageUploader
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 同步模块扩展
 * 提供增强的同步功能依赖
 */
@Module
@InstallIn(SingletonComponent::class)
object SyncModuleExtensions {
    
    /**
     * 提供同步消息上传器
     * 用于处理消息上传逻辑，包括重试机制和错误处理
     */
    @Provides
    @Singleton
    fun provideSyncMessageUploader(
        syncApi: RemoteSyncApi,
        database: AppDatabase,
        deviceIdManager: DeviceIdManager
    ): SyncMessageUploader {
        return SyncMessageUploader(
            syncApi = syncApi,
            syncMessageDao = database.syncMessageDao(),
            deviceIdManager = deviceIdManager
        )
    }
}
