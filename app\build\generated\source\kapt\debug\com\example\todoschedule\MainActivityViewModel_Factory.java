// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule;

import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivityViewModel_Factory implements Factory<MainActivityViewModel> {
  private final Provider<SessionRepository> sessionRepositoryProvider;

  public MainActivityViewModel_Factory(Provider<SessionRepository> sessionRepositoryProvider) {
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public MainActivityViewModel get() {
    return newInstance(sessionRepositoryProvider.get());
  }

  public static MainActivityViewModel_Factory create(
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new MainActivityViewModel_Factory(sessionRepositoryProvider);
  }

  public static MainActivityViewModel newInstance(SessionRepository sessionRepository) {
    return new MainActivityViewModel(sessionRepository);
  }
}
