// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.sync;

import android.content.Context;
import com.example.todoschedule.data.repository.SyncRepository;
import com.example.todoschedule.data.sync.SyncManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncViewModel_Factory implements Factory<SyncViewModel> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<SyncManager> syncManagerProvider;

  private final Provider<Context> contextProvider;

  public SyncViewModel_Factory(Provider<SyncRepository> syncRepositoryProvider,
      Provider<SyncManager> syncManagerProvider, Provider<Context> contextProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.syncManagerProvider = syncManagerProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public SyncViewModel get() {
    return newInstance(syncRepositoryProvider.get(), syncManagerProvider.get(), contextProvider.get());
  }

  public static SyncViewModel_Factory create(Provider<SyncRepository> syncRepositoryProvider,
      Provider<SyncManager> syncManagerProvider, Provider<Context> contextProvider) {
    return new SyncViewModel_Factory(syncRepositoryProvider, syncManagerProvider, contextProvider);
  }

  public static SyncViewModel newInstance(SyncRepository syncRepository, SyncManager syncManager,
      Context context) {
    return new SyncViewModel(syncRepository, syncManager, context);
  }
}
