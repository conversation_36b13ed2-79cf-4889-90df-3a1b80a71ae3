package com.example.todoschedule.ui.sync

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.SyncProblem
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.todoschedule.ui.sync.SyncViewModel.SyncState

/**
 * 同步状态指示器
 *
 * 显示当前同步状态、最后同步时间和待同步消息数量，并提供手动同步按钮
 *
 * @param syncState 同步状态
 * @param lastSyncTimeText 上次同步时间文本
 * @param pendingCount 待同步消息数量
 * @param onSyncClick 手动同步点击事件处理
 */
@Composable
fun SyncStatusIndicator(
    syncState: SyncState,
    lastSyncTimeText: String,
    pendingCount: Int,
    onSyncClick: () -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(8.dp)
    ) {
        // 同步状态图标
        when (syncState) {
            SyncState.SYNCING -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp
                )
            }
            else -> {
                val icon = when (syncState) {
                    SyncState.SYNCED -> Icons.Filled.Sync
                    SyncState.FAILED -> Icons.Filled.SyncProblem
                    else -> Icons.Filled.Sync
                }
                
                val tint = when (syncState) {
                    SyncState.SYNCED -> Color.Green
                    SyncState.FAILED -> Color.Red
                    else -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                }
                
                Icon(
                    imageVector = icon,
                    contentDescription = "同步状态",
                    tint = tint,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 同步信息
        Column {
            // 同步状态文本
            val statusText = when (syncState) {
                SyncState.IDLE -> "等待同步"
                SyncState.SYNCING -> "正在同步..."
                SyncState.SYNCED -> "同步成功"
                SyncState.FAILED -> "同步失败"
            }
            Text(text = statusText)
            
            // 上次同步时间
            Text(
                text = lastSyncTimeText,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            
            // 待同步消息数量
            if (pendingCount > 0) {
                Text(
                    text = "待同步: $pendingCount 项",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 手动同步按钮
        if (syncState != SyncState.SYNCING) {
            IconButton(onClick = onSyncClick) {
                Icon(
                    imageVector = Icons.Filled.Refresh,
                    contentDescription = "立即同步"
                )
            }
        }
    }
}

/**
 * 同步错误对话框
 *
 * @param errorMessage 错误消息
 * @param onDismiss 关闭对话框回调
 * @param onRetry 重试回调
 */
@Composable
fun SyncErrorDialog(
    errorMessage: String,
    onDismiss: () -> Unit,
    onRetry: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("同步错误") },
        text = { Text(errorMessage) },
        confirmButton = {
            TextButton(onClick = onRetry) {
                Text("重试")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("稍后再试")
            }
        }
    )
} 