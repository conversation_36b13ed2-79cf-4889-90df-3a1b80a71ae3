// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.sync.adapter.CourseAdapter;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideCourseAdapterFactory implements Factory<CourseAdapter> {
  @Override
  public CourseAdapter get() {
    return provideCourseAdapter();
  }

  public static SyncModule_ProvideCourseAdapterFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static CourseAdapter provideCourseAdapter() {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideCourseAdapter());
  }

  private static final class InstanceHolder {
    private static final SyncModule_ProvideCourseAdapterFactory INSTANCE = new SyncModule_ProvideCourseAdapterFactory();
  }
}
