// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.auth;

import com.example.todoschedule.domain.repository.SessionRepository;
import com.example.todoschedule.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SaveLoginSessionUseCase_Factory implements Factory<SaveLoginSessionUseCase> {
  private final Provider<SessionRepository> sessionRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public SaveLoginSessionUseCase_Factory(Provider<SessionRepository> sessionRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.sessionRepositoryProvider = sessionRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public SaveLoginSessionUseCase get() {
    return newInstance(sessionRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static SaveLoginSessionUseCase_Factory create(
      Provider<SessionRepository> sessionRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new SaveLoginSessionUseCase_Factory(sessionRepositoryProvider, userRepositoryProvider);
  }

  public static SaveLoginSessionUseCase newInstance(SessionRepository sessionRepository,
      UserRepository userRepository) {
    return new SaveLoginSessionUseCase(sessionRepository, userRepository);
  }
}
