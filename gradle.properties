# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
org.gradle.parallel=true
# 启用Gradle守护进程
org.gradle.daemon=true
# 启用Gradle缓存
org.gradle.caching=true
# 启用按需配置
org.gradle.configureondemand=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
# 启用Jetifier，帮助支持还未迁移到AndroidX的库
android.enableJetifier=true
# 开启Compose编译器报告
kotlin.daemon.jvmargs=-Xmx2048m
# 启用Kotlin增量编译
kotlin.incremental=true
# 启用Kotlin编译缓存
kotlin.caching.enabled=true
# 启用Kapt增量编译
kapt.incremental.apt=true
# 避免Kapt运行不必要的注解处理器
kapt.include.compile.classpath=false
# 编译优化相关配置
# 启用新的Gradle工作模式 - 提高构建性能
org.gradle.unsafe.configuration-cache=true
# 使用更多并行工作线程
org.gradle.workers.max=8
# 优化编译产物
android.enableR8.fullMode=true
# 启用矢量图形优化
android.enableVectorDrawables.useSupportLibrary=true
# 禁用Gradle源码下载
gradle.runtime.sources.download=false
# 增加网络连接超时时间
org.gradle.internal.http.socketTimeout=120000
org.gradle.internal.http.connectionTimeout=120000