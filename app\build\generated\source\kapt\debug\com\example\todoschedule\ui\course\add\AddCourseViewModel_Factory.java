// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.course.add;

import com.example.todoschedule.domain.repository.CourseRepository;
import com.example.todoschedule.domain.repository.TableRepository;
import com.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddCourseViewModel_Factory implements Factory<AddCourseViewModel> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<TableRepository> tableRepositoryProvider;

  private final Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider;

  public AddCourseViewModel_Factory(Provider<CourseRepository> courseRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.tableRepositoryProvider = tableRepositoryProvider;
    this.getLoginUserIdFlowUseCaseProvider = getLoginUserIdFlowUseCaseProvider;
  }

  @Override
  public AddCourseViewModel get() {
    return newInstance(courseRepositoryProvider.get(), tableRepositoryProvider.get(), getLoginUserIdFlowUseCaseProvider.get());
  }

  public static AddCourseViewModel_Factory create(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider) {
    return new AddCourseViewModel_Factory(courseRepositoryProvider, tableRepositoryProvider, getLoginUserIdFlowUseCaseProvider);
  }

  public static AddCourseViewModel newInstance(CourseRepository courseRepository,
      TableRepository tableRepository, GetLoginUserIdFlowUseCase getLoginUserIdFlowUseCase) {
    return new AddCourseViewModel(courseRepository, tableRepository, getLoginUserIdFlowUseCase);
  }
}
