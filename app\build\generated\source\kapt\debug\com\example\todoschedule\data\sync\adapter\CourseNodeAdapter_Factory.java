// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync.adapter;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CourseNodeAdapter_Factory implements Factory<CourseNodeAdapter> {
  @Override
  public CourseNodeAdapter get() {
    return newInstance();
  }

  public static CourseNodeAdapter_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static CourseNodeAdapter newInstance() {
    return new CourseNodeAdapter();
  }

  private static final class InstanceHolder {
    private static final CourseNodeAdapter_Factory INSTANCE = new CourseNodeAdapter_Factory();
  }
}
