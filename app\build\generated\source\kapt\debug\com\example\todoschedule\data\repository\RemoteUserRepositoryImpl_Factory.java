// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.remote.api.UserApiService;
import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RemoteUserRepositoryImpl_Factory implements Factory<RemoteUserRepositoryImpl> {
  private final Provider<UserApiService> userApiServiceProvider;

  private final Provider<SessionRepository> sessionRepositoryProvider;

  public RemoteUserRepositoryImpl_Factory(Provider<UserApiService> userApiServiceProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    this.userApiServiceProvider = userApiServiceProvider;
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public RemoteUserRepositoryImpl get() {
    return newInstance(userApiServiceProvider.get(), sessionRepositoryProvider.get());
  }

  public static RemoteUserRepositoryImpl_Factory create(
      Provider<UserApiService> userApiServiceProvider,
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new RemoteUserRepositoryImpl_Factory(userApiServiceProvider, sessionRepositoryProvider);
  }

  public static RemoteUserRepositoryImpl newInstance(UserApiService userApiService,
      SessionRepository sessionRepository) {
    return new RemoteUserRepositoryImpl(userApiService, sessionRepository);
  }
}
