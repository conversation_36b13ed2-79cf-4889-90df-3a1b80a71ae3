pluginManagement {
    repositories {
        // 阿里云镜像
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
        
        // 华为云镜像
        maven { url = uri("https://repo.huaweicloud.com/repository/maven") }
        
        // JitPack仓库 - 用于Synk和HLC库
        maven { url = uri("https://jitpack.io") }
        
        // 原有配置作为备选
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        // 阿里云镜像
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
        
        // 华为云镜像
        maven { url = uri("https://repo.huaweicloud.com/repository/maven") }
        
        // JitPack仓库 - 用于Synk和HLC库
        maven { url = uri("https://jitpack.io") }
        
        // 原有配置作为备选
        google()
        mavenCentral()
    }
}

rootProject.name = "TodoSchedule"
include(":app")
 