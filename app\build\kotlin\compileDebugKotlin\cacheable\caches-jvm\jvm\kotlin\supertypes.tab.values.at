/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel android.app.Application androidx.room.RoomDatabase kotlin.Enum kotlin.Enum kotlin.Enum7 6com.example.todoschedule.data.database.entity.Syncable7 6com.example.todoschedule.data.database.entity.Syncable7 6com.example.todoschedule.data.database.entity.Syncable7 6com.example.todoschedule.data.database.entity.Syncable< ;com.example.todoschedule.domain.repository.CourseRepositoryC Bcom.example.todoschedule.domain.repository.GlobalSettingRepositoryF Ecom.example.todoschedule.domain.repository.OrdinaryScheduleRepository@ ?com.example.todoschedule.domain.repository.RemoteUserRepository= <com.example.todoschedule.domain.repository.SessionRepository8 7com.example.todoschedule.data.repository.SyncRepository; :com.example.todoschedule.domain.repository.TableRepositoryE Dcom.example.todoschedule.domain.repository.TableTimeConfigRepository: 9com.example.todoschedule.domain.repository.UserRepository+ *com.example.todoschedule.data.sync.SyncApi kotlin.Enum kotlin.Enum kotlin.Enum android.app.Service android.os.Binder androidx.work.CoroutineWorker? >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter? >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter? >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter7 6com.example.todoschedule.data.sync.adapter.SynkAdapter? >com.example.todoschedule.data.sync.adapter.AbstractSynkAdapter android.os.Parcelable3 2kotlinx.serialization.internal.GeneratedSerializer" !kotlinx.serialization.KSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer android.os.Parcelable3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel> =com.example.todoschedule.ui.components.PermissionTextProvider androidx.lifecycle.ViewModel1 0com.example.todoschedule.ui.course.add.SaveState1 0com.example.todoschedule.ui.course.add.SaveState1 0com.example.todoschedule.ui.course.add.SaveState1 0com.example.todoschedule.ui.course.add.SaveState> =com.example.todoschedule.ui.course.detail.CourseDetailUiState> =com.example.todoschedule.ui.course.detail.CourseDetailUiState> =com.example.todoschedule.ui.course.detail.CourseDetailUiState> =com.example.todoschedule.ui.course.detail.CourseDetailUiState androidx.lifecycle.ViewModel8 7com.example.todoschedule.ui.course.edit.EditCourseEvent8 7com.example.todoschedule.ui.course.edit.EditCourseEvent kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel8 7com.example.todoschedule.ui.course.load.SaveCourseState8 7com.example.todoschedule.ui.course.load.SaveCourseState8 7com.example.todoschedule.ui.course.load.SaveCourseState8 7com.example.todoschedule.ui.course.load.SaveCourseState androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes1 0com.example.todoschedule.ui.navigation.AppRoutes androidx.lifecycle.ViewModelK Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiStateK Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiStateK Jcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailUiStateI Hcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEventI Hcom.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailEvent androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel; :com.example.todoschedule.ui.schedule.model.ScheduleUiState; :com.example.todoschedule.ui.schedule.model.ScheduleUiState; :com.example.todoschedule.ui.schedule.model.ScheduleUiState; :com.example.todoschedule.ui.schedule.model.ScheduleUiState; :com.example.todoschedule.ui.schedule.model.ScheduleUiState$ #androidx.lifecycle.AndroidViewModel7 6com.example.todoschedule.ui.settings.DatabaseOperation7 6com.example.todoschedule.ui.settings.DatabaseOperation7 6com.example.todoschedule.ui.settings.DatabaseOperation7 6com.example.todoschedule.ui.settings.DatabaseOperation androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum1 0com.example.todoschedule.ui.task.TaskItemUiModel1 0com.example.todoschedule.ui.task.TaskItemUiModel kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum2 1com.example.todoschedule.ui.theme.ColorSchemeEnum kotlin.Enum- ,com.example.todoschedule.ui.todo.TodoUiState- ,com.example.todoschedule.ui.todo.TodoUiState- ,com.example.todoschedule.ui.todo.TodoUiState- ,com.example.todoschedule.ui.todo.TodoUiState androidx.lifecycle.ViewModel' &com.example.todoschedule.util.Resource' &com.example.todoschedule.util.Resource' &com.example.todoschedule.util.Resource 
parser.Parser 
parser.Parser