// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.sync.adapter.CourseAdapter;
import com.example.todoschedule.data.sync.adapter.CourseNodeAdapter;
import com.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter;
import com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry;
import com.example.todoschedule.data.sync.adapter.TableAdapter;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideSynkAdapterRegistryFactory implements Factory<SynkAdapterRegistry> {
  private final Provider<CourseAdapter> courseAdapterProvider;

  private final Provider<TableAdapter> tableAdapterProvider;

  private final Provider<CourseNodeAdapter> courseNodeAdapterProvider;

  private final Provider<OrdinaryScheduleAdapter> ordinaryScheduleAdapterProvider;

  public SyncModule_ProvideSynkAdapterRegistryFactory(Provider<CourseAdapter> courseAdapterProvider,
      Provider<TableAdapter> tableAdapterProvider,
      Provider<CourseNodeAdapter> courseNodeAdapterProvider,
      Provider<OrdinaryScheduleAdapter> ordinaryScheduleAdapterProvider) {
    this.courseAdapterProvider = courseAdapterProvider;
    this.tableAdapterProvider = tableAdapterProvider;
    this.courseNodeAdapterProvider = courseNodeAdapterProvider;
    this.ordinaryScheduleAdapterProvider = ordinaryScheduleAdapterProvider;
  }

  @Override
  public SynkAdapterRegistry get() {
    return provideSynkAdapterRegistry(courseAdapterProvider.get(), tableAdapterProvider.get(), courseNodeAdapterProvider.get(), ordinaryScheduleAdapterProvider.get());
  }

  public static SyncModule_ProvideSynkAdapterRegistryFactory create(
      Provider<CourseAdapter> courseAdapterProvider, Provider<TableAdapter> tableAdapterProvider,
      Provider<CourseNodeAdapter> courseNodeAdapterProvider,
      Provider<OrdinaryScheduleAdapter> ordinaryScheduleAdapterProvider) {
    return new SyncModule_ProvideSynkAdapterRegistryFactory(courseAdapterProvider, tableAdapterProvider, courseNodeAdapterProvider, ordinaryScheduleAdapterProvider);
  }

  public static SynkAdapterRegistry provideSynkAdapterRegistry(CourseAdapter courseAdapter,
      TableAdapter tableAdapter, CourseNodeAdapter courseNodeAdapter,
      OrdinaryScheduleAdapter ordinaryScheduleAdapter) {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideSynkAdapterRegistry(courseAdapter, tableAdapter, courseNodeAdapter, ordinaryScheduleAdapter));
  }
}
