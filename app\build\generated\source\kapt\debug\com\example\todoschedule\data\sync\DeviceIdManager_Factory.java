// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DeviceIdManager_Factory implements Factory<DeviceIdManager> {
  private final Provider<Context> contextProvider;

  public DeviceIdManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DeviceIdManager get() {
    return newInstance(contextProvider.get());
  }

  public static DeviceIdManager_Factory create(Provider<Context> contextProvider) {
    return new DeviceIdManager_Factory(contextProvider);
  }

  public static DeviceIdManager newInstance(Context context) {
    return new DeviceIdManager(context);
  }
}
