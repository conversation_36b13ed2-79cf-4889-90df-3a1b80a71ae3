// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.auth;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HashPasswordUseCase_Factory implements Factory<HashPasswordUseCase> {
  @Override
  public HashPasswordUseCase get() {
    return newInstance();
  }

  public static HashPasswordUseCase_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static HashPasswordUseCase newInstance() {
    return new HashPasswordUseCase();
  }

  private static final class InstanceHolder {
    private static final HashPasswordUseCase_Factory INSTANCE = new HashPasswordUseCase_Factory();
  }
}
