// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui;

import com.example.todoschedule.data.repository.SyncRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  public MainActivity_MembersInjector(Provider<SyncRepository> syncRepositoryProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<SyncRepository> syncRepositoryProvider) {
    return new MainActivity_MembersInjector(syncRepositoryProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectSyncRepository(instance, syncRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.example.todoschedule.ui.MainActivity.syncRepository")
  public static void injectSyncRepository(MainActivity instance, SyncRepository syncRepository) {
    instance.syncRepository = syncRepository;
  }
}
