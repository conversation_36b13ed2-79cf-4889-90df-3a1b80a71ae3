## 同步功能测试步骤

1. 启动应用并登录
2. 点击底部导航栏的「我的」进入个人信息页面
3. 点击「数据同步」下的「同步设置」
4. 在同步设置界面查看同步状态，点击「立即同步」按钮测试手动同步
5. 查看同步状态指示变化

## 同步功能测试验证

要验证同步功能是否正常工作，请检查以下几点：

1. 打开应用时应该会自动启动同步服务
2. 在同步设置页面应该能看到正确的同步状态
3. 点击「立即同步」按钮后同步状态应变为「同步中」，然后变为「已同步」或「同步失败」
4. 多台设备间的数据应该能正确同步

## 排查同步问题

如果出现初始化错误，请检查以下几点：

1. **SyncService注入问题**：确保SyncService在AndroidManifest.xml中正确注册，并且正确注入了SyncRepository
2. **网络连接问题**：确保设备有网络连接，且服务器地址配置正确
3. **日志分析**：查看日志中"SyncService"、"SyncManager"相关的日志，特别注意错误信息

## 多设备测试方法

1. 在设备A上添加或修改数据
2. 手动触发同步（或等待自动同步）
3. 在设备B上打开应用，检查数据是否同步过来
4. 在设备B上修改同一数据，再次触发同步
5. 回到设备A验证修改是否同步，并检查冲突解决是否正确
