// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.database.dao.TimeSlotDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideTimeSlotDaoFactory implements Factory<TimeSlotDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideTimeSlotDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public TimeSlotDao get() {
    return provideTimeSlotDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideTimeSlotDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideTimeSlotDaoFactory(databaseProvider);
  }

  public static TimeSlotDao provideTimeSlotDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideTimeSlotDao(database));
  }
}
