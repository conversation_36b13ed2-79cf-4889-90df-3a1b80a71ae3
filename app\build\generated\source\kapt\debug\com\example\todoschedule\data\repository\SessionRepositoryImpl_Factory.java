// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SessionRepositoryImpl_Factory implements Factory<SessionRepositoryImpl> {
  private final Provider<DataStore<Preferences>> dataStoreProvider;

  public SessionRepositoryImpl_Factory(Provider<DataStore<Preferences>> dataStoreProvider) {
    this.dataStoreProvider = dataStoreProvider;
  }

  @Override
  public SessionRepositoryImpl get() {
    return newInstance(dataStoreProvider.get());
  }

  public static SessionRepositoryImpl_Factory create(
      Provider<DataStore<Preferences>> dataStoreProvider) {
    return new SessionRepositoryImpl_Factory(dataStoreProvider);
  }

  public static SessionRepositoryImpl newInstance(DataStore<Preferences> dataStore) {
    return new SessionRepositoryImpl(dataStore);
  }
}
