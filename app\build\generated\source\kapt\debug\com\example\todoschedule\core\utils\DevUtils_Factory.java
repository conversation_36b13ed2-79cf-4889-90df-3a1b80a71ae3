// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.core.utils;

import android.content.Context;
import com.example.todoschedule.data.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DevUtils_Factory implements Factory<DevUtils> {
  private final Provider<Context> contextProvider;

  private final Provider<AppDatabase> dbProvider;

  public DevUtils_Factory(Provider<Context> contextProvider, Provider<AppDatabase> dbProvider) {
    this.contextProvider = contextProvider;
    this.dbProvider = dbProvider;
  }

  @Override
  public DevUtils get() {
    return newInstance(contextProvider.get(), dbProvider.get());
  }

  public static DevUtils_Factory create(Provider<Context> contextProvider,
      Provider<AppDatabase> dbProvider) {
    return new DevUtils_Factory(contextProvider, dbProvider);
  }

  public static DevUtils newInstance(Context context, AppDatabase db) {
    return new DevUtils(context, db);
  }
}
