// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync.adapter;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CourseAdapter_Factory implements Factory<CourseAdapter> {
  @Override
  public CourseAdapter get() {
    return newInstance();
  }

  public static CourseAdapter_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static CourseAdapter newInstance() {
    return new CourseAdapter();
  }

  private static final class InstanceHolder {
    private static final CourseAdapter_Factory INSTANCE = new CourseAdapter_Factory();
  }
}
