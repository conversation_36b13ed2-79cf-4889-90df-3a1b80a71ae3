// Generated by Da<PERSON> (https://dagger.dev).
package com.example.todoschedule.data.sync;

import com.example.todoschedule.data.repository.SyncRepository;
import com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncManager_Factory implements Factory<SyncManager> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<DeviceIdManager> deviceIdManagerProvider;

  private final Provider<SynkAdapterRegistry> synkAdapterRegistryProvider;

  private final Provider<CrdtKeyResolver> crdtKeyResolverProvider;

  public SyncManager_Factory(Provider<SyncRepository> syncRepositoryProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<SynkAdapterRegistry> synkAdapterRegistryProvider,
      Provider<CrdtKeyResolver> crdtKeyResolverProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.deviceIdManagerProvider = deviceIdManagerProvider;
    this.synkAdapterRegistryProvider = synkAdapterRegistryProvider;
    this.crdtKeyResolverProvider = crdtKeyResolverProvider;
  }

  @Override
  public SyncManager get() {
    return newInstance(syncRepositoryProvider.get(), deviceIdManagerProvider.get(), synkAdapterRegistryProvider.get(), crdtKeyResolverProvider.get());
  }

  public static SyncManager_Factory create(Provider<SyncRepository> syncRepositoryProvider,
      Provider<DeviceIdManager> deviceIdManagerProvider,
      Provider<SynkAdapterRegistry> synkAdapterRegistryProvider,
      Provider<CrdtKeyResolver> crdtKeyResolverProvider) {
    return new SyncManager_Factory(syncRepositoryProvider, deviceIdManagerProvider, synkAdapterRegistryProvider, crdtKeyResolverProvider);
  }

  public static SyncManager newInstance(SyncRepository syncRepository,
      DeviceIdManager deviceIdManager, SynkAdapterRegistry synkAdapterRegistry,
      CrdtKeyResolver crdtKeyResolver) {
    return new SyncManager(syncRepository, deviceIdManager, synkAdapterRegistry, crdtKeyResolver);
  }
}
