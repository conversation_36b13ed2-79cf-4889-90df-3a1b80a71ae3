// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.auth;

import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ClearLoginSessionUseCase_Factory implements Factory<ClearLoginSessionUseCase> {
  private final Provider<SessionRepository> sessionRepositoryProvider;

  public ClearLoginSessionUseCase_Factory(Provider<SessionRepository> sessionRepositoryProvider) {
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public ClearLoginSessionUseCase get() {
    return newInstance(sessionRepositoryProvider.get());
  }

  public static ClearLoginSessionUseCase_Factory create(
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new ClearLoginSessionUseCase_Factory(sessionRepositoryProvider);
  }

  public static ClearLoginSessionUseCase newInstance(SessionRepository sessionRepository) {
    return new ClearLoginSessionUseCase(sessionRepository);
  }
}
