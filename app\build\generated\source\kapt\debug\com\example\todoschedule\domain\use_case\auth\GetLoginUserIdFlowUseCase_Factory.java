// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.auth;

import com.example.todoschedule.domain.repository.SessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetLoginUserIdFlowUseCase_Factory implements Factory<GetLoginUserIdFlowUseCase> {
  private final Provider<SessionRepository> sessionRepositoryProvider;

  public GetLoginUserIdFlowUseCase_Factory(Provider<SessionRepository> sessionRepositoryProvider) {
    this.sessionRepositoryProvider = sessionRepositoryProvider;
  }

  @Override
  public GetLoginUserIdFlowUseCase get() {
    return newInstance(sessionRepositoryProvider.get());
  }

  public static GetLoginUserIdFlowUseCase_Factory create(
      Provider<SessionRepository> sessionRepositoryProvider) {
    return new GetLoginUserIdFlowUseCase_Factory(sessionRepositoryProvider);
  }

  public static GetLoginUserIdFlowUseCase newInstance(SessionRepository sessionRepository) {
    return new GetLoginUserIdFlowUseCase(sessionRepository);
  }
}
