// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import androidx.fragment.app.Fragment;
import androidx.hilt.work.HiltWrapper_WorkerFactoryModule;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.example.todoschedule.core.utils.DevUtils;
import com.example.todoschedule.core.utils.PermissionManager;
import com.example.todoschedule.data.database.AppDatabase;
import com.example.todoschedule.data.database.dao.CourseDao;
import com.example.todoschedule.data.database.dao.GlobalSettingDao;
import com.example.todoschedule.data.database.dao.OrdinaryScheduleDao;
import com.example.todoschedule.data.database.dao.TableDao;
import com.example.todoschedule.data.database.dao.TableTimeConfigDao;
import com.example.todoschedule.data.database.dao.TimeSlotDao;
import com.example.todoschedule.data.database.dao.UserDao;
import com.example.todoschedule.data.remote.api.SyncApi;
import com.example.todoschedule.data.remote.api.UserApiService;
import com.example.todoschedule.data.repository.CourseRepositoryImpl;
import com.example.todoschedule.data.repository.GlobalSettingRepositoryImpl;
import com.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl;
import com.example.todoschedule.data.repository.RemoteUserRepositoryImpl;
import com.example.todoschedule.data.repository.SessionRepositoryImpl;
import com.example.todoschedule.data.repository.SyncRepository;
import com.example.todoschedule.data.repository.TableRepositoryImpl;
import com.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl;
import com.example.todoschedule.data.repository.UserRepositoryImpl;
import com.example.todoschedule.data.sync.CrdtKeyResolver;
import com.example.todoschedule.data.sync.DeviceIdManager;
import com.example.todoschedule.data.sync.SyncManager;
import com.example.todoschedule.data.sync.SyncService;
import com.example.todoschedule.data.sync.SyncService_MembersInjector;
import com.example.todoschedule.data.sync.adapter.CourseAdapter;
import com.example.todoschedule.data.sync.adapter.CourseNodeAdapter;
import com.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter;
import com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry;
import com.example.todoschedule.data.sync.adapter.TableAdapter;
import com.example.todoschedule.di.AppModule;
import com.example.todoschedule.di.DataStoreModule;
import com.example.todoschedule.di.DataStoreModule_ProvidePreferencesDataStoreFactory;
import com.example.todoschedule.di.DatabaseModule;
import com.example.todoschedule.di.DatabaseModule_ProvideAppDatabaseFactory;
import com.example.todoschedule.di.DatabaseModule_ProvideCourseDaoFactory;
import com.example.todoschedule.di.DatabaseModule_ProvideGlobalSettingDaoFactory;
import com.example.todoschedule.di.DatabaseModule_ProvideOrdinaryScheduleDaoFactory;
import com.example.todoschedule.di.DatabaseModule_ProvideTableDaoFactory;
import com.example.todoschedule.di.DatabaseModule_ProvideTimeConfigDaoFactory;
import com.example.todoschedule.di.DatabaseModule_ProvideTimeSlotDaoFactory;
import com.example.todoschedule.di.DatabaseModule_ProvideUserDaoFactory;
import com.example.todoschedule.di.NetworkModule;
import com.example.todoschedule.di.NetworkModule_ProvideAuthInterceptorFactory;
import com.example.todoschedule.di.NetworkModule_ProvideHttpLoggingInterceptorFactory;
import com.example.todoschedule.di.NetworkModule_ProvideMoshiFactory;
import com.example.todoschedule.di.NetworkModule_ProvideOkHttpClientFactory;
import com.example.todoschedule.di.NetworkModule_ProvideRetrofitFactory;
import com.example.todoschedule.di.NetworkModule_ProvideSyncApiServiceFactory;
import com.example.todoschedule.di.NetworkModule_ProvideUserApiServiceFactory;
import com.example.todoschedule.di.SyncModule;
import com.example.todoschedule.di.SyncModuleExtensions;
import com.example.todoschedule.di.SyncModule_ProvideCourseAdapterFactory;
import com.example.todoschedule.di.SyncModule_ProvideCourseNodeAdapterFactory;
import com.example.todoschedule.di.SyncModule_ProvideDeviceIdManagerFactory;
import com.example.todoschedule.di.SyncModule_ProvideOrdinaryScheduleAdapterFactory;
import com.example.todoschedule.di.SyncModule_ProvideSyncManagerFactory;
import com.example.todoschedule.di.SyncModule_ProvideSyncRepositoryFactory;
import com.example.todoschedule.di.SyncModule_ProvideSynkAdapterRegistryFactory;
import com.example.todoschedule.di.SyncModule_ProvideTableAdapterFactory;
import com.example.todoschedule.domain.repository.CourseRepository;
import com.example.todoschedule.domain.repository.GlobalSettingRepository;
import com.example.todoschedule.domain.repository.OrdinaryScheduleRepository;
import com.example.todoschedule.domain.repository.TableTimeConfigRepository;
import com.example.todoschedule.domain.use_case.auth.ClearLoginSessionUseCase;
import com.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase;
import com.example.todoschedule.domain.use_case.auth.HashPasswordUseCase;
import com.example.todoschedule.domain.use_case.auth.LoginUserUseCase;
import com.example.todoschedule.domain.use_case.auth.RegisterUserUseCase;
import com.example.todoschedule.domain.use_case.auth.SaveLoginSessionUseCase;
import com.example.todoschedule.domain.use_case.auth.VerifyPasswordUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.AddOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.DeleteOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinaryScheduleByIdUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.GetOrdinarySchedulesUseCase;
import com.example.todoschedule.domain.use_case.ordinary_schedule.UpdateOrdinaryScheduleUseCase;
import com.example.todoschedule.domain.use_case.table_time_config.GetDefaultTableTimeConfigUseCase;
import com.example.todoschedule.domain.utils.CalendarSyncManager;
import com.example.todoschedule.ui.MainActivity_MembersInjector;
import com.example.todoschedule.ui.auth.LoginViewModel;
import com.example.todoschedule.ui.auth.LoginViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.auth.RegisterViewModel;
import com.example.todoschedule.ui.auth.RegisterViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.course.add.AddCourseViewModel;
import com.example.todoschedule.ui.course.add.AddCourseViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.course.detail.CourseDetailViewModel;
import com.example.todoschedule.ui.course.detail.CourseDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.course.edit.EditCourseViewModel;
import com.example.todoschedule.ui.course.edit.EditCourseViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.course.load.WebViewScreenViewModel;
import com.example.todoschedule.ui.course.load.WebViewScreenViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.home.HomeViewModel;
import com.example.todoschedule.ui.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.navigation.SessionViewModel;
import com.example.todoschedule.ui.navigation.SessionViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel;
import com.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel;
import com.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel;
import com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.schedule.ScheduleViewModel;
import com.example.todoschedule.ui.schedule.ScheduleViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.settings.SettingsViewModel;
import com.example.todoschedule.ui.settings.SettingsViewModel_Factory;
import com.example.todoschedule.ui.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.settings.SettingsViewModel_MembersInjector;
import com.example.todoschedule.ui.sync.SyncViewModel;
import com.example.todoschedule.ui.sync.SyncViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.table.CreateEditTableViewModel;
import com.example.todoschedule.ui.table.CreateEditTableViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.task.TaskCalendarViewModel;
import com.example.todoschedule.ui.task.TaskCalendarViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.task.TaskViewModel;
import com.example.todoschedule.ui.task.TaskViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.todoschedule.ui.todo.TodoViewModel;
import com.example.todoschedule.ui.todo.TodoViewModel_HiltModules_KeyModule_ProvideFactory;
import com.squareup.moshi.Moshi;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideApplicationFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DelegateFactory;
import dagger.internal.DoubleCheck;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.SetBuilder;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.inject.Provider;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerTodoScheduleApplication_HiltComponents_SingletonC {
  private DaggerTodoScheduleApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder appModule(AppModule appModule) {
      Preconditions.checkNotNull(appModule);
      return this;
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder dataStoreModule(DataStoreModule dataStoreModule) {
      Preconditions.checkNotNull(dataStoreModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder databaseModule(DatabaseModule databaseModule) {
      Preconditions.checkNotNull(databaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_WorkerFactoryModule(
        HiltWrapper_WorkerFactoryModule hiltWrapper_WorkerFactoryModule) {
      Preconditions.checkNotNull(hiltWrapper_WorkerFactoryModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder networkModule(NetworkModule networkModule) {
      Preconditions.checkNotNull(networkModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder syncModule(SyncModule syncModule) {
      Preconditions.checkNotNull(syncModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder syncModuleExtensions(SyncModuleExtensions syncModuleExtensions) {
      Preconditions.checkNotNull(syncModuleExtensions);
      return this;
    }

    public TodoScheduleApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements TodoScheduleApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public TodoScheduleApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements TodoScheduleApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public TodoScheduleApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements TodoScheduleApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public TodoScheduleApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements TodoScheduleApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public TodoScheduleApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements TodoScheduleApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public TodoScheduleApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements TodoScheduleApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public TodoScheduleApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements TodoScheduleApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public TodoScheduleApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends TodoScheduleApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends TodoScheduleApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends TodoScheduleApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends TodoScheduleApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
    }

    @Override
    public void injectMainActivity(com.example.todoschedule.ui.MainActivity mainActivity) {
      injectMainActivity2(mainActivity);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return SetBuilder.<String>newSetBuilder(19).add(AddCourseViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AddEditOrdinaryScheduleViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(CourseDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(CreateEditTableViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(EditCourseViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(HomeViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(LoginViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(MainActivityViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(OrdinaryScheduleDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(QuickAddScheduleViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(RegisterViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ScheduleViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SessionViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SyncViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskCalendarViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TodoViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(WebViewScreenViewModel_HiltModules_KeyModule_ProvideFactory.provide()).build();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    private com.example.todoschedule.ui.MainActivity injectMainActivity2(
        com.example.todoschedule.ui.MainActivity instance) {
      MainActivity_MembersInjector.injectSyncRepository(instance, singletonCImpl.provideSyncRepositoryProvider.get());
      return instance;
    }
  }

  private static final class ViewModelCImpl extends TodoScheduleApplication_HiltComponents.ViewModelC {
    private final SavedStateHandle savedStateHandle;

    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<AddCourseViewModel> addCourseViewModelProvider;

    private Provider<AddEditOrdinaryScheduleViewModel> addEditOrdinaryScheduleViewModelProvider;

    private Provider<CourseDetailViewModel> courseDetailViewModelProvider;

    private Provider<CreateEditTableViewModel> createEditTableViewModelProvider;

    private Provider<EditCourseViewModel> editCourseViewModelProvider;

    private Provider<HomeViewModel> homeViewModelProvider;

    private Provider<LoginViewModel> loginViewModelProvider;

    private Provider<MainActivityViewModel> mainActivityViewModelProvider;

    private Provider<OrdinaryScheduleDetailViewModel> ordinaryScheduleDetailViewModelProvider;

    private Provider<QuickAddScheduleViewModel> quickAddScheduleViewModelProvider;

    private Provider<RegisterViewModel> registerViewModelProvider;

    private Provider<ScheduleViewModel> scheduleViewModelProvider;

    private Provider<SessionViewModel> sessionViewModelProvider;

    private Provider<SettingsViewModel> settingsViewModelProvider;

    private Provider<SyncViewModel> syncViewModelProvider;

    private Provider<TaskCalendarViewModel> taskCalendarViewModelProvider;

    private Provider<TaskViewModel> taskViewModelProvider;

    private Provider<TodoViewModel> todoViewModelProvider;

    private Provider<WebViewScreenViewModel> webViewScreenViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.savedStateHandle = savedStateHandleParam;
      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    private GetLoginUserIdFlowUseCase getLoginUserIdFlowUseCase() {
      return new GetLoginUserIdFlowUseCase(singletonCImpl.sessionRepositoryImplProvider.get());
    }

    private AddOrdinaryScheduleUseCase addOrdinaryScheduleUseCase() {
      return new AddOrdinaryScheduleUseCase(singletonCImpl.bindOrdinaryScheduleRepositoryProvider.get());
    }

    private GetOrdinaryScheduleByIdUseCase getOrdinaryScheduleByIdUseCase() {
      return new GetOrdinaryScheduleByIdUseCase(singletonCImpl.bindOrdinaryScheduleRepositoryProvider.get());
    }

    private UpdateOrdinaryScheduleUseCase updateOrdinaryScheduleUseCase() {
      return new UpdateOrdinaryScheduleUseCase(singletonCImpl.bindOrdinaryScheduleRepositoryProvider.get());
    }

    private LoginUserUseCase loginUserUseCase() {
      return new LoginUserUseCase(singletonCImpl.userRepositoryImplProvider.get(), singletonCImpl.remoteUserRepositoryImplProvider.get(), new VerifyPasswordUseCase());
    }

    private SaveLoginSessionUseCase saveLoginSessionUseCase() {
      return new SaveLoginSessionUseCase(singletonCImpl.sessionRepositoryImplProvider.get(), singletonCImpl.userRepositoryImplProvider.get());
    }

    private DeleteOrdinaryScheduleUseCase deleteOrdinaryScheduleUseCase() {
      return new DeleteOrdinaryScheduleUseCase(singletonCImpl.bindOrdinaryScheduleRepositoryProvider.get());
    }

    private RegisterUserUseCase registerUserUseCase() {
      return new RegisterUserUseCase(singletonCImpl.userRepositoryImplProvider.get(), singletonCImpl.remoteUserRepositoryImplProvider.get(), new HashPasswordUseCase());
    }

    private GetOrdinarySchedulesUseCase getOrdinarySchedulesUseCase() {
      return new GetOrdinarySchedulesUseCase(singletonCImpl.bindOrdinaryScheduleRepositoryProvider.get());
    }

    private GetDefaultTableTimeConfigUseCase getDefaultTableTimeConfigUseCase() {
      return new GetDefaultTableTimeConfigUseCase(singletonCImpl.bindTimeConfigRepositoryProvider.get());
    }

    private ClearLoginSessionUseCase clearLoginSessionUseCase() {
      return new ClearLoginSessionUseCase(singletonCImpl.sessionRepositoryImplProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.addCourseViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.addEditOrdinaryScheduleViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.courseDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.createEditTableViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.editCourseViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.homeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.loginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.mainActivityViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.ordinaryScheduleDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.quickAddScheduleViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
      this.registerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 10);
      this.scheduleViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 11);
      this.sessionViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 12);
      this.settingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 13);
      this.syncViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 14);
      this.taskCalendarViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 15);
      this.taskViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 16);
      this.todoViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 17);
      this.webViewScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 18);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return MapBuilder.<String, Provider<ViewModel>>newMapBuilder(19).put("com.example.todoschedule.ui.course.add.AddCourseViewModel", ((Provider) addCourseViewModelProvider)).put("com.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel", ((Provider) addEditOrdinaryScheduleViewModelProvider)).put("com.example.todoschedule.ui.course.detail.CourseDetailViewModel", ((Provider) courseDetailViewModelProvider)).put("com.example.todoschedule.ui.table.CreateEditTableViewModel", ((Provider) createEditTableViewModelProvider)).put("com.example.todoschedule.ui.course.edit.EditCourseViewModel", ((Provider) editCourseViewModelProvider)).put("com.example.todoschedule.ui.home.HomeViewModel", ((Provider) homeViewModelProvider)).put("com.example.todoschedule.ui.auth.LoginViewModel", ((Provider) loginViewModelProvider)).put("com.example.todoschedule.MainActivityViewModel", ((Provider) mainActivityViewModelProvider)).put("com.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel", ((Provider) ordinaryScheduleDetailViewModelProvider)).put("com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel", ((Provider) quickAddScheduleViewModelProvider)).put("com.example.todoschedule.ui.auth.RegisterViewModel", ((Provider) registerViewModelProvider)).put("com.example.todoschedule.ui.schedule.ScheduleViewModel", ((Provider) scheduleViewModelProvider)).put("com.example.todoschedule.ui.navigation.SessionViewModel", ((Provider) sessionViewModelProvider)).put("com.example.todoschedule.ui.settings.SettingsViewModel", ((Provider) settingsViewModelProvider)).put("com.example.todoschedule.ui.sync.SyncViewModel", ((Provider) syncViewModelProvider)).put("com.example.todoschedule.ui.task.TaskCalendarViewModel", ((Provider) taskCalendarViewModelProvider)).put("com.example.todoschedule.ui.task.TaskViewModel", ((Provider) taskViewModelProvider)).put("com.example.todoschedule.ui.todo.TodoViewModel", ((Provider) todoViewModelProvider)).put("com.example.todoschedule.ui.course.load.WebViewScreenViewModel", ((Provider) webViewScreenViewModelProvider)).build();
    }

    @Override
    public Map<String, Object> getHiltViewModelAssistedMap() {
      return Collections.<String, Object>emptyMap();
    }

    private SettingsViewModel injectSettingsViewModel(SettingsViewModel instance) {
      SettingsViewModel_MembersInjector.injectDevUtils(instance, singletonCImpl.devUtilsProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.todoschedule.ui.course.add.AddCourseViewModel 
          return (T) new AddCourseViewModel(singletonCImpl.bindCourseRepositoryProvider.get(), singletonCImpl.tableRepositoryImplProvider.get(), viewModelCImpl.getLoginUserIdFlowUseCase());

          case 1: // com.example.todoschedule.ui.ordinaryschedule.AddEditOrdinaryScheduleViewModel 
          return (T) new AddEditOrdinaryScheduleViewModel(viewModelCImpl.addOrdinaryScheduleUseCase(), viewModelCImpl.getOrdinaryScheduleByIdUseCase(), viewModelCImpl.updateOrdinaryScheduleUseCase(), singletonCImpl.sessionRepositoryImplProvider.get(), viewModelCImpl.savedStateHandle);

          case 2: // com.example.todoschedule.ui.course.detail.CourseDetailViewModel 
          return (T) new CourseDetailViewModel(singletonCImpl.bindCourseRepositoryProvider.get());

          case 3: // com.example.todoschedule.ui.table.CreateEditTableViewModel 
          return (T) new CreateEditTableViewModel(singletonCImpl.tableRepositoryImplProvider.get(), singletonCImpl.sessionRepositoryImplProvider.get(), singletonCImpl.bindGlobalSettingRepositoryProvider.get(), viewModelCImpl.savedStateHandle);

          case 4: // com.example.todoschedule.ui.course.edit.EditCourseViewModel 
          return (T) new EditCourseViewModel(singletonCImpl.bindCourseRepositoryProvider.get());

          case 5: // com.example.todoschedule.ui.home.HomeViewModel 
          return (T) new HomeViewModel();

          case 6: // com.example.todoschedule.ui.auth.LoginViewModel 
          return (T) new LoginViewModel(viewModelCImpl.loginUserUseCase(), viewModelCImpl.saveLoginSessionUseCase(), singletonCImpl.provideSyncManagerProvider.get());

          case 7: // com.example.todoschedule.MainActivityViewModel 
          return (T) new MainActivityViewModel(singletonCImpl.sessionRepositoryImplProvider.get());

          case 8: // com.example.todoschedule.ui.ordinaryschedule.OrdinaryScheduleDetailViewModel 
          return (T) new OrdinaryScheduleDetailViewModel(viewModelCImpl.getOrdinaryScheduleByIdUseCase(), viewModelCImpl.deleteOrdinaryScheduleUseCase(), viewModelCImpl.savedStateHandle);

          case 9: // com.example.todoschedule.ui.schedule.QuickAddScheduleViewModel 
          return (T) new QuickAddScheduleViewModel(viewModelCImpl.addOrdinaryScheduleUseCase(), singletonCImpl.sessionRepositoryImplProvider.get());

          case 10: // com.example.todoschedule.ui.auth.RegisterViewModel 
          return (T) new RegisterViewModel(viewModelCImpl.registerUserUseCase());

          case 11: // com.example.todoschedule.ui.schedule.ScheduleViewModel 
          return (T) new ScheduleViewModel(singletonCImpl.bindCourseRepositoryProvider.get(), singletonCImpl.sessionRepositoryImplProvider.get(), singletonCImpl.tableRepositoryImplProvider.get(), singletonCImpl.bindGlobalSettingRepositoryProvider.get(), viewModelCImpl.getOrdinarySchedulesUseCase(), viewModelCImpl.addOrdinaryScheduleUseCase(), viewModelCImpl.updateOrdinaryScheduleUseCase(), viewModelCImpl.deleteOrdinaryScheduleUseCase(), viewModelCImpl.getDefaultTableTimeConfigUseCase(), singletonCImpl.bindTimeConfigRepositoryProvider.get());

          case 12: // com.example.todoschedule.ui.navigation.SessionViewModel 
          return (T) new SessionViewModel(viewModelCImpl.getLoginUserIdFlowUseCase(), singletonCImpl.sessionRepositoryImplProvider.get());

          case 13: // com.example.todoschedule.ui.settings.SettingsViewModel 
          return (T) viewModelCImpl.injectSettingsViewModel(SettingsViewModel_Factory.newInstance(ApplicationContextModule_ProvideApplicationFactory.provideApplication(singletonCImpl.applicationContextModule), viewModelCImpl.clearLoginSessionUseCase(), singletonCImpl.sessionRepositoryImplProvider.get()));

          case 14: // com.example.todoschedule.ui.sync.SyncViewModel 
          return (T) new SyncViewModel(singletonCImpl.provideSyncRepositoryProvider.get(), singletonCImpl.provideSyncManagerProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 15: // com.example.todoschedule.ui.task.TaskCalendarViewModel 
          return (T) new TaskCalendarViewModel(singletonCImpl.calendarSyncManagerProvider.get(), singletonCImpl.sessionRepositoryImplProvider.get(), viewModelCImpl.getOrdinarySchedulesUseCase(), singletonCImpl.bindCourseRepositoryProvider.get(), singletonCImpl.bindGlobalSettingRepositoryProvider.get(), singletonCImpl.tableRepositoryImplProvider.get(), singletonCImpl.permissionManagerProvider.get(), viewModelCImpl.getDefaultTableTimeConfigUseCase());

          case 16: // com.example.todoschedule.ui.task.TaskViewModel 
          return (T) new TaskViewModel(singletonCImpl.sessionRepositoryImplProvider.get(), viewModelCImpl.getOrdinarySchedulesUseCase(), viewModelCImpl.updateOrdinaryScheduleUseCase(), viewModelCImpl.getDefaultTableTimeConfigUseCase(), singletonCImpl.bindCourseRepositoryProvider.get(), singletonCImpl.bindGlobalSettingRepositoryProvider.get(), singletonCImpl.tableRepositoryImplProvider.get());

          case 17: // com.example.todoschedule.ui.todo.TodoViewModel 
          return (T) new TodoViewModel();

          case 18: // com.example.todoschedule.ui.course.load.WebViewScreenViewModel 
          return (T) new WebViewScreenViewModel(singletonCImpl.bindCourseRepositoryProvider.get(), singletonCImpl.tableRepositoryImplProvider.get(), singletonCImpl.bindGlobalSettingRepositoryProvider.get(), viewModelCImpl.getLoginUserIdFlowUseCase());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends TodoScheduleApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends TodoScheduleApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectSyncService(SyncService syncService) {
      injectSyncService2(syncService);
    }

    private SyncService injectSyncService2(SyncService instance) {
      SyncService_MembersInjector.injectSyncRepository(instance, singletonCImpl.provideSyncRepositoryProvider.get());
      SyncService_MembersInjector.injectSyncManager(instance, singletonCImpl.provideSyncManagerProvider.get());
      SyncService_MembersInjector.injectSessionRepository(instance, singletonCImpl.sessionRepositoryImplProvider.get());
      return instance;
    }
  }

  private static final class SingletonCImpl extends TodoScheduleApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<DevUtils> devUtilsProvider;

    private Provider<HttpLoggingInterceptor> provideHttpLoggingInterceptorProvider;

    private Provider<DataStore<Preferences>> providePreferencesDataStoreProvider;

    private Provider<SessionRepositoryImpl> sessionRepositoryImplProvider;

    private Provider<Interceptor> provideAuthInterceptorProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<Moshi> provideMoshiProvider;

    private Provider<Retrofit> provideRetrofitProvider;

    private Provider<SyncApi> provideSyncApiServiceProvider;

    private Provider<DeviceIdManager> provideDeviceIdManagerProvider;

    private Provider<SyncManager> provideSyncManagerProvider;

    private Provider<SyncRepository> provideSyncRepositoryProvider;

    private Provider<CourseAdapter> provideCourseAdapterProvider;

    private Provider<TableAdapter> provideTableAdapterProvider;

    private Provider<CourseNodeAdapter> provideCourseNodeAdapterProvider;

    private Provider<OrdinaryScheduleAdapter> provideOrdinaryScheduleAdapterProvider;

    private Provider<SynkAdapterRegistry> provideSynkAdapterRegistryProvider;

    private Provider<CrdtKeyResolver> crdtKeyResolverProvider;

    private Provider<CourseDao> provideCourseDaoProvider;

    private Provider<GlobalSettingDao> provideGlobalSettingDaoProvider;

    private Provider<GlobalSettingRepositoryImpl> globalSettingRepositoryImplProvider;

    private Provider<GlobalSettingRepository> bindGlobalSettingRepositoryProvider;

    private Provider<CourseRepositoryImpl> courseRepositoryImplProvider;

    private Provider<CourseRepository> bindCourseRepositoryProvider;

    private Provider<TableDao> provideTableDaoProvider;

    private Provider<TableRepositoryImpl> tableRepositoryImplProvider;

    private Provider<OrdinaryScheduleDao> provideOrdinaryScheduleDaoProvider;

    private Provider<TimeSlotDao> provideTimeSlotDaoProvider;

    private Provider<OrdinaryScheduleRepositoryImpl> ordinaryScheduleRepositoryImplProvider;

    private Provider<OrdinaryScheduleRepository> bindOrdinaryScheduleRepositoryProvider;

    private Provider<UserDao> provideUserDaoProvider;

    private Provider<UserRepositoryImpl> userRepositoryImplProvider;

    private Provider<UserApiService> provideUserApiServiceProvider;

    private Provider<RemoteUserRepositoryImpl> remoteUserRepositoryImplProvider;

    private Provider<TableTimeConfigDao> provideTimeConfigDaoProvider;

    private Provider<TableTimeConfigRepositoryImpl> tableTimeConfigRepositoryImplProvider;

    private Provider<TableTimeConfigRepository> bindTimeConfigRepositoryProvider;

    private Provider<CalendarSyncManager> calendarSyncManagerProvider;

    private Provider<PermissionManager> permissionManagerProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 1));
      this.devUtilsProvider = DoubleCheck.provider(new SwitchingProvider<DevUtils>(singletonCImpl, 0));
      this.provideHttpLoggingInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<HttpLoggingInterceptor>(singletonCImpl, 7));
      this.providePreferencesDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<DataStore<Preferences>>(singletonCImpl, 10));
      this.sessionRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<SessionRepositoryImpl>(singletonCImpl, 9));
      this.provideAuthInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<Interceptor>(singletonCImpl, 8));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 6));
      this.provideMoshiProvider = DoubleCheck.provider(new SwitchingProvider<Moshi>(singletonCImpl, 11));
      this.provideRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 5));
      this.provideSyncApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<SyncApi>(singletonCImpl, 4));
      this.provideDeviceIdManagerProvider = DoubleCheck.provider(new SwitchingProvider<DeviceIdManager>(singletonCImpl, 12));
      this.provideSyncManagerProvider = new DelegateFactory<>();
      this.provideSyncRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<SyncRepository>(singletonCImpl, 3));
      this.provideCourseAdapterProvider = DoubleCheck.provider(new SwitchingProvider<CourseAdapter>(singletonCImpl, 14));
      this.provideTableAdapterProvider = DoubleCheck.provider(new SwitchingProvider<TableAdapter>(singletonCImpl, 15));
      this.provideCourseNodeAdapterProvider = DoubleCheck.provider(new SwitchingProvider<CourseNodeAdapter>(singletonCImpl, 16));
      this.provideOrdinaryScheduleAdapterProvider = DoubleCheck.provider(new SwitchingProvider<OrdinaryScheduleAdapter>(singletonCImpl, 17));
      this.provideSynkAdapterRegistryProvider = DoubleCheck.provider(new SwitchingProvider<SynkAdapterRegistry>(singletonCImpl, 13));
      this.crdtKeyResolverProvider = DoubleCheck.provider(new SwitchingProvider<CrdtKeyResolver>(singletonCImpl, 18));
      DelegateFactory.setDelegate(provideSyncManagerProvider, DoubleCheck.provider(new SwitchingProvider<SyncManager>(singletonCImpl, 2)));
      this.provideCourseDaoProvider = DoubleCheck.provider(new SwitchingProvider<CourseDao>(singletonCImpl, 20));
      this.provideGlobalSettingDaoProvider = DoubleCheck.provider(new SwitchingProvider<GlobalSettingDao>(singletonCImpl, 22));
      this.globalSettingRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 21);
      this.bindGlobalSettingRepositoryProvider = DoubleCheck.provider((Provider) globalSettingRepositoryImplProvider);
      this.courseRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 19);
      this.bindCourseRepositoryProvider = DoubleCheck.provider((Provider) courseRepositoryImplProvider);
      this.provideTableDaoProvider = DoubleCheck.provider(new SwitchingProvider<TableDao>(singletonCImpl, 24));
      this.tableRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<TableRepositoryImpl>(singletonCImpl, 23));
      this.provideOrdinaryScheduleDaoProvider = DoubleCheck.provider(new SwitchingProvider<OrdinaryScheduleDao>(singletonCImpl, 26));
      this.provideTimeSlotDaoProvider = DoubleCheck.provider(new SwitchingProvider<TimeSlotDao>(singletonCImpl, 27));
      this.ordinaryScheduleRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 25);
      this.bindOrdinaryScheduleRepositoryProvider = DoubleCheck.provider((Provider) ordinaryScheduleRepositoryImplProvider);
      this.provideUserDaoProvider = DoubleCheck.provider(new SwitchingProvider<UserDao>(singletonCImpl, 29));
      this.userRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserRepositoryImpl>(singletonCImpl, 28));
      this.provideUserApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<UserApiService>(singletonCImpl, 31));
      this.remoteUserRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<RemoteUserRepositoryImpl>(singletonCImpl, 30));
      this.provideTimeConfigDaoProvider = DoubleCheck.provider(new SwitchingProvider<TableTimeConfigDao>(singletonCImpl, 33));
      this.tableTimeConfigRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 32);
      this.bindTimeConfigRepositoryProvider = DoubleCheck.provider((Provider) tableTimeConfigRepositoryImplProvider);
      this.calendarSyncManagerProvider = DoubleCheck.provider(new SwitchingProvider<CalendarSyncManager>(singletonCImpl, 34));
      this.permissionManagerProvider = DoubleCheck.provider(new SwitchingProvider<PermissionManager>(singletonCImpl, 35));
    }

    @Override
    public void injectTodoScheduleApplication(TodoScheduleApplication todoScheduleApplication) {
      injectTodoScheduleApplication2(todoScheduleApplication);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private TodoScheduleApplication injectTodoScheduleApplication2(
        TodoScheduleApplication instance) {
      TodoScheduleApplication_MembersInjector.injectDevUtils(instance, devUtilsProvider.get());
      TodoScheduleApplication_MembersInjector.injectSyncManager(instance, provideSyncManagerProvider.get());
      TodoScheduleApplication_MembersInjector.injectSessionRepository(instance, sessionRepositoryImplProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.todoschedule.core.utils.DevUtils 
          return (T) new DevUtils(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideAppDatabaseProvider.get());

          case 1: // com.example.todoschedule.data.database.AppDatabase 
          return (T) DatabaseModule_ProvideAppDatabaseFactory.provideAppDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 2: // com.example.todoschedule.data.sync.SyncManager 
          return (T) SyncModule_ProvideSyncManagerFactory.provideSyncManager(singletonCImpl.provideSyncRepositoryProvider.get(), singletonCImpl.provideDeviceIdManagerProvider.get(), singletonCImpl.provideSynkAdapterRegistryProvider.get(), singletonCImpl.crdtKeyResolverProvider.get());

          case 3: // com.example.todoschedule.data.repository.SyncRepository 
          return (T) SyncModule_ProvideSyncRepositoryFactory.provideSyncRepository(singletonCImpl.provideAppDatabaseProvider.get(), singletonCImpl.provideSyncApiServiceProvider.get(), singletonCImpl.provideDeviceIdManagerProvider.get(), singletonCImpl.sessionRepositoryImplProvider.get(), singletonCImpl.provideSyncManagerProvider);

          case 4: // com.example.todoschedule.data.remote.api.SyncApi 
          return (T) NetworkModule_ProvideSyncApiServiceFactory.provideSyncApiService(singletonCImpl.provideRetrofitProvider.get());

          case 5: // retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRetrofitFactory.provideRetrofit(singletonCImpl.provideOkHttpClientProvider.get(), singletonCImpl.provideMoshiProvider.get());

          case 6: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient(singletonCImpl.provideHttpLoggingInterceptorProvider.get(), singletonCImpl.provideAuthInterceptorProvider.get());

          case 7: // okhttp3.logging.HttpLoggingInterceptor 
          return (T) NetworkModule_ProvideHttpLoggingInterceptorFactory.provideHttpLoggingInterceptor();

          case 8: // okhttp3.Interceptor 
          return (T) NetworkModule_ProvideAuthInterceptorFactory.provideAuthInterceptor(singletonCImpl.sessionRepositoryImplProvider.get());

          case 9: // com.example.todoschedule.data.repository.SessionRepositoryImpl 
          return (T) new SessionRepositoryImpl(singletonCImpl.providePreferencesDataStoreProvider.get());

          case 10: // androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> 
          return (T) DataStoreModule_ProvidePreferencesDataStoreFactory.providePreferencesDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 11: // com.squareup.moshi.Moshi 
          return (T) NetworkModule_ProvideMoshiFactory.provideMoshi();

          case 12: // com.example.todoschedule.data.sync.DeviceIdManager 
          return (T) SyncModule_ProvideDeviceIdManagerFactory.provideDeviceIdManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 13: // com.example.todoschedule.data.sync.adapter.SynkAdapterRegistry 
          return (T) SyncModule_ProvideSynkAdapterRegistryFactory.provideSynkAdapterRegistry(singletonCImpl.provideCourseAdapterProvider.get(), singletonCImpl.provideTableAdapterProvider.get(), singletonCImpl.provideCourseNodeAdapterProvider.get(), singletonCImpl.provideOrdinaryScheduleAdapterProvider.get());

          case 14: // com.example.todoschedule.data.sync.adapter.CourseAdapter 
          return (T) SyncModule_ProvideCourseAdapterFactory.provideCourseAdapter();

          case 15: // com.example.todoschedule.data.sync.adapter.TableAdapter 
          return (T) SyncModule_ProvideTableAdapterFactory.provideTableAdapter();

          case 16: // com.example.todoschedule.data.sync.adapter.CourseNodeAdapter 
          return (T) SyncModule_ProvideCourseNodeAdapterFactory.provideCourseNodeAdapter();

          case 17: // com.example.todoschedule.data.sync.adapter.OrdinaryScheduleAdapter 
          return (T) SyncModule_ProvideOrdinaryScheduleAdapterFactory.provideOrdinaryScheduleAdapter();

          case 18: // com.example.todoschedule.data.sync.CrdtKeyResolver 
          return (T) new CrdtKeyResolver(singletonCImpl.provideAppDatabaseProvider.get(), singletonCImpl.provideSyncRepositoryProvider);

          case 19: // com.example.todoschedule.data.repository.CourseRepositoryImpl 
          return (T) new CourseRepositoryImpl(singletonCImpl.provideCourseDaoProvider.get(), singletonCImpl.sessionRepositoryImplProvider.get(), singletonCImpl.bindGlobalSettingRepositoryProvider.get(), singletonCImpl.provideSyncManagerProvider.get());

          case 20: // com.example.todoschedule.data.database.dao.CourseDao 
          return (T) DatabaseModule_ProvideCourseDaoFactory.provideCourseDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 21: // com.example.todoschedule.data.repository.GlobalSettingRepositoryImpl 
          return (T) new GlobalSettingRepositoryImpl(singletonCImpl.provideGlobalSettingDaoProvider.get());

          case 22: // com.example.todoschedule.data.database.dao.GlobalSettingDao 
          return (T) DatabaseModule_ProvideGlobalSettingDaoFactory.provideGlobalSettingDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 23: // com.example.todoschedule.data.repository.TableRepositoryImpl 
          return (T) new TableRepositoryImpl(singletonCImpl.provideTableDaoProvider.get());

          case 24: // com.example.todoschedule.data.database.dao.TableDao 
          return (T) DatabaseModule_ProvideTableDaoFactory.provideTableDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 25: // com.example.todoschedule.data.repository.OrdinaryScheduleRepositoryImpl 
          return (T) new OrdinaryScheduleRepositoryImpl(singletonCImpl.provideOrdinaryScheduleDaoProvider.get(), singletonCImpl.provideTimeSlotDaoProvider.get());

          case 26: // com.example.todoschedule.data.database.dao.OrdinaryScheduleDao 
          return (T) DatabaseModule_ProvideOrdinaryScheduleDaoFactory.provideOrdinaryScheduleDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 27: // com.example.todoschedule.data.database.dao.TimeSlotDao 
          return (T) DatabaseModule_ProvideTimeSlotDaoFactory.provideTimeSlotDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 28: // com.example.todoschedule.data.repository.UserRepositoryImpl 
          return (T) new UserRepositoryImpl(singletonCImpl.provideUserDaoProvider.get(), singletonCImpl.sessionRepositoryImplProvider.get());

          case 29: // com.example.todoschedule.data.database.dao.UserDao 
          return (T) DatabaseModule_ProvideUserDaoFactory.provideUserDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 30: // com.example.todoschedule.data.repository.RemoteUserRepositoryImpl 
          return (T) new RemoteUserRepositoryImpl(singletonCImpl.provideUserApiServiceProvider.get(), singletonCImpl.sessionRepositoryImplProvider.get());

          case 31: // com.example.todoschedule.data.remote.api.UserApiService 
          return (T) NetworkModule_ProvideUserApiServiceFactory.provideUserApiService(singletonCImpl.provideRetrofitProvider.get());

          case 32: // com.example.todoschedule.data.repository.TableTimeConfigRepositoryImpl 
          return (T) new TableTimeConfigRepositoryImpl(singletonCImpl.provideTimeConfigDaoProvider.get());

          case 33: // com.example.todoschedule.data.database.dao.TableTimeConfigDao 
          return (T) DatabaseModule_ProvideTimeConfigDaoFactory.provideTimeConfigDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 34: // com.example.todoschedule.domain.utils.CalendarSyncManager 
          return (T) new CalendarSyncManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 35: // com.example.todoschedule.core.utils.PermissionManager 
          return (T) new PermissionManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
