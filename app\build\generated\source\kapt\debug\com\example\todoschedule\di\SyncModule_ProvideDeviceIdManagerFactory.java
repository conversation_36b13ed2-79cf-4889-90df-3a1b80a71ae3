// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.di;

import android.content.Context;
import com.example.todoschedule.data.sync.DeviceIdManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncModule_ProvideDeviceIdManagerFactory implements Factory<DeviceIdManager> {
  private final Provider<Context> contextProvider;

  public SyncModule_ProvideDeviceIdManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DeviceIdManager get() {
    return provideDeviceIdManager(contextProvider.get());
  }

  public static SyncModule_ProvideDeviceIdManagerFactory create(Provider<Context> contextProvider) {
    return new SyncModule_ProvideDeviceIdManagerFactory(contextProvider);
  }

  public static DeviceIdManager provideDeviceIdManager(Context context) {
    return Preconditions.checkNotNullFromProvides(SyncModule.INSTANCE.provideDeviceIdManager(context));
  }
}
