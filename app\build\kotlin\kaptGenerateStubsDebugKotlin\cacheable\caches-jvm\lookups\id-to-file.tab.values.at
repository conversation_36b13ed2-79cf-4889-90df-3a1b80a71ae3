/ Header Record For PersistentHashMapValueStorage; :app/src/main/java/com/example/todoschedule/MainActivity.ktD Capp/src/main/java/com/example/todoschedule/MainActivityViewModel.ktF Eapp/src/main/java/com/example/todoschedule/TodoScheduleApplication.ktJ Iapp/src/main/java/com/example/todoschedule/core/constants/AppConstants.ktQ Papp/src/main/java/com/example/todoschedule/core/extensions/DateTimeExtensions.ktB Aapp/src/main/java/com/example/todoschedule/core/utils/DevUtils.ktK Japp/src/main/java/com/example/todoschedule/core/utils/PermissionManager.ktH Gapp/src/main/java/com/example/todoschedule/data/database/AppDatabase.ktQ Papp/src/main/java/com/example/todoschedule/data/database/converter/Converters.ktL Kapp/src/main/java/com/example/todoschedule/data/database/converter/Enums.ktJ Iapp/src/main/java/com/example/todoschedule/data/database/dao/CourseDao.ktN Mapp/src/main/java/com/example/todoschedule/data/database/dao/CourseNodeDao.ktQ Papp/src/main/java/com/example/todoschedule/data/database/dao/GlobalSettingDao.ktT Sapp/src/main/java/com/example/todoschedule/data/database/dao/OrdinaryScheduleDao.ktO Napp/src/main/java/com/example/todoschedule/data/database/dao/SyncMessageDao.ktI Happ/src/main/java/com/example/todoschedule/data/database/dao/TableDao.ktS Rapp/src/main/java/com/example/todoschedule/data/database/dao/TableTimeConfigDao.ktL Kapp/src/main/java/com/example/todoschedule/data/database/dao/TimeSlotDao.ktH Gapp/src/main/java/com/example/todoschedule/data/database/dao/UserDao.ktP Oapp/src/main/java/com/example/todoschedule/data/database/entity/CourseEntity.ktT Sapp/src/main/java/com/example/todoschedule/data/database/entity/CourseNodeEntity.kt\ [app/src/main/java/com/example/todoschedule/data/database/entity/GlobalTableSettingEntity.ktZ Yapp/src/main/java/com/example/todoschedule/data/database/entity/OrdinaryScheduleEntity.ktU Tapp/src/main/java/com/example/todoschedule/data/database/entity/SyncMessageEntity.ktL Kapp/src/main/java/com/example/todoschedule/data/database/entity/Syncable.ktO Napp/src/main/java/com/example/todoschedule/data/database/entity/TableEntity.ktY Xapp/src/main/java/com/example/todoschedule/data/database/entity/TableTimeConfigEntity.ktd capp/src/main/java/com/example/todoschedule/data/database/entity/TableTimeConfigNodeDetaileEntity.ktR Qapp/src/main/java/com/example/todoschedule/data/database/entity/TimeSlotEntity.ktN Mapp/src/main/java/com/example/todoschedule/data/database/entity/UserEntity.ktG Fapp/src/main/java/com/example/todoschedule/data/mapper/CourseMapper.ktN Mapp/src/main/java/com/example/todoschedule/data/mapper/GlobalSettingMapper.ktR Qapp/src/main/java/com/example/todoschedule/data/mapper/OrdinaryScheduleMappper.ktP Oapp/src/main/java/com/example/todoschedule/data/mapper/TableTimeConfigMapper.ktI Happ/src/main/java/com/example/todoschedule/data/mapper/TimeSlotMapper.ktE Dapp/src/main/java/com/example/todoschedule/data/mapper/UserMapper.ktI Happ/src/main/java/com/example/todoschedule/data/model/CourseWithNodes.ktW Vapp/src/main/java/com/example/todoschedule/data/model/OrdinaryScheduleWithTimeSlots.ktR Qapp/src/main/java/com/example/todoschedule/data/model/TableTimeConfigWithNodes.ktI Happ/src/main/java/com/example/todoschedule/data/remote/TodoApiService.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/api/SyncApi.ktM Lapp/src/main/java/com/example/todoschedule/data/remote/api/UserApiService.ktJ Iapp/src/main/java/com/example/todoschedule/data/remote/dto/ApiResponse.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/dto/UserDto.ktL Kapp/src/main/java/com/example/todoschedule/data/remote/model/ApiResponse.ktJ Iapp/src/main/java/com/example/todoschedule/data/remote/model/CourseDto.ktN Mapp/src/main/java/com/example/todoschedule/data/remote/model/CourseNodeDto.ktI Happ/src/main/java/com/example/todoschedule/data/remote/model/TableDto.ktS Rapp/src/main/java/com/example/todoschedule/data/repository/CourseRepositoryImpl.ktZ Yapp/src/main/java/com/example/todoschedule/data/repository/GlobalSettingRepositoryImpl.kt] \app/src/main/java/com/example/todoschedule/data/repository/OrdinaryScheduleRepositoryImpl.ktW Vapp/src/main/java/com/example/todoschedule/data/repository/RemoteUserRepositoryImpl.ktT Sapp/src/main/java/com/example/todoschedule/data/repository/SessionRepositoryImpl.ktM Lapp/src/main/java/com/example/todoschedule/data/repository/SyncRepository.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktR Qapp/src/main/java/com/example/todoschedule/data/repository/TableRepositoryImpl.kt\ [app/src/main/java/com/example/todoschedule/data/repository/TableTimeConfigRepositoryImpl.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/UserRepositoryImpl.ktH Gapp/src/main/java/com/example/todoschedule/data/sync/CrdtKeyResolver.ktH Gapp/src/main/java/com/example/todoschedule/data/sync/DeviceIdManager.kt@ ?app/src/main/java/com/example/todoschedule/data/sync/SyncApi.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncApiImpl.ktF Eapp/src/main/java/com/example/todoschedule/data/sync/SyncConstants.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncService.ktC Bapp/src/main/java/com/example/todoschedule/data/sync/SyncWorker.ktN Mapp/src/main/java/com/example/todoschedule/data/sync/adapter/CourseAdapter.ktR Qapp/src/main/java/com/example/todoschedule/data/sync/adapter/CourseNodeAdapter.ktX Wapp/src/main/java/com/example/todoschedule/data/sync/adapter/OrdinaryScheduleAdapter.ktL Kapp/src/main/java/com/example/todoschedule/data/sync/adapter/SynkAdapter.ktT Sapp/src/main/java/com/example/todoschedule/data/sync/adapter/SynkAdapterRegistry.ktM Lapp/src/main/java/com/example/todoschedule/data/sync/adapter/TableAdapter.ktK Japp/src/main/java/com/example/todoschedule/data/sync/dto/SyncMessageDto.ktJ Iapp/src/main/java/com/example/todoschedule/data/sync/entity/SyncEntity.kt; :app/src/main/java/com/example/todoschedule/di/AppModule.ktA @app/src/main/java/com/example/todoschedule/di/DataStoreModule.kt@ ?app/src/main/java/com/example/todoschedule/di/DatabaseModule.kt? >app/src/main/java/com/example/todoschedule/di/NetworkModule.ktB Aapp/src/main/java/com/example/todoschedule/di/RepositoryModule.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktB Aapp/src/main/java/com/example/todoschedule/domain/model/Course.ktF Eapp/src/main/java/com/example/todoschedule/domain/model/CourseNode.ktN Mapp/src/main/java/com/example/todoschedule/domain/model/GlobalTableSetting.ktL Kapp/src/main/java/com/example/todoschedule/domain/model/OrdinarySchedule.ktA @app/src/main/java/com/example/todoschedule/domain/model/Table.ktK Japp/src/main/java/com/example/todoschedule/domain/model/TableTimeConfig.ktO Napp/src/main/java/com/example/todoschedule/domain/model/TableTimeConfigNode.ktI Happ/src/main/java/com/example/todoschedule/domain/model/ThemeSettings.ktD Capp/src/main/java/com/example/todoschedule/domain/model/TimeSlot.kt@ ?app/src/main/java/com/example/todoschedule/domain/model/User.ktQ Papp/src/main/java/com/example/todoschedule/domain/repository/CourseRepository.ktX Wapp/src/main/java/com/example/todoschedule/domain/repository/GlobalSettingRepository.kt[ Zapp/src/main/java/com/example/todoschedule/domain/repository/OrdinaryScheduleRepository.ktU Tapp/src/main/java/com/example/todoschedule/domain/repository/RemoteUserRepository.ktR Qapp/src/main/java/com/example/todoschedule/domain/repository/SessionRepository.ktP Oapp/src/main/java/com/example/todoschedule/domain/repository/TableRepository.ktZ Yapp/src/main/java/com/example/todoschedule/domain/repository/TableTimeConfigRepository.ktO Napp/src/main/java/com/example/todoschedule/domain/repository/UserRepository.kt\ [app/src/main/java/com/example/todoschedule/domain/use_case/auth/ClearLoginSessionUseCase.kt] \app/src/main/java/com/example/todoschedule/domain/use_case/auth/GetLoginUserIdFlowUseCase.ktW Vapp/src/main/java/com/example/todoschedule/domain/use_case/auth/HashPasswordUseCase.ktT Sapp/src/main/java/com/example/todoschedule/domain/use_case/auth/LoginUserUseCase.ktW Vapp/src/main/java/com/example/todoschedule/domain/use_case/auth/RegisterUserUseCase.kt[ Zapp/src/main/java/com/example/todoschedule/domain/use_case/auth/SaveLoginSessionUseCase.ktY Xapp/src/main/java/com/example/todoschedule/domain/use_case/auth/VerifyPasswordUseCase.ktk japp/src/main/java/com/example/todoschedule/domain/use_case/ordinary_schedule/AddOrdinaryScheduleUseCase.ktn mapp/src/main/java/com/example/todoschedule/domain/use_case/ordinary_schedule/DeleteOrdinaryScheduleUseCase.kto napp/src/main/java/com/example/todoschedule/domain/use_case/ordinary_schedule/GetOrdinaryScheduleByIdUseCase.ktl kapp/src/main/java/com/example/todoschedule/domain/use_case/ordinary_schedule/GetOrdinarySchedulesUseCase.ktn mapp/src/main/java/com/example/todoschedule/domain/use_case/ordinary_schedule/UpdateOrdinaryScheduleUseCase.ktq papp/src/main/java/com/example/todoschedule/domain/use_case/table_time_config/GetDefaultTableTimeConfigUseCase.ktO Napp/src/main/java/com/example/todoschedule/domain/utils/CalendarSyncManager.ktI Happ/src/main/java/com/example/todoschedule/domain/utils/CalendarUtils.ktB Aapp/src/main/java/com/example/todoschedule/navigation/NavGraph.kt> =app/src/main/java/com/example/todoschedule/ui/MainActivity.ktB Aapp/src/main/java/com/example/todoschedule/ui/auth/LoginScreen.ktC Bapp/src/main/java/com/example/todoschedule/ui/auth/LoginUiState.ktE Dapp/src/main/java/com/example/todoschedule/ui/auth/LoginViewModel.ktE Dapp/src/main/java/com/example/todoschedule/ui/auth/RegisterScreen.ktF Eapp/src/main/java/com/example/todoschedule/ui/auth/RegisterUiState.ktH Gapp/src/main/java/com/example/todoschedule/ui/auth/RegisterViewModel.ktM Lapp/src/main/java/com/example/todoschedule/ui/components/PermissionDialog.ktL Kapp/src/main/java/com/example/todoschedule/ui/course/add/AddCourseScreen.ktO Napp/src/main/java/com/example/todoschedule/ui/course/add/AddCourseViewModel.ktN Mapp/src/main/java/com/example/todoschedule/ui/course/add/CourseNodeUiState.ktR Qapp/src/main/java/com/example/todoschedule/ui/course/detail/CourseDetailModels.ktR Qapp/src/main/java/com/example/todoschedule/ui/course/detail/CourseDetailScreen.ktU Tapp/src/main/java/com/example/todoschedule/ui/course/detail/CourseDetailViewModel.ktN Mapp/src/main/java/com/example/todoschedule/ui/course/edit/EditCourseScreen.ktQ Papp/src/main/java/com/example/todoschedule/ui/course/edit/EditCourseViewModel.ktR Qapp/src/main/java/com/example/todoschedule/ui/course/load/SchoolSelectorScreen.ktM Lapp/src/main/java/com/example/todoschedule/ui/course/load/SchoolViewModel.ktK Japp/src/main/java/com/example/todoschedule/ui/course/load/WebViewScreen.ktT Sapp/src/main/java/com/example/todoschedule/ui/course/load/WebViewScreenViewModel.ktA @app/src/main/java/com/example/todoschedule/ui/home/<USER>/src/main/java/com/example/todoschedule/ui/home/<USER>/src/main/java/com/example/todoschedule/ui/navigation/AppBottomNavigation.ktJ Iapp/src/main/java/com/example/todoschedule/ui/navigation/AppNavigation.ktF Eapp/src/main/java/com/example/todoschedule/ui/navigation/AppRoutes.ktL Kapp/src/main/java/com/example/todoschedule/ui/navigation/NavigationState.kt` _app/src/main/java/com/example/todoschedule/ui/ordinaryschedule/AddEditOrdinaryScheduleScreen.ktc bapp/src/main/java/com/example/todoschedule/ui/ordinaryschedule/AddEditOrdinaryScheduleViewModel.kt_ ^app/src/main/java/com/example/todoschedule/ui/ordinaryschedule/OrdinaryScheduleDetailScreen.ktb aapp/src/main/java/com/example/todoschedule/ui/ordinaryschedule/OrdinaryScheduleDetailViewModel.ktG Fapp/src/main/java/com/example/todoschedule/ui/profile/ProfileScreen.ktW Vapp/src/main/java/com/example/todoschedule/ui/schedule/QuickAddScheduleSheetContent.ktT Sapp/src/main/java/com/example/todoschedule/ui/schedule/QuickAddScheduleViewModel.ktI Happ/src/main/java/com/example/todoschedule/ui/schedule/ScheduleScreen.ktL Kapp/src/main/java/com/example/todoschedule/ui/schedule/ScheduleViewModel.ktC Bapp/src/main/java/com/example/todoschedule/ui/schedule/TimeAxis.ktC Bapp/src/main/java/com/example/todoschedule/ui/schedule/WeekGrid.ktN Mapp/src/main/java/com/example/todoschedule/ui/schedule/model/CourseUiModel.ktP Oapp/src/main/java/com/example/todoschedule/ui/schedule/model/ScheduleUiState.ktR Qapp/src/main/java/com/example/todoschedule/ui/schedule/model/TimeDetailUiModel.ktO Napp/src/main/java/com/example/todoschedule/ui/screens/setting/SettingScreen.ktI Happ/src/main/java/com/example/todoschedule/ui/settings/SettingsScreen.ktL Kapp/src/main/java/com/example/todoschedule/ui/settings/SettingsViewModel.ktC Bapp/src/main/java/com/example/todoschedule/ui/study/StudyScreen.ktJ Iapp/src/main/java/com/example/todoschedule/ui/sync/SyncStatusIndicator.ktD Capp/src/main/java/com/example/todoschedule/ui/sync/SyncViewModel.ktM Lapp/src/main/java/com/example/todoschedule/ui/table/CreateEditTableScreen.ktN Mapp/src/main/java/com/example/todoschedule/ui/table/CreateEditTableUiState.ktP Oapp/src/main/java/com/example/todoschedule/ui/table/CreateEditTableViewModel.ktM Lapp/src/main/java/com/example/todoschedule/ui/task/TaskCalendarSyncScreen.ktL Kapp/src/main/java/com/example/todoschedule/ui/task/TaskCalendarViewModel.ktA @app/src/main/java/com/example/todoschedule/ui/task/TaskModels.ktI Happ/src/main/java/com/example/todoschedule/ui/task/TaskReminderScreen.ktA @app/src/main/java/com/example/todoschedule/ui/task/TaskScreen.ktD Capp/src/main/java/com/example/todoschedule/ui/task/TaskViewModel.kt= <app/src/main/java/com/example/todoschedule/ui/theme/Color.kt= <app/src/main/java/com/example/todoschedule/ui/theme/Theme.kt< ;app/src/main/java/com/example/todoschedule/ui/theme/Type.ktA @app/src/main/java/com/example/todoschedule/ui/todo/TodoModels.ktA @app/src/main/java/com/example/todoschedule/ui/todo/TodoScreen.ktD Capp/src/main/java/com/example/todoschedule/ui/todo/TodoViewModel.ktB Aapp/src/main/java/com/example/todoschedule/ui/utils/ColorUtils.kt< ;app/src/main/java/com/example/todoschedule/util/Resource.ktI Happ/src/main/java/com/example/todoschedule/utils/courseadapter/Common.ktL Kapp/src/main/java/com/example/todoschedule/utils/courseadapter/Generator.ktN Mapp/src/main/java/com/example/todoschedule/utils/courseadapter/bean/Course.ktV Uapp/src/main/java/com/example/todoschedule/utils/courseadapter/bean/CourseBaseBean.ktX Wapp/src/main/java/com/example/todoschedule/utils/courseadapter/bean/CourseDetailBean.ktT Sapp/src/main/java/com/example/todoschedule/utils/courseadapter/bean/ParserResult.ktR Qapp/src/main/java/com/example/todoschedule/utils/courseadapter/bean/TimeDetail.ktQ Papp/src/main/java/com/example/todoschedule/utils/courseadapter/bean/TimeTable.ktP Oapp/src/main/java/com/example/todoschedule/utils/courseadapter/bean/WeekBean.ktP Oapp/src/main/java/com/example/todoschedule/utils/courseadapter/parser/Parser.ktY Xapp/src/main/java/com/example/todoschedule/utils/courseadapter/parser/SupwisdomParser.ktS Rapp/src/main/java/com/example/todoschedule/utils/courseadapter/parser/ZZUParser.ktI Happ/src/main/java/com/example/todoschedule/utils/injectjs/ZZUInjectJs.ktO Napp/src/main/java/com/example/todoschedule/ui/screens/setting/SettingScreen.ktJ Iapp/src/main/java/com/example/todoschedule/ui/sync/SyncStatusIndicator.ktJ Iapp/src/main/java/com/example/todoschedule/core/constants/AppConstants.ktM Lapp/src/main/java/com/example/todoschedule/data/repository/SyncRepository.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncService.ktC Bapp/src/main/java/com/example/todoschedule/data/sync/SyncWorker.kt> =app/src/main/java/com/example/todoschedule/ui/MainActivity.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktM Lapp/src/main/java/com/example/todoschedule/data/repository/SyncRepository.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncService.ktC Bapp/src/main/java/com/example/todoschedule/data/sync/SyncWorker.kt> =app/src/main/java/com/example/todoschedule/ui/MainActivity.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktE Dapp/src/main/java/com/example/todoschedule/ui/auth/LoginViewModel.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/api/SyncApi.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktK Japp/src/main/java/com/example/todoschedule/data/sync/dto/SyncMessageDto.ktL Kapp/src/main/java/com/example/todoschedule/data/database/entity/Syncable.ktM Lapp/src/main/java/com/example/todoschedule/data/repository/SyncRepository.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktH Gapp/src/main/java/com/example/todoschedule/data/sync/DeviceIdManager.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktC Bapp/src/main/java/com/example/todoschedule/data/sync/SyncWorker.ktK Japp/src/main/java/com/example/todoschedule/data/sync/dto/SyncMessageDto.ktH Gapp/src/main/java/com/example/todoschedule/data/database/AppDatabase.ktJ Iapp/src/main/java/com/example/todoschedule/data/database/dao/CourseDao.ktN Mapp/src/main/java/com/example/todoschedule/data/database/dao/CourseNodeDao.ktT Sapp/src/main/java/com/example/todoschedule/data/database/dao/OrdinaryScheduleDao.ktI Happ/src/main/java/com/example/todoschedule/data/database/dao/TableDao.ktP Oapp/src/main/java/com/example/todoschedule/data/database/entity/CourseEntity.ktT Sapp/src/main/java/com/example/todoschedule/data/database/entity/CourseNodeEntity.ktZ Yapp/src/main/java/com/example/todoschedule/data/database/entity/OrdinaryScheduleEntity.ktO Napp/src/main/java/com/example/todoschedule/data/database/entity/TableEntity.ktY Xapp/src/main/java/com/example/todoschedule/data/database/entity/TableTimeConfigEntity.ktG Fapp/src/main/java/com/example/todoschedule/data/mapper/CourseMapper.ktR Qapp/src/main/java/com/example/todoschedule/data/mapper/OrdinaryScheduleMappper.ktI Happ/src/main/java/com/example/todoschedule/data/model/CourseWithNodes.ktW Vapp/src/main/java/com/example/todoschedule/data/model/OrdinaryScheduleWithTimeSlots.ktR Qapp/src/main/java/com/example/todoschedule/data/repository/TableRepositoryImpl.ktH Gapp/src/main/java/com/example/todoschedule/data/sync/CrdtKeyResolver.ktN Mapp/src/main/java/com/example/todoschedule/data/sync/adapter/CourseAdapter.ktR Qapp/src/main/java/com/example/todoschedule/data/sync/adapter/CourseNodeAdapter.ktX Wapp/src/main/java/com/example/todoschedule/data/sync/adapter/OrdinaryScheduleAdapter.ktM Lapp/src/main/java/com/example/todoschedule/data/sync/adapter/TableAdapter.ktL Kapp/src/main/java/com/example/todoschedule/data/database/entity/Syncable.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktH Gapp/src/main/java/com/example/todoschedule/data/sync/DeviceIdManager.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktH Gapp/src/main/java/com/example/todoschedule/data/database/AppDatabase.ktJ Iapp/src/main/java/com/example/todoschedule/data/database/dao/CourseDao.ktN Mapp/src/main/java/com/example/todoschedule/data/database/dao/CourseNodeDao.ktT Sapp/src/main/java/com/example/todoschedule/data/database/dao/OrdinaryScheduleDao.ktI Happ/src/main/java/com/example/todoschedule/data/database/dao/TableDao.ktP Oapp/src/main/java/com/example/todoschedule/data/database/entity/CourseEntity.ktT Sapp/src/main/java/com/example/todoschedule/data/database/entity/CourseNodeEntity.ktZ Yapp/src/main/java/com/example/todoschedule/data/database/entity/OrdinaryScheduleEntity.ktO Napp/src/main/java/com/example/todoschedule/data/database/entity/TableEntity.ktY Xapp/src/main/java/com/example/todoschedule/data/database/entity/TableTimeConfigEntity.ktG Fapp/src/main/java/com/example/todoschedule/data/mapper/CourseMapper.ktR Qapp/src/main/java/com/example/todoschedule/data/mapper/OrdinaryScheduleMappper.ktI Happ/src/main/java/com/example/todoschedule/data/model/CourseWithNodes.ktW Vapp/src/main/java/com/example/todoschedule/data/model/OrdinaryScheduleWithTimeSlots.ktR Qapp/src/main/java/com/example/todoschedule/data/repository/TableRepositoryImpl.ktH Gapp/src/main/java/com/example/todoschedule/data/sync/CrdtKeyResolver.ktN Mapp/src/main/java/com/example/todoschedule/data/sync/adapter/CourseAdapter.ktR Qapp/src/main/java/com/example/todoschedule/data/sync/adapter/CourseNodeAdapter.ktX Wapp/src/main/java/com/example/todoschedule/data/sync/adapter/OrdinaryScheduleAdapter.ktM Lapp/src/main/java/com/example/todoschedule/data/sync/adapter/TableAdapter.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktH Gapp/src/main/java/com/example/todoschedule/data/sync/CrdtKeyResolver.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktF Eapp/src/main/java/com/example/todoschedule/TodoScheduleApplication.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncService.ktE Dapp/src/main/java/com/example/todoschedule/ui/auth/LoginViewModel.ktF Eapp/src/main/java/com/example/todoschedule/TodoScheduleApplication.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncService.ktC Bapp/src/main/java/com/example/todoschedule/data/sync/SyncWorker.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktK Japp/src/main/java/com/example/todoschedule/data/sync/dto/SyncMessageDto.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/api/SyncApi.ktK Japp/src/main/java/com/example/todoschedule/data/sync/dto/SyncMessageDto.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/api/SyncApi.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/api/SyncApi.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncApiImpl.ktK Japp/src/main/java/com/example/todoschedule/data/sync/dto/SyncMessageDto.ktU Tapp/src/main/java/com/example/todoschedule/data/database/entity/SyncMessageEntity.ktM Lapp/src/main/java/com/example/todoschedule/data/repository/SyncRepository.kt@ ?app/src/main/java/com/example/todoschedule/data/sync/SyncApi.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktJ Iapp/src/main/java/com/example/todoschedule/data/sync/entity/SyncEntity.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/api/SyncApi.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncApiImpl.ktJ Iapp/src/main/java/com/example/todoschedule/core/constants/AppConstants.ktO Napp/src/main/java/com/example/todoschedule/data/database/dao/SyncMessageDao.ktU Tapp/src/main/java/com/example/todoschedule/data/database/entity/SyncMessageEntity.ktX Wapp/src/main/java/com/example/todoschedule/data/database/migration/DatabaseMigration.ktM Lapp/src/main/java/com/example/todoschedule/data/repository/SyncRepository.ktW Vapp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryExtensions.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.ktL Kapp/src/main/java/com/example/todoschedule/data/sync/SyncMessageUploader.ktK Japp/src/main/java/com/example/todoschedule/data/sync/dto/SyncMessageDto.kt@ ?app/src/main/java/com/example/todoschedule/di/DatabaseModule.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktF Eapp/src/main/java/com/example/todoschedule/di/SyncModuleExtensions.kt@ ?app/src/main/java/com/example/todoschedule/util/NetworkUtils.ktH Gapp/src/main/java/com/example/todoschedule/data/database/AppDatabase.ktF Eapp/src/main/java/com/example/todoschedule/data/remote/api/SyncApi.kt@ ?app/src/main/java/com/example/todoschedule/data/sync/SyncApi.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncApiImpl.ktJ Iapp/src/main/java/com/example/todoschedule/data/sync/entity/SyncEntity.ktQ Papp/src/main/java/com/example/todoschedule/data/repository/SyncRepositoryImpl.ktD Capp/src/main/java/com/example/todoschedule/data/sync/SyncManager.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.kt_ ^app/src/main/java/com/example/todoschedule/data/database/entity/SyncMessageEntityExtensions.kt_ ^app/src/main/java/com/example/todoschedule/data/database/entity/SyncMessageEntityExtensions.kt< ;app/src/main/java/com/example/todoschedule/di/SyncModule.ktJ Iapp/src/main/java/com/example/todoschedule/data/sync/entity/SyncEntity.ktJ Iapp/src/main/java/com/example/todoschedule/core/constants/AppConstants.kt