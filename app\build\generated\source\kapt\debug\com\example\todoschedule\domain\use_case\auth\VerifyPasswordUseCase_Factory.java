// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.domain.use_case.auth;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VerifyPasswordUseCase_Factory implements Factory<VerifyPasswordUseCase> {
  @Override
  public VerifyPasswordUseCase get() {
    return newInstance();
  }

  public static VerifyPasswordUseCase_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static VerifyPasswordUseCase newInstance() {
    return new VerifyPasswordUseCase();
  }

  private static final class InstanceHolder {
    private static final VerifyPasswordUseCase_Factory INSTANCE = new VerifyPasswordUseCase_Factory();
  }
}
