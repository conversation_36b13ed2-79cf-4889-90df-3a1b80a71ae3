// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.data.repository;

import com.example.todoschedule.data.database.dao.TableTimeConfigDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TableTimeConfigRepositoryImpl_Factory implements Factory<TableTimeConfigRepositoryImpl> {
  private final Provider<TableTimeConfigDao> tableTimeConfigDaoProvider;

  public TableTimeConfigRepositoryImpl_Factory(
      Provider<TableTimeConfigDao> tableTimeConfigDaoProvider) {
    this.tableTimeConfigDaoProvider = tableTimeConfigDaoProvider;
  }

  @Override
  public TableTimeConfigRepositoryImpl get() {
    return newInstance(tableTimeConfigDaoProvider.get());
  }

  public static TableTimeConfigRepositoryImpl_Factory create(
      Provider<TableTimeConfigDao> tableTimeConfigDaoProvider) {
    return new TableTimeConfigRepositoryImpl_Factory(tableTimeConfigDaoProvider);
  }

  public static TableTimeConfigRepositoryImpl newInstance(TableTimeConfigDao tableTimeConfigDao) {
    return new TableTimeConfigRepositoryImpl(tableTimeConfigDao);
  }
}
