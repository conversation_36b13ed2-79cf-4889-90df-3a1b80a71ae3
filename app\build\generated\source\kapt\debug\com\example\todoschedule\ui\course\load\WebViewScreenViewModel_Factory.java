// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.todoschedule.ui.course.load;

import com.example.todoschedule.domain.repository.CourseRepository;
import com.example.todoschedule.domain.repository.GlobalSettingRepository;
import com.example.todoschedule.domain.repository.TableRepository;
import com.example.todoschedule.domain.use_case.auth.GetLoginUserIdFlowUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WebViewScreenViewModel_Factory implements Factory<WebViewScreenViewModel> {
  private final Provider<CourseRepository> courseRepositoryProvider;

  private final Provider<TableRepository> tableRepositoryProvider;

  private final Provider<GlobalSettingRepository> globalSettingRepositoryProvider;

  private final Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider;

  public WebViewScreenViewModel_Factory(Provider<CourseRepository> courseRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider) {
    this.courseRepositoryProvider = courseRepositoryProvider;
    this.tableRepositoryProvider = tableRepositoryProvider;
    this.globalSettingRepositoryProvider = globalSettingRepositoryProvider;
    this.getLoginUserIdFlowUseCaseProvider = getLoginUserIdFlowUseCaseProvider;
  }

  @Override
  public WebViewScreenViewModel get() {
    return newInstance(courseRepositoryProvider.get(), tableRepositoryProvider.get(), globalSettingRepositoryProvider.get(), getLoginUserIdFlowUseCaseProvider.get());
  }

  public static WebViewScreenViewModel_Factory create(
      Provider<CourseRepository> courseRepositoryProvider,
      Provider<TableRepository> tableRepositoryProvider,
      Provider<GlobalSettingRepository> globalSettingRepositoryProvider,
      Provider<GetLoginUserIdFlowUseCase> getLoginUserIdFlowUseCaseProvider) {
    return new WebViewScreenViewModel_Factory(courseRepositoryProvider, tableRepositoryProvider, globalSettingRepositoryProvider, getLoginUserIdFlowUseCaseProvider);
  }

  public static WebViewScreenViewModel newInstance(CourseRepository courseRepository,
      TableRepository tableRepository, GlobalSettingRepository globalSettingRepository,
      GetLoginUserIdFlowUseCase getLoginUserIdFlowUseCase) {
    return new WebViewScreenViewModel(courseRepository, tableRepository, globalSettingRepository, getLoginUserIdFlowUseCase);
  }
}
